#!/usr/bin/env python3
"""
Simple Frontend Testing Suite for Agno WorkSphere
Tests basic frontend functionality without Unicode issues
"""
import requests
import time
import json

class SimpleFrontendTester:
    def __init__(self):
        self.results = []
        self.base_url = "http://localhost:3000"
        
    def log_result(self, test_name, status, details=None):
        """Log test result"""
        result = {
            "test": test_name,
            "status": status,
            "details": details,
            "timestamp": time.time()
        }
        self.results.append(result)
        print(f"[{status}] {test_name}")
        if details:
            print(f"    {details}")
    
    def test_frontend_accessibility(self):
        """Test if frontend is accessible"""
        print("\n--- Testing Frontend Accessibility ---")
        
        try:
            response = requests.get(self.base_url, timeout=10)
            if response.status_code == 200:
                self.log_result("Frontend Server Access", "PASS", f"Status: {response.status_code}")
                
                # Check if it's a React app
                content = response.text
                if "react" in content.lower() or "root" in content:
                    self.log_result("React App Detection", "PASS", "React app detected")
                else:
                    self.log_result("React App Detection", "FAIL", "React app not detected")
                
                # Check for basic HTML structure
                if "<html" in content and "<body" in content:
                    self.log_result("HTML Structure", "PASS", "Valid HTML structure")
                else:
                    self.log_result("HTML Structure", "FAIL", "Invalid HTML structure")
                
            else:
                self.log_result("Frontend Server Access", "FAIL", f"Status: {response.status_code}")
                
        except Exception as e:
            self.log_result("Frontend Server Access", "FAIL", str(e))
    
    def test_static_resources(self):
        """Test if static resources are accessible"""
        print("\n--- Testing Static Resources ---")
        
        # Common static resource paths
        static_paths = [
            "/static/css/",
            "/static/js/",
            "/favicon.ico",
            "/manifest.json"
        ]
        
        for path in static_paths:
            try:
                response = requests.get(f"{self.base_url}{path}", timeout=5)
                if response.status_code in [200, 304]:  # 304 is Not Modified, which is OK
                    self.log_result(f"Static Resource: {path}", "PASS", f"Status: {response.status_code}")
                elif response.status_code == 404:
                    self.log_result(f"Static Resource: {path}", "SKIP", "Not found (may not exist)")
                else:
                    self.log_result(f"Static Resource: {path}", "FAIL", f"Status: {response.status_code}")
            except Exception as e:
                self.log_result(f"Static Resource: {path}", "FAIL", str(e))
    
    def test_api_integration(self):
        """Test if frontend can communicate with backend"""
        print("\n--- Testing API Integration ---")
        
        # Test if frontend can reach backend
        try:
            # This simulates what the frontend would do
            response = requests.get("http://localhost:3001/api/v1/auth/login", 
                                  headers={"Content-Type": "application/json"})
            
            # We expect this to fail with 405 (Method Not Allowed) since we're using GET instead of POST
            # But if it returns 405, it means the endpoint exists
            if response.status_code == 405:
                self.log_result("Backend API Reachability", "PASS", "Backend API is reachable")
            elif response.status_code == 404:
                self.log_result("Backend API Reachability", "FAIL", "Backend API endpoint not found")
            else:
                self.log_result("Backend API Reachability", "PASS", f"Backend responded with {response.status_code}")
                
        except Exception as e:
            self.log_result("Backend API Reachability", "FAIL", str(e))
    
    def test_cors_configuration(self):
        """Test CORS configuration"""
        print("\n--- Testing CORS Configuration ---")
        
        try:
            # Test preflight request
            headers = {
                "Origin": "http://localhost:3000",
                "Access-Control-Request-Method": "POST",
                "Access-Control-Request-Headers": "Content-Type"
            }
            
            response = requests.options("http://localhost:3001/api/v1/auth/login", headers=headers)
            
            if response.status_code in [200, 204]:
                cors_headers = response.headers
                if "Access-Control-Allow-Origin" in cors_headers:
                    self.log_result("CORS Configuration", "PASS", "CORS headers present")
                else:
                    self.log_result("CORS Configuration", "FAIL", "CORS headers missing")
            else:
                self.log_result("CORS Configuration", "FAIL", f"Preflight failed: {response.status_code}")
                
        except Exception as e:
            self.log_result("CORS Configuration", "FAIL", str(e))
    
    def test_performance_basic(self):
        """Test basic performance metrics"""
        print("\n--- Testing Basic Performance ---")
        
        try:
            start_time = time.time()
            response = requests.get(self.base_url, timeout=30)
            load_time = time.time() - start_time
            
            if response.status_code == 200:
                if load_time < 5.0:
                    self.log_result("Page Load Time", "PASS", f"Loaded in {load_time:.2f}s")
                elif load_time < 10.0:
                    self.log_result("Page Load Time", "WARN", f"Slow load: {load_time:.2f}s")
                else:
                    self.log_result("Page Load Time", "FAIL", f"Too slow: {load_time:.2f}s")
                
                # Check response size
                content_length = len(response.content)
                if content_length < 1024 * 1024:  # Less than 1MB
                    self.log_result("Response Size", "PASS", f"Size: {content_length} bytes")
                else:
                    self.log_result("Response Size", "WARN", f"Large response: {content_length} bytes")
            else:
                self.log_result("Page Load Time", "FAIL", f"Failed to load: {response.status_code}")
                
        except Exception as e:
            self.log_result("Page Load Time", "FAIL", str(e))
    
    def run_all_tests(self):
        """Run all frontend tests"""
        print("AGNO WORKSPHERE - FRONTEND TESTING SUITE")
        print("=" * 50)
        
        start_time = time.time()
        
        try:
            self.test_frontend_accessibility()
            self.test_static_resources()
            self.test_api_integration()
            self.test_cors_configuration()
            self.test_performance_basic()
            
        except Exception as e:
            print(f"\nFrontend test suite crashed: {e}")
            import traceback
            traceback.print_exc()
        
        total_time = time.time() - start_time
        self.print_summary(total_time)
    
    def print_summary(self, total_time):
        """Print test summary"""
        print("\n" + "=" * 50)
        print("FRONTEND TEST SUMMARY")
        print("=" * 50)
        
        total_tests = len(self.results)
        passed_tests = len([r for r in self.results if r["status"] == "PASS"])
        failed_tests = len([r for r in self.results if r["status"] == "FAIL"])
        skipped_tests = len([r for r in self.results if r["status"] == "SKIP"])
        warned_tests = len([r for r in self.results if r["status"] == "WARN"])
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Warnings: {warned_tests}")
        print(f"Skipped: {skipped_tests}")
        print(f"Total Time: {total_time:.2f}s")
        print(f"Success Rate: {(passed_tests/total_tests*100):.1f}%" if total_tests > 0 else "N/A")
        
        if failed_tests > 0:
            print(f"\nFAILED TESTS:")
            for result in self.results:
                if result["status"] == "FAIL":
                    print(f"  - {result['test']}: {result['details']}")
        
        if warned_tests > 0:
            print(f"\nWARNINGS:")
            for result in self.results:
                if result["status"] == "WARN":
                    print(f"  - {result['test']}: {result['details']}")
        
        print(f"\nTest Environment:")
        print(f"  Frontend URL: {self.base_url}")
        
        # Save results
        filename = f"simple_frontend_test_results_{int(time.time())}.json"
        with open(filename, 'w') as f:
            json.dump({
                "timestamp": time.time(),
                "total_time": total_time,
                "summary": {
                    "total": total_tests,
                    "passed": passed_tests,
                    "failed": failed_tests,
                    "warned": warned_tests,
                    "skipped": skipped_tests
                },
                "results": self.results
            }, f, indent=2)
        
        print(f"\nResults saved to: {filename}")

if __name__ == "__main__":
    tester = SimpleFrontendTester()
    tester.run_all_tests()
