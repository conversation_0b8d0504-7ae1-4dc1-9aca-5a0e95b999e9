// Simple script to check authentication state
console.log('=== Authentication State Check ===');

// Check localStorage
console.log('Access Token:', localStorage.getItem('accessToken') ? 'Present' : 'Missing');
console.log('Current User:', localStorage.getItem('currentUser'));
console.log('Organization ID:', localStorage.getItem('organizationId'));
console.log('User Role:', localStorage.getItem('userRole'));

// Test API call
async function testAPI() {
    const token = localStorage.getItem('accessToken');
    if (!token) {
        console.log('❌ No token found');
        return;
    }
    
    try {
        const response = await fetch('http://localhost:3001/api/v1/users/me', {
            headers: {
                'Authorization': `Bearer ${token}`
            }
        });
        
        if (response.ok) {
            const data = await response.json();
            console.log('✅ API call successful');
            console.log('User data:', data.data.user);
            console.log('Organizations:', data.data.organizations);
            
            if (data.data.organizations && data.data.organizations.length > 0) {
                const org = data.data.organizations[0];
                console.log('First organization:', org);
                console.log('Organization name:', org.organization ? org.organization.name : org.name);
            }
        } else {
            console.log('❌ API call failed:', response.status);
        }
    } catch (error) {
        console.log('❌ API error:', error.message);
    }
}

testAPI();
