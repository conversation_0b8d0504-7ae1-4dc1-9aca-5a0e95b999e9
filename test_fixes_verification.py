#!/usr/bin/env python3
"""
Verification script for the fixes implemented in Agno WorkSphere
Tests the key functionality that was reported as broken
"""

import asyncio
import aiohttp
import json
from datetime import datetime

# API Configuration
API_BASE_URL = "http://localhost:3001"
TEST_EMAIL = "<EMAIL>"
TEST_PASSWORD = "password123"

class FixVerificationTester:
    def __init__(self):
        self.session = None
        self.auth_token = None
        self.organization_id = None
        self.project_id = None

    async def setup_session(self):
        """Setup HTTP session"""
        self.session = aiohttp.ClientSession()

    async def cleanup_session(self):
        """Cleanup HTTP session"""
        if self.session:
            await self.session.close()

    async def login(self):
        """Login to get auth token"""
        print("🔐 Testing login...")
        
        login_data = {
            "email": TEST_EMAIL,
            "password": TEST_PASSWORD
        }
        
        async with self.session.post(
            f"{API_BASE_URL}/api/v1/auth/login",
            json=login_data
        ) as response:
            if response.status == 200:
                data = await response.json()
                self.auth_token = data["data"]["access_token"]
                print("   ✅ Login successful")
                return True
            else:
                print(f"   ❌ Login failed: {response.status}")
                return False

    def get_headers(self):
        """Get headers with auth token"""
        return {
            "Authorization": f"Bearer {self.auth_token}",
            "Content-Type": "application/json"
        }

    async def test_project_creation(self):
        """Test project creation and board/column creation"""
        print("\n📁 Testing project creation with automatic board setup...")
        
        project_data = {
            "name": f"Test Project {datetime.now().strftime('%H%M%S')}",
            "description": "Test project for verification",
            "organization_id": "demo-org-1"
        }
        
        async with self.session.post(
            f"{API_BASE_URL}/api/v1/projects",
            json=project_data,
            headers=self.get_headers()
        ) as response:
            if response.status == 200:
                data = await response.json()
                self.project_id = data["data"]["id"]
                print(f"   ✅ Project created: {data['data']['name']}")
                
                # Test if boards were created automatically
                await self.test_automatic_board_creation()
                return True
            else:
                print(f"   ❌ Project creation failed: {response.status}")
                return False

    async def test_automatic_board_creation(self):
        """Test if boards and columns were created automatically"""
        print("   🏗️  Testing automatic board and column creation...")
        
        # Check if boards were created
        async with self.session.get(
            f"{API_BASE_URL}/api/v1/boards?project_id={self.project_id}",
            headers=self.get_headers()
        ) as response:
            if response.status == 200:
                data = await response.json()
                boards = data.get("data", [])
                if boards:
                    board_id = boards[0]["id"]
                    print(f"      ✅ Board created automatically: {boards[0]['name']}")
                    
                    # Check if columns were created
                    await self.test_automatic_column_creation(board_id)
                else:
                    print("      ❌ No boards found")
            else:
                print(f"      ❌ Failed to get boards: {response.status}")

    async def test_automatic_column_creation(self, board_id):
        """Test if columns were created automatically"""
        async with self.session.get(
            f"{API_BASE_URL}/api/v1/columns?board_id={board_id}",
            headers=self.get_headers()
        ) as response:
            if response.status == 200:
                data = await response.json()
                columns = data.get("data", [])
                if columns:
                    print(f"      ✅ {len(columns)} columns created automatically")
                    for col in columns:
                        print(f"         • {col['title']} ({col['status']})")
                else:
                    print("      ❌ No columns found")
            else:
                print(f"      ❌ Failed to get columns: {response.status}")

    async def test_domain_validation(self):
        """Test domain validation for invitations"""
        print("\n🌐 Testing domain validation for invitations...")
        
        # Get organization details to check allowed domains
        async with self.session.get(
            f"{API_BASE_URL}/api/v1/organizations/demo-org-1",
            headers=self.get_headers()
        ) as response:
            if response.status == 200:
                data = await response.json()
                org = data["data"]
                allowed_domains = org.get("allowed_domains", [])
                print(f"   📋 Organization allowed domains: {allowed_domains}")
                
                # Test valid domain invitation
                await self.test_valid_domain_invitation(allowed_domains)
                
                # Test invalid domain invitation
                await self.test_invalid_domain_invitation()
            else:
                print(f"   ❌ Failed to get organization: {response.status}")

    async def test_valid_domain_invitation(self, allowed_domains):
        """Test invitation with valid domain"""
        if not allowed_domains:
            print("   ⚠️  No domain restrictions set, skipping domain validation test")
            return
            
        valid_email = f"test@{allowed_domains[0]}"
        print(f"   ✅ Testing valid domain invitation: {valid_email}")
        
        invite_data = {
            "email": valid_email,
            "role": "member"
        }
        
        async with self.session.post(
            f"{API_BASE_URL}/api/v1/organizations/demo-org-1/invite",
            json=invite_data,
            headers=self.get_headers()
        ) as response:
            if response.status == 200:
                print("      ✅ Valid domain invitation accepted")
            else:
                print(f"      ❌ Valid domain invitation failed: {response.status}")

    async def test_invalid_domain_invitation(self):
        """Test invitation with invalid domain"""
        invalid_email = "<EMAIL>"
        print(f"   🚫 Testing invalid domain invitation: {invalid_email}")
        
        invite_data = {
            "email": invalid_email,
            "role": "member"
        }
        
        async with self.session.post(
            f"{API_BASE_URL}/api/v1/organizations/demo-org-1/invite",
            json=invite_data,
            headers=self.get_headers()
        ) as response:
            if response.status == 403:
                print("      ✅ Invalid domain invitation properly rejected")
            elif response.status == 200:
                print("      ⚠️  Invalid domain invitation was accepted (no domain restrictions)")
            else:
                print(f"      ❌ Unexpected response: {response.status}")

    async def test_email_service(self):
        """Test email service functionality"""
        print("\n📧 Testing email service...")
        
        # Send a test invitation to trigger email
        invite_data = {
            "email": "<EMAIL>",
            "role": "member"
        }
        
        async with self.session.post(
            f"{API_BASE_URL}/api/v1/organizations/demo-org-1/invite",
            json=invite_data,
            headers=self.get_headers()
        ) as response:
            if response.status in [200, 403]:  # 403 is expected for domain validation
                print("   ✅ Email service is functioning (check server logs for email output)")
            else:
                print(f"   ❌ Email service test failed: {response.status}")

    async def test_task_creation_api(self):
        """Test task creation API"""
        print("\n📝 Testing task creation API...")
        
        if not self.project_id:
            print("   ⚠️  No project available for task creation test")
            return
            
        # Get boards for the project
        async with self.session.get(
            f"{API_BASE_URL}/api/v1/boards?project_id={self.project_id}",
            headers=self.get_headers()
        ) as response:
            if response.status == 200:
                data = await response.json()
                boards = data.get("data", [])
                if boards:
                    board_id = boards[0]["id"]
                    await self.test_task_creation_with_board(board_id)
                else:
                    print("   ❌ No boards found for task creation")
            else:
                print(f"   ❌ Failed to get boards: {response.status}")

    async def test_task_creation_with_board(self, board_id):
        """Test task creation with specific board"""
        # Get columns for the board
        async with self.session.get(
            f"{API_BASE_URL}/api/v1/columns?board_id={board_id}",
            headers=self.get_headers()
        ) as response:
            if response.status == 200:
                data = await response.json()
                columns = data.get("data", [])
                if columns:
                    column_id = columns[0]["id"]
                    
                    # Create a test task
                    task_data = {
                        "title": f"Test Task {datetime.now().strftime('%H%M%S')}",
                        "description": "Test task for verification",
                        "column_id": column_id,
                        "priority": "medium",
                        "position": 0
                    }
                    
                    async with self.session.post(
                        f"{API_BASE_URL}/api/v1/cards",
                        json=task_data,
                        headers=self.get_headers()
                    ) as task_response:
                        if task_response.status == 200:
                            task_data = await task_response.json()
                            print(f"   ✅ Task created successfully: {task_data['data']['title']}")
                        else:
                            print(f"   ❌ Task creation failed: {task_response.status}")
                else:
                    print("   ❌ No columns found for task creation")
            else:
                print(f"   ❌ Failed to get columns: {response.status}")

    async def run_all_tests(self):
        """Run all verification tests"""
        print("🚀 Starting Agno WorkSphere Fix Verification Tests")
        print("=" * 60)
        
        await self.setup_session()
        
        try:
            # Login first
            if not await self.login():
                return
            
            # Run all tests
            await self.test_project_creation()
            await self.test_domain_validation()
            await self.test_email_service()
            await self.test_task_creation_api()
            
            print("\n" + "=" * 60)
            print("✅ Fix verification tests completed!")
            print("\n📋 Summary of fixes verified:")
            print("   • Project creation with automatic board/column setup")
            print("   • Domain validation for invitations")
            print("   • Email service functionality")
            print("   • Task creation API")
            print("\n💡 Note: Check the backend server logs for email output")
            
        except Exception as e:
            print(f"\n❌ Test execution failed: {e}")
        finally:
            await self.cleanup_session()

async def main():
    """Main function"""
    tester = FixVerificationTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
