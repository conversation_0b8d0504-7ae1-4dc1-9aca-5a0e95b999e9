"""
Authentication endpoints
"""
from datetime import datetime, timed<PERSON>ta
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
import pyotp
import qrcode
import io
import base64

from app.core.database import get_db
from app.core.security import (
    hash_password, verify_password, create_access_token, create_refresh_token,
    verify_token, generate_2fa_secret, verify_2fa_token,
    generate_email_verification_token, generate_password_reset_token
)
from app.core.exceptions import (
    AuthenticationError, ValidationError, DuplicateResourceError, ResourceNotFoundError
)
from app.models.user import User
from app.schemas.auth import (
    UserRegister, UserLogin, AuthResponse, TokenResponse, UserResponse,
    RefreshTokenRequest, ForgotPasswordRequest, ResetPasswordRequest,
    VerifyEmailRequest, Enable2FAResponse, Verify2FARequest, ChangePasswordRequest
)
from app.core.deps import get_current_active_user
from app.config import settings

router = APIRouter()


@router.post("/register", response_model=AuthResponse)
async def register(
    user_data: UserRegister,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db)
):
    """Register a new user"""
    # Check if user already exists
    result = await db.execute(select(User).where(User.email == user_data.email))
    if result.scalar_one_or_none():
        raise DuplicateResourceError("User with this email already exists")
    
    # Create new user
    hashed_password = hash_password(user_data.password)
    user = User(
        email=user_data.email,
        password_hash=hashed_password,
        first_name=user_data.first_name,
        last_name=user_data.last_name,
        email_verified=False  # Will be verified via email
    )
    
    db.add(user)
    await db.commit()
    await db.refresh(user)
    
    # Generate tokens
    access_token = create_access_token({"sub": str(user.id)})
    refresh_token = create_refresh_token({"sub": str(user.id)})
    
    # TODO: Send verification email in background
    # background_tasks.add_task(send_verification_email, user.email, verification_token)
    
    return AuthResponse(
        user=UserResponse.from_orm(user),
        tokens=TokenResponse(
            access_token=access_token,
            refresh_token=refresh_token,
            expires_in=settings.jwt_expires_in * 60
        )
    )


@router.post("/login", response_model=AuthResponse)
async def login(
    user_data: UserLogin,
    db: AsyncSession = Depends(get_db)
):
    """Login user"""
    # Find user by email
    result = await db.execute(select(User).where(User.email == user_data.email))
    user = result.scalar_one_or_none()
    
    if not user or not verify_password(user_data.password, user.password_hash):
        raise AuthenticationError("Invalid email or password")
    
    # Update last login
    user.last_login_at = datetime.utcnow()
    await db.commit()
    
    # Generate tokens
    access_token = create_access_token({"sub": str(user.id)})
    refresh_token = create_refresh_token({"sub": str(user.id)})
    
    return AuthResponse(
        user=UserResponse.from_orm(user),
        tokens=TokenResponse(
            access_token=access_token,
            refresh_token=refresh_token,
            expires_in=settings.jwt_expires_in * 60
        )
    )


@router.post("/refresh-token", response_model=TokenResponse)
async def refresh_token(
    token_data: RefreshTokenRequest,
    db: AsyncSession = Depends(get_db)
):
    """Refresh access token"""
    try:
        payload = verify_token(token_data.refresh_token, token_type="refresh")
        user_id = payload.get("sub")
        
        # Verify user still exists
        result = await db.execute(select(User).where(User.id == user_id))
        user = result.scalar_one_or_none()
        if not user:
            raise AuthenticationError("User not found")
        
        # Generate new tokens
        access_token = create_access_token({"sub": str(user.id)})
        new_refresh_token = create_refresh_token({"sub": str(user.id)})
        
        return TokenResponse(
            access_token=access_token,
            refresh_token=new_refresh_token,
            expires_in=settings.jwt_expires_in * 60
        )
        
    except Exception as e:
        raise AuthenticationError("Invalid refresh token")


@router.post("/logout")
async def logout():
    """Logout user (client should discard tokens)"""
    return {"success": True, "message": "Logged out successfully"}


@router.post("/forgot-password")
async def forgot_password(
    request_data: ForgotPasswordRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db)
):
    """Request password reset"""
    result = await db.execute(select(User).where(User.email == request_data.email))
    user = result.scalar_one_or_none()
    
    if user:
        reset_token = generate_password_reset_token()
        # TODO: Store reset token in database with expiration
        # TODO: Send reset email in background
        # background_tasks.add_task(send_password_reset_email, user.email, reset_token)
    
    # Always return success to prevent email enumeration
    return {"success": True, "message": "If the email exists, a reset link has been sent"}


@router.post("/reset-password")
async def reset_password(
    request_data: ResetPasswordRequest,
    db: AsyncSession = Depends(get_db)
):
    """Reset password with token"""
    # TODO: Verify reset token from database
    # For now, just return success
    return {"success": True, "message": "Password reset successfully"}


@router.post("/verify-email")
async def verify_email(
    request_data: VerifyEmailRequest,
    db: AsyncSession = Depends(get_db)
):
    """Verify email address"""
    # TODO: Verify email token from database
    # For now, just return success
    return {"success": True, "message": "Email verified successfully"}


@router.post("/enable-2fa", response_model=Enable2FAResponse)
async def enable_2fa(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Enable two-factor authentication"""
    if current_user.two_factor_enabled:
        raise ValidationError("Two-factor authentication is already enabled")

    # Generate secret
    secret = generate_2fa_secret()

    # Generate QR code
    totp = pyotp.TOTP(secret)
    qr_url = totp.provisioning_uri(
        name=current_user.email,
        issuer_name=settings.app_name
    )

    # Create QR code image
    qr = qrcode.QRCode(version=1, box_size=10, border=5)
    qr.add_data(qr_url)
    qr.make(fit=True)

    img = qr.make_image(fill_color="black", back_color="white")
    img_buffer = io.BytesIO()
    img.save(img_buffer, format='PNG')
    img_str = base64.b64encode(img_buffer.getvalue()).decode()
    qr_code_url = f"data:image/png;base64,{img_str}"

    # Store secret (not enabled until verified)
    current_user.two_factor_secret = secret
    await db.commit()

    return Enable2FAResponse(
        secret=secret,
        qr_code_url=qr_code_url
    )


@router.post("/verify-2fa")
async def verify_2fa(
    request_data: Verify2FARequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Verify and enable 2FA"""
    if not current_user.two_factor_secret:
        raise ValidationError("Two-factor authentication setup not started")

    if not verify_2fa_token(current_user.two_factor_secret, request_data.token):
        raise ValidationError("Invalid 2FA token")

    # Enable 2FA
    current_user.two_factor_enabled = True
    await db.commit()

    return {"success": True, "message": "Two-factor authentication enabled successfully"}


@router.post("/change-password")
async def change_password(
    request_data: ChangePasswordRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Change user password"""
    if not verify_password(request_data.current_password, current_user.password_hash):
        raise AuthenticationError("Current password is incorrect")

    # Update password
    current_user.password_hash = hash_password(request_data.new_password)
    await db.commit()

    return {"success": True, "message": "Password changed successfully"}
