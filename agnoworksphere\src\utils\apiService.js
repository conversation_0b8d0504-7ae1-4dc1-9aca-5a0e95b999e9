// src/utils/apiService.js - Real Backend Integration

// Simulate API delay
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Helper function to get headers with authentication (not used in mock mode)
// const getAuthHeaders = (organizationId = null) => {
//   const token = localStorage.getItem('accessToken');
//   const headers = {
//     'Content-Type': 'application/json',
//     ...(organizationId && { 'X-Organization-ID': organizationId })
//   };

//   if (token) {
//     headers['Authorization'] = `Bearer ${token}`;
//   }

//   return headers;
// };

// Helper function to handle API responses (not used in mock mode)
// const handleResponse = async (response) => {
//   const result = await response.json();

//   if (!response.ok) {
//     throw new Error(result.error?.message || result.message || 'API request failed');
//   }

//   return result;
// };

const apiService = {
  // Organizations (Use organizationService.js instead)
  // organizations: {
  //   // Commented out - use organizationService.js for organization operations
  // },

    // All organization methods commented out - use organizationService.js instead

  // Projects (Real Backend Integration)
  projects: {
    getAll: async (organizationId) => {
      try {
        // Use real API service
        const realApiService = (await import('./realApiService')).default;
        const result = await realApiService.projects.getAll(organizationId);
        console.log('Projects loaded successfully:', result);
        return result || [];
      } catch (error) {
        console.error('Failed to fetch projects:', error);
        return []; // Return empty array instead of mock data
      }
    },

    create: async (organizationId, projectData) => {
      try {
        // Use real API service
        const realApiService = (await import('./realApiService')).default;
        const result = await realApiService.projects.create(organizationId, projectData);
        console.log('Project created successfully:', result);
        return result;
      } catch (error) {
        console.error('Failed to create project:', error);
        throw error; // Don't use mock fallback, let the error bubble up
      }
    },

    getById: async (id) => {
      try {
        // Use real API service
        const realApiService = (await import('./realApiService')).default;
        return await realApiService.projects.getById(id);
      } catch (error) {
        console.error('Failed to fetch project:', error);
        throw error; // Don't use mock fallback
      }
    },

    update: async (id, updateData) => {
      try {
        // Use real API service
        const realApiService = (await import('./realApiService')).default;
        return await realApiService.projects.update(id, updateData);
      } catch (error) {
        console.error('Failed to update project:', error);
        throw error; // Don't use mock fallback
      }
    },

    delete: async (id) => {
      try {
        // Use real API service
        const realApiService = (await import('./realApiService')).default;
        return await realApiService.projects.delete(id);
      } catch (error) {
        console.error('Failed to delete project:', error);
        throw error; // Don't use mock fallback
      }
    }
  },

  // Boards (MOCK - Temporarily disabled)
  boards: {
    getByProject: async (projectId) => {
      try {
        // Simulate API delay
        await delay(300);

        // Return mock boards data
        return [
          {
            id: 'board-1',
            name: 'Project Board',
            project_id: projectId,
            columns: []
          }
        ];
      } catch (error) {
        console.error('Failed to fetch boards:', error);
        throw error;
      }
    },

    getById: async (id) => {
      try {
        // Simulate API delay
        await delay(200);

        // Return mock board data
        return {
          id: id,
          name: 'Mock Board',
          project_id: 'proj-1',
          columns: []
        };
      } catch (error) {
        console.error('Failed to fetch board:', error);
        throw error;
      }
    },

    create: async (projectId, boardData) => {
      try {
        // Simulate API delay
        await delay(300);

        // Return mock created board
        return {
          id: `board-${Date.now()}`,
          ...boardData,
          project_id: projectId,
          created_at: new Date().toISOString()
        };
      } catch (error) {
        console.error('Failed to create board:', error);
        throw error;
      }
    },

    update: async (id, updateData) => {
      try {
        // Simulate API delay
        await delay(300);

        // Return mock updated board
        return {
          id: id,
          ...updateData,
          updated_at: new Date().toISOString()
        };
      } catch (error) {
        console.error('Failed to update board:', error);
        throw error;
      }
    },

    delete: async (id) => {
      try {
        // Simulate API delay
        await delay(300);

        // Return success response
        return { success: true, id: id };
      } catch (error) {
        console.error('Failed to delete board:', error);
        throw error;
      }
    }
  },

  // Users (MOCK - Temporarily disabled)
  users: {
    getProfile: async () => {
      try {
        // Simulate API delay
        await delay(200);

        // Return mock user profile
        const currentUser = localStorage.getItem('currentUser');
        if (currentUser) {
          return JSON.parse(currentUser);
        }

        return {
          id: 'user-1',
          email: '<EMAIL>',
          first_name: 'Demo',
          last_name: 'User'
        };
      } catch (error) {
        console.error('Failed to fetch user profile:', error);
        throw error;
      }
    },

    updateProfile: async (updateData) => {
      try {
        // Simulate API delay
        await delay(300);

        // Update localStorage
        const currentUser = localStorage.getItem('currentUser');
        if (currentUser) {
          const userData = JSON.parse(currentUser);
          const updatedUser = { ...userData, ...updateData };
          localStorage.setItem('currentUser', JSON.stringify(updatedUser));
          return updatedUser;
        }

        return updateData;
      } catch (error) {
        console.error('Failed to update user profile:', error);
        throw error;
      }
    },

    uploadAvatar: async (file) => {
      try {
        // Simulate API delay
        await delay(500);

        // Mock avatar upload - return a fake URL
        const avatarUrl = `https://api.dicebear.com/7.x/avataaars/svg?seed=${Date.now()}`;

        return {
          avatar_url: avatarUrl,
          success: true
        };
      } catch (error) {
        console.error('Failed to upload avatar:', error);
        throw error;
      }
    }
  },

  // Organization API methods
  organizations: {
    // Create new organization
    create: async (orgData, logoFile = null) => {
      try {
        // Import organizationService dynamically to avoid circular imports
        const { createOrganization } = await import('./organizationService');
        return await createOrganization(orgData, logoFile);
      } catch (error) {
        console.error('Failed to create organization:', error);
        throw error;
      }
    },

    // Get all organizations
    getAll: async () => {
      try {
        const { getOrganizations } = await import('./organizationService');
        return await getOrganizations();
      } catch (error) {
        console.error('Failed to get organizations:', error);
        throw error;
      }
    },

    // Get organization by ID
    getById: async (id) => {
      try {
        const { getOrganizationById } = await import('./organizationService');
        return await getOrganizationById(id);
      } catch (error) {
        console.error('Failed to get organization:', error);
        throw error;
      }
    },

    // Update organization
    update: async (id, orgData, logoFile = null) => {
      try {
        const { updateOrganization } = await import('./organizationService');
        return await updateOrganization(id, orgData, logoFile);
      } catch (error) {
        console.error('Failed to update organization:', error);
        throw error;
      }
    },

    // Upload organization logo
    uploadLogo: async (id, logoFile) => {
      try {
        const { uploadOrganizationLogo } = await import('./organizationService');
        return await uploadOrganizationLogo(id, logoFile);
      } catch (error) {
        console.error('Failed to upload organization logo:', error);
        throw error;
      }
    },

    // Delete organization logo
    deleteLogo: async (id) => {
      try {
        const { deleteOrganizationLogo } = await import('./organizationService');
        return await deleteOrganizationLogo(id);
      } catch (error) {
        console.error('Failed to delete organization logo:', error);
        throw error;
      }
    }
  },

  // Analytics methods
  getUserActivityAnalytics: async (period = '30d') => {
    await delay(500);
    return {
      totalUsers: 24,
      activeUsers: 18,
      newUsers: 3,
      userGrowth: 12.5,
      dailyActiveUsers: [
        { date: '2024-01-01', users: 15 },
        { date: '2024-01-02', users: 18 },
        { date: '2024-01-03', users: 16 },
        { date: '2024-01-04', users: 20 },
        { date: '2024-01-05', users: 22 },
        { date: '2024-01-06', users: 19 },
        { date: '2024-01-07', users: 24 }
      ]
    };
  },

  getOrganizationPerformance: async (period = '30d') => {
    await delay(500);
    return {
      totalProjects: 12,
      completedProjects: 8,
      activeProjects: 4,
      completionRate: 66.7,
      teamProductivity: 78,
      performanceMetrics: [
        { metric: 'Task Completion Rate', value: 85, trend: 'up' },
        { metric: 'Team Collaboration', value: 92, trend: 'up' },
        { metric: 'Project Delivery', value: 78, trend: 'down' },
        { metric: 'Resource Utilization', value: 88, trend: 'up' }
      ]
    };
  },

  getProjectStatistics: async (period = '30d') => {
    await delay(500);
    return {
      totalTasks: 156,
      completedTasks: 124,
      inProgressTasks: 24,
      overdueTasks: 8,
      taskCompletionRate: 79.5,
      averageTaskDuration: 3.2
    };
  },

  getUsageAnalytics: async (period = '30d') => {
    await delay(500);
    return {
      totalSessions: 342,
      averageSessionDuration: 28,
      pageViews: 1456,
      bounceRate: 23,
      mostUsedFeatures: [
        { feature: 'Kanban Board', usage: 89 },
        { feature: 'Team Chat', usage: 76 },
        { feature: 'File Sharing', usage: 65 },
        { feature: 'Time Tracking', usage: 54 }
      ]
    };
  },

  // Billing methods
  getSubscriptionDetails: async () => {
    await delay(500);
    return {
      plan: 'Professional',
      status: 'active',
      price: 29.99,
      currency: 'USD',
      billingCycle: 'monthly',
      nextBillingDate: '2024-02-15',
      seats: 25,
      usedSeats: 18
    };
  },

  getPaymentHistory: async () => {
    await delay(500);
    return [
      {
        id: 'inv_001',
        date: '2024-01-15',
        amount: 29.99,
        status: 'paid',
        description: 'Professional Plan - Monthly',
        downloadUrl: '#'
      },
      {
        id: 'inv_002',
        date: '2023-12-15',
        amount: 29.99,
        status: 'paid',
        description: 'Professional Plan - Monthly',
        downloadUrl: '#'
      }
    ];
  },

  getUsageBilling: async () => {
    await delay(500);
    return {
      currentPeriod: {
        start: '2024-01-15',
        end: '2024-02-15'
      },
      metrics: [
        { name: 'Active Users', current: 18, limit: 25, unit: 'users' },
        { name: 'Projects', current: 12, limit: 'unlimited', unit: 'projects' },
        { name: 'Storage', current: 2.4, limit: 100, unit: 'GB' },
        { name: 'API Calls', current: 1250, limit: 10000, unit: 'calls' }
      ]
    };
  },

  getAvailablePlans: async () => {
    await delay(500);
    return [
      {
        id: 'starter',
        name: 'Starter',
        price: 9.99,
        currency: 'USD',
        billingCycle: 'monthly',
        features: ['Up to 5 users', '10 projects', '5GB storage'],
        current: false
      },
      {
        id: 'professional',
        name: 'Professional',
        price: 29.99,
        currency: 'USD',
        billingCycle: 'monthly',
        features: ['Up to 25 users', 'Unlimited projects', '100GB storage'],
        current: true,
        popular: true
      },
      {
        id: 'enterprise',
        name: 'Enterprise',
        price: 99.99,
        currency: 'USD',
        billingCycle: 'monthly',
        features: ['Unlimited users', 'Unlimited projects', '1TB storage'],
        current: false
      }
    ];
  }
};

export default apiService;
