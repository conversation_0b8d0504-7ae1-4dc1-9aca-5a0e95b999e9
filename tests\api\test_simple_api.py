#!/usr/bin/env python3
"""
Simple API Testing Suite for Agno WorkSphere
Tests core API endpoints without Unicode issues
"""
import requests
import json
import time
import sys

BASE_URL = "http://localhost:3001"
API_BASE = f"{BASE_URL}/api/v1"

class SimpleAPITester:
    def __init__(self):
        self.results = []
        self.auth_token = None
        self.test_user_email = f"test_{int(time.time())}@testcompany.com"
        self.test_org_id = None
        self.test_project_id = None
        
    def log_result(self, test_name, status, details=None):
        """Log test result"""
        result = {
            "test": test_name,
            "status": status,
            "details": details,
            "timestamp": time.time()
        }
        self.results.append(result)
        print(f"[{status}] {test_name}")
        if details:
            print(f"    {details}")
    
    def make_request(self, method, endpoint, data=None, expected_status=200):
        """Make HTTP request"""
        headers = {"Content-Type": "application/json"}
        if self.auth_token:
            headers["Authorization"] = f"Bearer {self.auth_token}"
        
        full_url = f"{API_BASE}{endpoint}"
        
        try:
            if method.upper() == "GET":
                response = requests.get(full_url, headers=headers)
            elif method.upper() == "POST":
                response = requests.post(full_url, headers=headers, json=data)
            elif method.upper() == "PUT":
                response = requests.put(full_url, headers=headers, json=data)
            elif method.upper() == "DELETE":
                response = requests.delete(full_url, headers=headers)
            else:
                return False, f"Unsupported method: {method}"
            
            if response.status_code == expected_status:
                try:
                    return True, response.json()
                except:
                    return True, {"status_code": response.status_code}
            else:
                return False, f"Expected {expected_status}, got {response.status_code}: {response.text[:200]}"
                
        except Exception as e:
            return False, str(e)
    
    def test_health(self):
        """Test health endpoint"""
        print("\n--- Testing Health Endpoints ---")
        
        # Root endpoint
        success, data = self.make_request("GET", f"{BASE_URL}/")
        if success:
            self.log_result("Root Endpoint", "PASS")
        else:
            self.log_result("Root Endpoint", "FAIL", data)
        
        # Health check
        success, data = self.make_request("GET", f"{BASE_URL}/health")
        if success:
            self.log_result("Health Check", "PASS")
        else:
            self.log_result("Health Check", "FAIL", data)
    
    def test_authentication(self):
        """Test authentication endpoints"""
        print("\n--- Testing Authentication ---")
        
        # Test registration
        register_data = {
            "email": self.test_user_email,
            "password": "TestPassword123!",
            "first_name": "Test",
            "last_name": "User",
            "organization_name": "Test Organization"
        }
        
        success, data = self.make_request("POST", "/auth/register", register_data)
        if success:
            self.log_result("User Registration", "PASS")
            if data and "data" in data:
                self.auth_token = data["data"].get("tokens", {}).get("access_token")
                self.test_org_id = data["data"].get("organization", {}).get("id")
        else:
            self.log_result("User Registration", "FAIL", data)
        
        # Test login
        login_data = {
            "email": self.test_user_email,
            "password": "TestPassword123!"
        }
        
        success, data = self.make_request("POST", "/auth/login", login_data)
        if success:
            self.log_result("User Login", "PASS")
            if data and "data" in data:
                self.auth_token = data["data"].get("tokens", {}).get("access_token")
        else:
            self.log_result("User Login", "FAIL", data)
    
    def test_user_endpoints(self):
        """Test user management endpoints"""
        print("\n--- Testing User Management ---")
        
        if not self.auth_token:
            self.log_result("User Tests", "SKIP", "No auth token")
            return
        
        # Get user profile
        success, data = self.make_request("GET", "/users/profile")
        if success:
            self.log_result("Get User Profile", "PASS")
        else:
            self.log_result("Get User Profile", "FAIL", data)
        
        # Update user profile
        update_data = {
            "first_name": "Updated",
            "last_name": "User",
            "bio": "Updated bio for testing"
        }
        success, data = self.make_request("PUT", "/users/profile", update_data)
        if success:
            self.log_result("Update User Profile", "PASS")
        else:
            self.log_result("Update User Profile", "FAIL", data)
    
    def test_organization_endpoints(self):
        """Test organization management endpoints"""
        print("\n--- Testing Organization Management ---")
        
        if not self.auth_token:
            self.log_result("Organization Tests", "SKIP", "No auth token")
            return
        
        # Get organizations
        success, data = self.make_request("GET", "/organizations")
        if success:
            self.log_result("Get Organizations", "PASS")
        else:
            self.log_result("Get Organizations", "FAIL", data)
        
        # Get specific organization
        if self.test_org_id:
            success, data = self.make_request("GET", f"/organizations/{self.test_org_id}")
            if success:
                self.log_result("Get Organization Details", "PASS")
            else:
                self.log_result("Get Organization Details", "FAIL", data)
    
    def test_project_endpoints(self):
        """Test project management endpoints"""
        print("\n--- Testing Project Management ---")
        
        if not self.auth_token:
            self.log_result("Project Tests", "SKIP", "No auth token")
            return
        
        # Create project
        project_data = {
            "name": "Test Project",
            "description": "A test project for API testing",
            "organization_id": self.test_org_id
        }
        success, data = self.make_request("POST", "/projects", project_data)
        if success:
            self.log_result("Create Project", "PASS")
            if data and "data" in data:
                self.test_project_id = data["data"].get("id")
        else:
            self.log_result("Create Project", "FAIL", data)
        
        # Get projects
        success, data = self.make_request("GET", "/projects")
        if success:
            self.log_result("Get Projects", "PASS")
        else:
            self.log_result("Get Projects", "FAIL", data)
    
    def test_board_endpoints(self):
        """Test board management endpoints"""
        print("\n--- Testing Board Management ---")
        
        if not self.auth_token or not self.test_project_id:
            self.log_result("Board Tests", "SKIP", "No auth token or project")
            return
        
        # Create board
        board_data = {
            "name": "Test Board",
            "description": "A test board for API testing",
            "project_id": self.test_project_id
        }
        success, data = self.make_request("POST", "/boards", board_data)
        if success:
            self.log_result("Create Board", "PASS")
        else:
            self.log_result("Create Board", "FAIL", data)
        
        # Get boards
        success, data = self.make_request("GET", "/boards")
        if success:
            self.log_result("Get Boards", "PASS")
        else:
            self.log_result("Get Boards", "FAIL", data)
    
    def test_error_handling(self):
        """Test error handling"""
        print("\n--- Testing Error Handling ---")
        
        # Test unauthorized access
        old_token = self.auth_token
        self.auth_token = None
        
        success, data = self.make_request("GET", "/users/profile", expected_status=401)
        if success:
            self.log_result("Unauthorized Access Block", "PASS")
        else:
            self.log_result("Unauthorized Access Block", "FAIL", data)
        
        # Test with invalid token
        self.auth_token = "invalid_token"
        success, data = self.make_request("GET", "/users/profile", expected_status=401)
        if success:
            self.log_result("Invalid Token Block", "PASS")
        else:
            self.log_result("Invalid Token Block", "FAIL", data)
        
        # Restore valid token
        self.auth_token = old_token
        
        # Test non-existent resource
        success, data = self.make_request("GET", "/projects/non-existent-id", expected_status=404)
        if success:
            self.log_result("Non-existent Resource", "PASS")
        else:
            self.log_result("Non-existent Resource", "FAIL", data)
    
    def run_all_tests(self):
        """Run all tests"""
        print("AGNO WORKSPHERE - API TESTING SUITE")
        print("=" * 50)
        
        start_time = time.time()
        
        try:
            self.test_health()
            self.test_authentication()
            self.test_user_endpoints()
            self.test_organization_endpoints()
            self.test_project_endpoints()
            self.test_board_endpoints()
            self.test_error_handling()
            
        except Exception as e:
            print(f"\nTest suite crashed: {e}")
            import traceback
            traceback.print_exc()
        
        total_time = time.time() - start_time
        self.print_summary(total_time)
    
    def print_summary(self, total_time):
        """Print test summary"""
        print("\n" + "=" * 50)
        print("TEST SUMMARY")
        print("=" * 50)
        
        total_tests = len(self.results)
        passed_tests = len([r for r in self.results if r["status"] == "PASS"])
        failed_tests = len([r for r in self.results if r["status"] == "FAIL"])
        skipped_tests = len([r for r in self.results if r["status"] == "SKIP"])
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Skipped: {skipped_tests}")
        print(f"Total Time: {total_time:.2f}s")
        print(f"Success Rate: {(passed_tests/total_tests*100):.1f}%" if total_tests > 0 else "N/A")
        
        if failed_tests > 0:
            print(f"\nFAILED TESTS:")
            for result in self.results:
                if result["status"] == "FAIL":
                    print(f"  - {result['test']}: {result['details']}")
        
        print(f"\nTest Environment:")
        print(f"  Base URL: {BASE_URL}")
        print(f"  Test User: {self.test_user_email}")
        print(f"  Organization ID: {self.test_org_id}")
        print(f"  Project ID: {self.test_project_id}")
        
        # Save results
        filename = f"simple_api_test_results_{int(time.time())}.json"
        with open(filename, 'w') as f:
            json.dump({
                "timestamp": time.time(),
                "total_time": total_time,
                "summary": {
                    "total": total_tests,
                    "passed": passed_tests,
                    "failed": failed_tests,
                    "skipped": skipped_tests
                },
                "results": self.results
            }, f, indent=2)
        
        print(f"\nResults saved to: {filename}")

if __name__ == "__main__":
    tester = SimpleAPITester()
    tester.run_all_tests()
