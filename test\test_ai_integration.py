#!/usr/bin/env python3
"""
Test script for AI project generation integration
"""
import asyncio
import sys
import os
import json

# Add the backend directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'backend'))

from app.services.ai_service import AIService

class MockDB:
    """Mock database session for testing"""
    def query(self, *args):
        return self
    def filter(self, *args):
        return self
    def first(self):
        return None
    def add(self, *args):
        pass
    def commit(self):
        pass

async def test_ai_project_generation():
    """Test AI project generation functionality"""
    print("🤖 Testing AI Project Generation Integration")
    print("=" * 50)
    
    # Initialize AI service
    ai_service = AIService(MockDB())
    
    # Test project data
    test_projects = [
        {
            "name": "ChatBot",
            "type": "web_application",
            "team_size": 5,
            "team_experience": "intermediate"
        },
        {
            "name": "E-commerce Mobile App",
            "type": "mobile_app",
            "team_size": 7,
            "team_experience": "advanced"
        },
        {
            "name": "Healthcare Analytics Platform",
            "type": "data_analysis",
            "team_size": 4,
            "team_experience": "intermediate"
        }
    ]
    
    for i, project in enumerate(test_projects, 1):
        print(f"\n📋 Test {i}: {project['name']}")
        print("-" * 30)
        
        try:
            # Test AI project preview generation
            result = await ai_service.generate_ai_project_preview(
                project_name=project["name"],
                organization_id="test-org-123",
                user_id="test-user-123",
                project_type=project["type"],
                team_size=project["team_size"],
                team_experience=project["team_experience"]
            )
            
            print(f"✅ Project generation successful!")
            print(f"📝 Description: {result['project'].get('description', 'N/A')[:100]}...")
            print(f"🎯 Objectives: {len(result['project'].get('objectives', []))} objectives")
            print(f"📋 Tasks: {len(result.get('tasks', []))} tasks generated")
            print(f"💡 Suggestions: {len(result.get('suggestions', []))} suggestions")
            print(f"⏱️  Estimated Duration: {result.get('estimated_duration', 'N/A')} weeks")
            
            # Display some suggestions
            if result.get('suggestions'):
                print(f"\n💡 Sample Suggestions:")
                for j, suggestion in enumerate(result['suggestions'][:3], 1):
                    print(f"   {j}. [{suggestion.get('category', 'general')}] {suggestion.get('title', 'N/A')}")
            
        except Exception as e:
            print(f"❌ Error: {str(e)}")
            if "OpenAI" in str(e):
                print("   Note: This is expected if OpenAI API key is not configured")
                print("   The system should fall back to template-based generation")
            
    print(f"\n🎉 AI Integration Test Complete!")
    print("=" * 50)

async def test_ai_suggestions():
    """Test AI suggestions generation"""
    print("\n💡 Testing AI Suggestions Generation")
    print("=" * 40)
    
    ai_service = AIService(MockDB())
    
    # Test suggestions for different project types
    test_data = {
        "name": "ChatBot",
        "description": "AI-powered customer service chatbot",
        "industry": "Technology",
        "complexity": "medium"
    }
    
    try:
        suggestions = await ai_service._generate_ai_suggestions(
            "ChatBot", 
            "web_application", 
            test_data
        )
        
        print(f"✅ Generated {len(suggestions)} suggestions")
        
        for i, suggestion in enumerate(suggestions[:5], 1):
            print(f"\n{i}. {suggestion.get('title', 'N/A')}")
            print(f"   Category: {suggestion.get('category', 'N/A')}")
            print(f"   Priority: {suggestion.get('priority', 'N/A')}")
            print(f"   Description: {suggestion.get('description', 'N/A')[:80]}...")
            
    except Exception as e:
        print(f"❌ Suggestions Error: {str(e)}")

if __name__ == "__main__":
    print("🚀 Starting AI Integration Tests")
    print("This will test the AI project generation functionality")
    print("Note: If OpenAI API is not configured, it will use template fallbacks\n")
    
    # Run the tests
    asyncio.run(test_ai_project_generation())
    asyncio.run(test_ai_suggestions())
    
    print("\n✨ All tests completed!")
    print("The AI integration is ready for use in the project management system.")
