// src/utils/apiService.js (MOCK - No Backend Required)

// Mock data for projects
const mockProjects = [
  {
    id: 'proj-1',
    name: 'Website Redesign',
    description: 'Complete redesign of the company website with modern UI/UX',
    status: 'active',
    priority: 'high',
    start_date: '2024-01-15',
    due_date: '2024-03-15',
    created_at: new Date(Date.now() - 86400000 * 30).toISOString(), // 30 days ago
    created_by: 'user-1',
    organization_id: 'org-1',
    progress: 65
  },
  {
    id: 'proj-2',
    name: 'Mobile App Development',
    description: 'Native mobile app for iOS and Android platforms',
    status: 'active',
    priority: 'medium',
    start_date: '2024-02-01',
    due_date: '2024-06-01',
    created_at: new Date(Date.now() - 86400000 * 20).toISOString(), // 20 days ago
    created_by: 'user-2',
    organization_id: 'org-1',
    progress: 30
  },
  {
    id: 'proj-3',
    name: 'API Integration',
    description: 'Integration with third-party APIs for enhanced functionality',
    status: 'completed',
    priority: 'low',
    start_date: '2023-12-01',
    due_date: '2024-01-31',
    created_at: new Date(Date.now() - 86400000 * 45).toISOString(), // 45 days ago
    created_by: 'user-1',
    organization_id: 'org-1',
    progress: 100
  }
];

// Simulate API delay
const delay = (ms) => new Promise(resolve => setTimeout(resolve, ms));

// Helper function to get headers with authentication (not used in mock mode)
// const getAuthHeaders = (organizationId = null) => {
//   const token = localStorage.getItem('accessToken');
//   const headers = {
//     'Content-Type': 'application/json',
//     ...(organizationId && { 'X-Organization-ID': organizationId })
//   };

//   if (token) {
//     headers['Authorization'] = `Bearer ${token}`;
//   }

//   return headers;
// };

// Helper function to handle API responses (not used in mock mode)
// const handleResponse = async (response) => {
//   const result = await response.json();

//   if (!response.ok) {
//     throw new Error(result.error?.message || result.message || 'API request failed');
//   }

//   return result;
// };

const apiService = {
  // Organizations (Use organizationService.js instead)
  // organizations: {
  //   // Commented out - use organizationService.js for organization operations
  // },

    // All organization methods commented out - use organizationService.js instead

  // Projects (MOCK - No Backend)
  projects: {
    getAll: async (organizationId, params = {}) => {
      try {
        // Simulate API delay
        await delay(300);

        // Filter projects by organization if specified
        let filteredProjects = mockProjects;
        if (organizationId) {
          filteredProjects = mockProjects.filter(p => p.organization_id === organizationId);
        }

        return filteredProjects;
      } catch (error) {
        console.error('Failed to fetch projects:', error);
        throw error;
      }
    },

    create: async (organizationId, projectData) => {
      try {
        // Simulate API delay
        await delay(500);

        const newProject = {
          id: `proj-${Date.now()}`,
          ...projectData,
          organization_id: organizationId,
          status: 'active',
          progress: 0,
          created_at: new Date().toISOString(),
          created_by: 'current-user'
        };

        // Add to mock data
        mockProjects.push(newProject);

        return newProject;
      } catch (error) {
        console.error('Failed to create project:', error);
        throw error;
      }
    },

    getById: async (id) => {
      try {
        // Simulate API delay
        await delay(200);

        const project = mockProjects.find(p => p.id === id);
        if (!project) {
          throw new Error('Project not found');
        }

        return project;
      } catch (error) {
        console.error('Failed to fetch project:', error);
        throw error;
      }
    },

    update: async (id, updateData) => {
      try {
        // Simulate API delay
        await delay(300);

        const projectIndex = mockProjects.findIndex(p => p.id === id);
        if (projectIndex === -1) {
          throw new Error('Project not found');
        }

        // Update the project
        mockProjects[projectIndex] = {
          ...mockProjects[projectIndex],
          ...updateData,
          updated_at: new Date().toISOString()
        };

        return mockProjects[projectIndex];
      } catch (error) {
        console.error('Failed to update project:', error);
        throw error;
      }
    },

    delete: async (id) => {
      try {
        // Simulate API delay
        await delay(300);

        const projectIndex = mockProjects.findIndex(p => p.id === id);
        if (projectIndex === -1) {
          throw new Error('Project not found');
        }

        // Remove the project
        const deletedProject = mockProjects.splice(projectIndex, 1)[0];

        return { success: true, data: deletedProject };
      } catch (error) {
        console.error('Failed to delete project:', error);
        throw error;
      }
    }
  },

  // Boards (MOCK - Temporarily disabled)
  boards: {
    getByProject: async (projectId) => {
      try {
        // Simulate API delay
        await delay(300);

        // Return mock boards data
        return [
          {
            id: 'board-1',
            name: 'Project Board',
            project_id: projectId,
            columns: []
          }
        ];
      } catch (error) {
        console.error('Failed to fetch boards:', error);
        throw error;
      }
    },

    getById: async (id) => {
      try {
        // Simulate API delay
        await delay(200);

        // Return mock board data
        return {
          id: id,
          name: 'Mock Board',
          project_id: 'proj-1',
          columns: []
        };
      } catch (error) {
        console.error('Failed to fetch board:', error);
        throw error;
      }
    },

    create: async (projectId, boardData) => {
      try {
        // Simulate API delay
        await delay(300);

        // Return mock created board
        return {
          id: `board-${Date.now()}`,
          ...boardData,
          project_id: projectId,
          created_at: new Date().toISOString()
        };
      } catch (error) {
        console.error('Failed to create board:', error);
        throw error;
      }
    },

    update: async (id, updateData) => {
      try {
        // Simulate API delay
        await delay(300);

        // Return mock updated board
        return {
          id: id,
          ...updateData,
          updated_at: new Date().toISOString()
        };
      } catch (error) {
        console.error('Failed to update board:', error);
        throw error;
      }
    },

    delete: async (id) => {
      try {
        // Simulate API delay
        await delay(300);

        // Return success response
        return { success: true, id: id };
      } catch (error) {
        console.error('Failed to delete board:', error);
        throw error;
      }
    }
  },

  // Users (MOCK - Temporarily disabled)
  users: {
    getProfile: async () => {
      try {
        // Simulate API delay
        await delay(200);

        // Return mock user profile
        const currentUser = localStorage.getItem('currentUser');
        if (currentUser) {
          return JSON.parse(currentUser);
        }

        return {
          id: 'user-1',
          email: '<EMAIL>',
          first_name: 'Demo',
          last_name: 'User'
        };
      } catch (error) {
        console.error('Failed to fetch user profile:', error);
        throw error;
      }
    },

    updateProfile: async (updateData) => {
      try {
        // Simulate API delay
        await delay(300);

        // Update localStorage
        const currentUser = localStorage.getItem('currentUser');
        if (currentUser) {
          const userData = JSON.parse(currentUser);
          const updatedUser = { ...userData, ...updateData };
          localStorage.setItem('currentUser', JSON.stringify(updatedUser));
          return updatedUser;
        }

        return updateData;
      } catch (error) {
        console.error('Failed to update user profile:', error);
        throw error;
      }
    },

    uploadAvatar: async (file) => {
      try {
        // Simulate API delay
        await delay(500);

        // Mock avatar upload - return a fake URL
        const avatarUrl = `https://api.dicebear.com/7.x/avataaars/svg?seed=${Date.now()}`;

        return {
          avatar_url: avatarUrl,
          success: true
        };
      } catch (error) {
        console.error('Failed to upload avatar:', error);
        throw error;
      }
    }
  },

  // Organization API methods
  organizations: {
    // Create new organization
    create: async (orgData, logoFile = null) => {
      try {
        // Import organizationService dynamically to avoid circular imports
        const { createOrganization } = await import('./organizationService');
        return await createOrganization(orgData, logoFile);
      } catch (error) {
        console.error('Failed to create organization:', error);
        throw error;
      }
    },

    // Get all organizations
    getAll: async () => {
      try {
        const { getOrganizations } = await import('./organizationService');
        return await getOrganizations();
      } catch (error) {
        console.error('Failed to get organizations:', error);
        throw error;
      }
    },

    // Get organization by ID
    getById: async (id) => {
      try {
        const { getOrganizationById } = await import('./organizationService');
        return await getOrganizationById(id);
      } catch (error) {
        console.error('Failed to get organization:', error);
        throw error;
      }
    },

    // Update organization
    update: async (id, orgData, logoFile = null) => {
      try {
        const { updateOrganization } = await import('./organizationService');
        return await updateOrganization(id, orgData, logoFile);
      } catch (error) {
        console.error('Failed to update organization:', error);
        throw error;
      }
    },

    // Upload organization logo
    uploadLogo: async (id, logoFile) => {
      try {
        const { uploadOrganizationLogo } = await import('./organizationService');
        return await uploadOrganizationLogo(id, logoFile);
      } catch (error) {
        console.error('Failed to upload organization logo:', error);
        throw error;
      }
    },

    // Delete organization logo
    deleteLogo: async (id) => {
      try {
        const { deleteOrganizationLogo } = await import('./organizationService');
        return await deleteOrganizationLogo(id);
      } catch (error) {
        console.error('Failed to delete organization logo:', error);
        throw error;
      }
    }
  }
};

export default apiService;
