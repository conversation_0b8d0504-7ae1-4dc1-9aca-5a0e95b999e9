/**
 * Integration Test for Organization Creation Flow
 * This test verifies the complete organization creation functionality
 */

// Mock test data
const testOrganizationData = {
  name: "Test Organization",
  description: "A comprehensive test organization for validation",
  website: "https://test-org.com",
  industry: "technology",
  size: "11-50",
  organization_category: "startup",
  contact_email: "<EMAIL>",
  contact_phone: "+****************",
  address_line1: "123 Test Street",
  address_line2: "Suite 100",
  city: "Test City",
  state: "Test State",
  postal_code: "12345",
  country: "US",
  timezone: "America/New_York",
  language: "en",
  allowed_domains: "test-org.com, testorg.net"
};

// Mock logo file for testing
const createMockLogoFile = () => {
  const canvas = document.createElement('canvas');
  canvas.width = 200;
  canvas.height = 200;
  const ctx = canvas.getContext('2d');
  
  // Draw a simple logo
  ctx.fillStyle = '#3B82F6';
  ctx.fillRect(0, 0, 200, 200);
  ctx.fillStyle = '#FFFFFF';
  ctx.font = '48px Arial';
  ctx.textAlign = 'center';
  ctx.fillText('TO', 100, 120);
  
  return new Promise(resolve => {
    canvas.toBlob(blob => {
      const file = new File([blob], 'test-logo.png', { type: 'image/png' });
      resolve(file);
    }, 'image/png');
  });
};

// Test functions
const testOrganizationService = async () => {
  console.log('🧪 Testing Organization Service...');
  
  try {
    // Import the organization service
    const { createOrganization, updateOrganization, uploadOrganizationLogo } = 
      await import('../agnoworksphere/src/utils/organizationService.js');
    
    // Test 1: Create organization without logo
    console.log('📝 Test 1: Creating organization without logo...');
    const result1 = await createOrganization(testOrganizationData);
    
    if (result1.error) {
      throw new Error(`Organization creation failed: ${result1.error}`);
    }
    
    console.log('✅ Organization created successfully:', result1.data.name);
    console.log('📊 Organization ID:', result1.data.id);
    
    // Test 2: Create organization with logo
    console.log('📝 Test 2: Creating organization with logo...');
    const logoFile = await createMockLogoFile();
    const result2 = await createOrganization({
      ...testOrganizationData,
      name: "Test Organization with Logo"
    }, logoFile);
    
    if (result2.error) {
      throw new Error(`Organization creation with logo failed: ${result2.error}`);
    }
    
    console.log('✅ Organization with logo created successfully:', result2.data.name);
    console.log('🖼️ Logo URL:', result2.data.logo_url);
    
    // Test 3: Update organization
    console.log('📝 Test 3: Updating organization...');
    const updateData = {
      description: "Updated description for testing",
      contact_email: "<EMAIL>"
    };
    
    const result3 = await updateOrganization(result1.data.id, updateData);
    
    if (result3.error) {
      throw new Error(`Organization update failed: ${result3.error}`);
    }
    
    console.log('✅ Organization updated successfully');
    
    // Test 4: Upload logo separately
    console.log('📝 Test 4: Uploading logo separately...');
    const logoFile2 = await createMockLogoFile();
    const result4 = await uploadOrganizationLogo(result1.data.id, logoFile2);
    
    if (result4.error) {
      throw new Error(`Logo upload failed: ${result4.error}`);
    }
    
    console.log('✅ Logo uploaded successfully');
    
    return true;
  } catch (error) {
    console.error('❌ Organization Service Test Failed:', error.message);
    return false;
  }
};

const testApiService = async () => {
  console.log('🧪 Testing API Service...');
  
  try {
    // Import the API service
    const apiService = await import('../agnoworksphere/src/utils/apiService.js');
    
    // Test organization creation through API service
    console.log('📝 Testing API service organization creation...');
    const logoFile = await createMockLogoFile();
    const result = await apiService.default.organizations.create(testOrganizationData, logoFile);
    
    if (result.error) {
      throw new Error(`API service creation failed: ${result.error}`);
    }
    
    console.log('✅ API service organization creation successful:', result.data.name);
    
    return true;
  } catch (error) {
    console.error('❌ API Service Test Failed:', error.message);
    return false;
  }
};

const testValidation = async () => {
  console.log('🧪 Testing Validation...');
  
  try {
    const { createOrganization } = await import('../agnoworksphere/src/utils/organizationService.js');
    
    // Test 1: Empty name validation
    console.log('📝 Test 1: Empty name validation...');
    const result1 = await createOrganization({ ...testOrganizationData, name: '' });
    
    if (!result1.error) {
      throw new Error('Expected validation error for empty name');
    }
    
    console.log('✅ Empty name validation works:', result1.error);
    
    // Test 2: Invalid email validation
    console.log('📝 Test 2: Invalid email validation...');
    const result2 = await createOrganization({ 
      ...testOrganizationData, 
      contact_email: 'invalid-email' 
    });
    
    if (!result2.error) {
      throw new Error('Expected validation error for invalid email');
    }
    
    console.log('✅ Invalid email validation works:', result2.error);
    
    // Test 3: Invalid logo file type
    console.log('📝 Test 3: Invalid logo file type validation...');
    const invalidFile = new File(['test'], 'test.txt', { type: 'text/plain' });
    const result3 = await createOrganization(testOrganizationData, invalidFile);
    
    if (!result3.error) {
      throw new Error('Expected validation error for invalid file type');
    }
    
    console.log('✅ Invalid file type validation works:', result3.error);
    
    return true;
  } catch (error) {
    console.error('❌ Validation Test Failed:', error.message);
    return false;
  }
};

// Main test runner
const runIntegrationTests = async () => {
  console.log('🚀 Starting Organization Creation Integration Tests...\n');
  
  const results = {
    organizationService: false,
    apiService: false,
    validation: false
  };
  
  // Run tests
  results.organizationService = await testOrganizationService();
  console.log('');
  
  results.apiService = await testApiService();
  console.log('');
  
  results.validation = await testValidation();
  console.log('');
  
  // Summary
  console.log('📋 Test Results Summary:');
  console.log('========================');
  console.log(`Organization Service: ${results.organizationService ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`API Service: ${results.apiService ? '✅ PASS' : '❌ FAIL'}`);
  console.log(`Validation: ${results.validation ? '✅ PASS' : '❌ FAIL'}`);
  
  const allPassed = Object.values(results).every(result => result === true);
  console.log(`\n🎯 Overall Result: ${allPassed ? '✅ ALL TESTS PASSED' : '❌ SOME TESTS FAILED'}`);
  
  if (allPassed) {
    console.log('\n🎉 Organization creation flow is working correctly!');
    console.log('✨ Ready for production use.');
  } else {
    console.log('\n⚠️ Some tests failed. Please review the implementation.');
  }
  
  return allPassed;
};

// Export for use in browser console or test runner
if (typeof window !== 'undefined') {
  window.runOrganizationCreationTests = runIntegrationTests;
  console.log('🔧 Integration tests loaded. Run window.runOrganizationCreationTests() to start testing.');
} else if (typeof module !== 'undefined') {
  module.exports = { runIntegrationTests, testOrganizationData };
}

// Auto-run if this file is executed directly
if (typeof window === 'undefined' && require.main === module) {
  runIntegrationTests();
}
