#!/usr/bin/env python3
"""
Development server runner for Agno WorkSphere API
"""
import uvicorn
import sys
import os

# Add the backend directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from app.config import settings
    print(f"🚀 Starting Agno WorkSphere API")
    print(f"📍 Server will be available at: http://localhost:3001")
    print(f"📚 API Documentation: http://localhost:3001/docs")

    if __name__ == "__main__":
        uvicorn.run(
            "enhanced_server:app",
            host="0.0.0.0",
            port=3001,
            reload=True,
            log_level="info",
            access_log=True
        )
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("🔧 Trying to run enhanced_server directly...")

    if __name__ == "__main__":
        uvicorn.run(
            "enhanced_server:app",
            host="0.0.0.0",
            port=3001,
            reload=True,
            log_level="info",
            access_log=True
        )
