import realApiService from './realApiService';

/**
 * Notification service for managing user notifications
 */

// Get notifications for the current user
export const getNotifications = async (filters = {}) => {
  try {
    console.log('Fetching notifications with filters:', filters);

    // For now, return mock data since the API endpoint doesn't exist yet
    const mockNotifications = [
      {
        id: '1',
        title: 'Welcome to Agno WorkSphere',
        message: 'Your account has been successfully created.',
        type: 'info',
        priority: 'medium',
        read: false,
        createdAt: new Date().toISOString(),
        actionUrl: null
      },
      {
        id: '2',
        title: 'Project Update',
        message: 'New task has been assigned to you.',
        type: 'task',
        priority: 'high',
        read: false,
        createdAt: new Date(Date.now() - 3600000).toISOString(),
        actionUrl: '/kanban-board'
      }
    ];

    return {
      success: true,
      data: mockNotifications,
      pagination: {
        page: filters.page || 1,
        limit: filters.limit || 20,
        total: mockNotifications.length,
        totalPages: 1
      }
    };
  } catch (error) {
    console.error('Failed to fetch notifications:', error);

    // Return empty array on error
    return {
      success: false,
      data: [],
      error: error.message
    };
  }
};

// Mark notification as read
export const markNotificationAsRead = async (notificationId) => {
  try {
    console.log(`Marking notification ${notificationId} as read`);

    // Mock successful response
    return {
      success: true,
      message: 'Notification marked as read',
      data: { id: notificationId, read: true }
    };
  } catch (error) {
    console.error('Failed to mark notification as read:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Mark all notifications as read
export const markAllNotificationsAsRead = async () => {
  try {
    console.log('Marking all notifications as read');

    // Mock successful response
    return {
      success: true,
      message: 'All notifications marked as read',
      data: { markedCount: 2 }
    };
  } catch (error) {
    console.error('Failed to mark all notifications as read:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Create a welcome notification for new users
export const createWelcomeNotification = async (userId, organizationName) => {
  try {
    console.log(`Creating welcome notification for user ${userId} in organization ${organizationName}`);

    const welcomeNotification = {
      id: `welcome_${userId}_${Date.now()}`,
      type: 'welcome',
      title: 'Welcome to Agno WorkSphere!',
      message: `Welcome to ${organizationName}! We're excited to have you on board. Start by exploring your dashboard and setting up your first project.`,
      priority: 'high',
      read: false,
      createdAt: new Date().toISOString(),
      data: {
        isWelcome: true,
        organizationName,
        actions: [
          { label: 'Get Started', variant: 'default', action: 'tour' },
          { label: 'View Profile', variant: 'outline', action: 'profile' }
        ]
      }
    };

    // Mock successful creation
    return {
      success: true,
      data: welcomeNotification,
      message: 'Welcome notification created successfully'
    };
  } catch (error) {
    console.error('Failed to create welcome notification:', error);
    // Don't throw error for welcome notifications - they're not critical
    return null;
  }
};

// Create a notification
export const createNotification = async (notificationData) => {
  try {
    console.log('Creating notification:', notificationData);

    const notification = {
      id: `notif_${Date.now()}`,
      ...notificationData,
      createdAt: new Date().toISOString(),
      read: false
    };

    // Mock successful creation
    return {
      success: true,
      data: notification,
      message: 'Notification created successfully'
    };
  } catch (error) {
    console.error('Failed to create notification:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Delete notification
export const deleteNotification = async (notificationId) => {
  try {
    console.log(`Deleting notification ${notificationId}`);

    // Mock successful deletion
    return {
      success: true,
      message: 'Notification deleted successfully',
      data: { id: notificationId }
    };
  } catch (error) {
    console.error('Failed to delete notification:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Get notification preferences
export const getNotificationPreferences = async () => {
  try {
    console.log('Fetching notification preferences');

    // Mock preferences
    const mockPreferences = {
      email: true,
      push: true,
      inApp: true,
      types: {
        tasks: true,
        projects: true,
        mentions: true,
        deadlines: true,
        system: false
      }
    };

    return mockPreferences;
  } catch (error) {
    console.error('Failed to fetch notification preferences:', error);
    return {};
  }
};

// Update notification preferences
export const updateNotificationPreferences = async (preferences) => {
  try {
    console.log('Updating notification preferences:', preferences);

    // Mock successful update
    return {
      success: true,
      data: preferences,
      message: 'Notification preferences updated successfully'
    };
  } catch (error) {
    console.error('Failed to update notification preferences:', error);
    return {
      success: false,
      error: error.message
    };
  }
};

// Check if user is a first-time user and needs welcome notification
export const checkFirstTimeUser = async () => {
  try {
    // Check if user has any notifications or if this is their first login
    const result = await getNotifications({ limit: 1 });
    const notifications = result.data || [];

    // If no notifications exist, this might be a first-time user
    if (notifications.length === 0) {
      return true;
    }

    // Check if there's already a welcome notification
    const hasWelcomeNotification = notifications.some(n => n.type === 'welcome' || n.data?.isWelcome);
    return !hasWelcomeNotification;
  } catch (error) {
    console.error('Failed to check first-time user status:', error);
    return false;
  }
};

// Generate mock notifications for development/fallback
export const generateMockNotifications = () => {
  return [
    {
      id: 'welcome_001',
      type: 'welcome',
      title: 'Welcome to Agno WorkSphere!',
      message: 'Welcome to your new workspace! Start by exploring your dashboard and setting up your first project.',
      timestamp: new Date(),
      isRead: false,
      read: false,
      priority: 'high',
      data: {
        isWelcome: true,
        actions: [
          { label: 'Get Started', variant: 'default', action: 'tour' },
          { label: 'View Profile', variant: 'outline', action: 'profile' }
        ]
      }
    }
  ];
};

// Helper functions for specific notification types
export const notifyProjectCreated = async (projectData, creatorId) => {
  return createNotification({
    type: 'project_created',
    title: 'Project Created Successfully',
    message: `Project "${projectData.name}" has been created successfully.`,
    data: { projectId: projectData.id, projectName: projectData.name },
    userId: creatorId,
    priority: 'medium'
  });
};

export const notifyTaskAssigned = async (taskData, assigneeId, assignerId) => {
  return createNotification({
    type: 'task_assigned',
    title: 'New Task Assigned',
    message: `You have been assigned to task "${taskData.title}".`,
    data: { taskId: taskData.id, taskTitle: taskData.title, assignerId },
    userId: assigneeId,
    priority: 'high'
  });
};

export const notifyTaskCompleted = async (taskData, projectOwnerId) => {
  return createNotification({
    type: 'task_completed',
    title: 'Task Completed',
    message: `Task "${taskData.title}" has been completed.`,
    data: { taskId: taskData.id, taskTitle: taskData.title },
    userId: projectOwnerId,
    priority: 'medium'
  });
};

export const notifyTaskUpdated = async (taskData, projectOwnerId) => {
  return createNotification({
    type: 'task_updated',
    title: 'Task Updated',
    message: `Task "${taskData.title}" has been updated.`,
    data: { taskId: taskData.id, taskTitle: taskData.title },
    userId: projectOwnerId,
    priority: 'low'
  });
};

export const notifyTeamMemberAdded = async (memberData, projectId, projectOwnerId) => {
  return createNotification({
    type: 'team_member_added',
    title: 'New Team Member',
    message: `${memberData.name} has been added to the project.`,
    data: { memberId: memberData.id, memberName: memberData.name, projectId },
    userId: projectOwnerId,
    priority: 'medium'
  });
};

// Real-time notification manager
class NotificationManager {
  constructor() {
    this.listeners = new Set();
    this.notifications = [];
    this.isPolling = false;
    this.pollingInterval = null;
  }

  // Start real-time polling
  startRealTime() {
    if (this.isPolling) return;

    this.isPolling = true;
    this.pollingInterval = setInterval(async () => {
      try {
        const result = await getNotifications();
        if (result.success) {
          this.notifications = result.data;
          this.notifyListeners();
        }
      } catch (error) {
        console.error('Error polling notifications:', error);
      }
    }, 30000); // Poll every 30 seconds

    console.log('Real-time notifications started');
  }

  // Stop real-time polling
  stopRealTime() {
    if (this.pollingInterval) {
      clearInterval(this.pollingInterval);
      this.pollingInterval = null;
    }
    this.isPolling = false;
    console.log('Real-time notifications stopped');
  }

  // Add listener
  addListener(callback) {
    this.listeners.add(callback);
    return () => this.listeners.delete(callback);
  }

  // Notify listeners
  notifyListeners() {
    this.listeners.forEach(callback => {
      try {
        callback(this.notifications);
      } catch (error) {
        console.error('Error in notification listener:', error);
      }
    });
  }

  // Get unread count
  getUnreadCount() {
    return this.notifications.filter(n => !n.read).length;
  }
}

// Create singleton instance
const notificationManager = new NotificationManager();

// Default export
const notificationService = {
  getNotifications,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  createWelcomeNotification,
  createNotification,
  deleteNotification,
  getNotificationPreferences,
  updateNotificationPreferences,
  checkFirstTimeUser,
  generateMockNotifications,
  // New notification helpers
  notifyProjectCreated,
  notifyTaskAssigned,
  notifyTaskCompleted,
  notifyTaskUpdated,
  notifyTeamMemberAdded,
  // Real-time manager
  manager: notificationManager
};

export default notificationService;
