#!/usr/bin/env python3
"""
Simple FastAPI server for testing without database
"""
import sys
import os
import time
from typing import Optional

# Add the backend directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from fastapi import FastAP<PERSON>, HTTPException, Header
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from app.config import settings
from app.core.security import hash_password, verify_password, create_access_token

# Create FastAPI app
app = FastAPI(
    title=settings.app_name,
    version=settings.app_version,
    description="Simple test server for Agno WorkSphere API"
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.allowed_origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# In-memory storage for testing
users_db = {}
organizations_db = {}
projects_db = {}

# Pydantic models
class UserRegister(BaseModel):
    email: str
    password: str
    first_name: str
    last_name: str

class UserLogin(BaseModel):
    email: str
    password: str

class TokenResponse(BaseModel):
    access_token: str
    token_type: str = "bearer"

class UserResponse(BaseModel):
    id: str
    email: str
    first_name: str
    last_name: str

class OrganizationCreate(BaseModel):
    name: str
    description: Optional[str] = None

class ProjectCreate(BaseModel):
    name: str
    description: Optional[str] = None


@app.get("/")
async def root():
    """Root endpoint"""
    return {
        "success": True,
        "data": {
            "message": "Welcome to Agno WorkSphere API (Test Mode)",
            "version": settings.app_version,
            "environment": settings.environment
        },
        "timestamp": time.time()
    }


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "success": True,
        "data": {
            "status": "healthy",
            "version": settings.app_version,
            "environment": settings.environment,
            "mode": "test"
        },
        "timestamp": time.time()
    }


@app.post("/api/v1/auth/register", response_model=dict)
async def register(user_data: UserRegister):
    """Register a new user"""
    # Check if user already exists
    if user_data.email in users_db:
        raise HTTPException(status_code=409, detail="User with this email already exists")
    
    # Create user
    user_id = f"user_{len(users_db) + 1}"
    hashed_password = hash_password(user_data.password)
    
    user = {
        "id": user_id,
        "email": user_data.email,
        "password_hash": hashed_password,
        "first_name": user_data.first_name,
        "last_name": user_data.last_name,
        "email_verified": True,  # Auto-verify for testing
        "created_at": time.time()
    }
    
    users_db[user_data.email] = user
    
    # Generate token
    access_token = create_access_token({"sub": user_id, "email": user_data.email})
    
    return {
        "success": True,
        "data": {
            "user": UserResponse(**user),
            "tokens": TokenResponse(access_token=access_token)
        },
        "message": "User registered successfully"
    }


@app.post("/api/v1/auth/login", response_model=dict)
async def login(user_data: UserLogin):
    """Login user"""
    # Find user
    user = users_db.get(user_data.email)
    if not user or not verify_password(user_data.password, user["password_hash"]):
        raise HTTPException(status_code=401, detail="Invalid email or password")
    
    # Generate token
    access_token = create_access_token({"sub": user["id"], "email": user["email"]})
    
    return {
        "success": True,
        "data": {
            "user": UserResponse(**user),
            "tokens": TokenResponse(access_token=access_token)
        },
        "message": "Login successful"
    }


@app.get("/api/v1/users/profile")
async def get_profile(authorization: Optional[str] = Header(None)):
    """Get current user profile"""
    if not authorization:
        raise HTTPException(status_code=401, detail="Authorization header required")
    
    # For testing, just return a mock user
    mock_user = {
        "id": "user_1",
        "email": "<EMAIL>",
        "first_name": "Demo",
        "last_name": "User",
        "email_verified": True,
        "created_at": time.time()
    }
    
    return {
        "success": True,
        "data": UserResponse(**mock_user)
    }


@app.get("/api/v1/organizations")
async def get_organizations(authorization: Optional[str] = Header(None)):
    """Get organizations"""
    if not authorization:
        raise HTTPException(status_code=401, detail="Authorization header required")
    
    return {
        "success": True,
        "data": list(organizations_db.values())
    }


@app.post("/api/v1/organizations")
async def create_organization(
    org_data: OrganizationCreate,
    authorization: Optional[str] = Header(None)
):
    """Create organization"""
    if not authorization:
        raise HTTPException(status_code=401, detail="Authorization header required")
    
    org_id = f"org_{len(organizations_db) + 1}"
    organization = {
        "id": org_id,
        "name": org_data.name,
        "description": org_data.description,
        "created_at": time.time()
    }
    
    organizations_db[org_id] = organization
    
    return {
        "success": True,
        "data": organization,
        "message": "Organization created successfully"
    }


@app.get("/api/v1/projects")
async def get_projects(authorization: Optional[str] = Header(None)):
    """Get projects"""
    if not authorization:
        raise HTTPException(status_code=401, detail="Authorization header required")
    
    return {
        "success": True,
        "data": list(projects_db.values())
    }


@app.post("/api/v1/projects")
async def create_project(
    project_data: ProjectCreate,
    authorization: Optional[str] = Header(None)
):
    """Create project"""
    if not authorization:
        raise HTTPException(status_code=401, detail="Authorization header required")
    
    project_id = f"project_{len(projects_db) + 1}"
    project = {
        "id": project_id,
        "name": project_data.name,
        "description": project_data.description,
        "status": "active",
        "created_at": time.time()
    }
    
    projects_db[project_id] = project
    
    return {
        "success": True,
        "data": project,
        "message": "Project created successfully"
    }


if __name__ == "__main__":
    import uvicorn
    print(f"🚀 Starting {settings.app_name} (Test Mode)")
    print(f"📍 Server will be available at: http://localhost:3001")
    print(f"📚 API Documentation: http://localhost:3001/docs")
    print(f"🔧 Environment: {settings.environment}")
    
    uvicorn.run(
        "simple_server:app",
        host="0.0.0.0",
        port=3001,
        reload=settings.debug,
        log_level="info"
    )
