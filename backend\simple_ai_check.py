#!/usr/bin/env python3
"""
Simple AI Integration Check
Quick verification of AI components
"""

import os
from datetime import datetime

def check_ai_config():
    """Check AI configuration in .env file"""
    print("🤖 AI INTEGRATION CHECK")
    print("=" * 40)
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    print(f"\n📋 AI Configuration:")
    
    # Check environment variables
    ai_enabled = os.getenv("AI_ENABLED", "True")
    openai_key = os.getenv("OPENAI_API_KEY", "")
    openai_model = os.getenv("OPENAI_MODEL", "gpt-3.5-turbo")
    prediction_enabled = os.getenv("AI_PREDICTION_ENABLED", "True")
    insights_enabled = os.getenv("AI_INSIGHTS_ENABLED", "True")
    notifications_enabled = os.getenv("AI_SMART_NOTIFICATIONS", "True")
    
    print(f"   AI Enabled: {'✅' if ai_enabled.lower() == 'true' else '❌'} {ai_enabled}")
    print(f"   OpenAI API Key: {'✅ Set' if openai_key else '❌ Not set (using demo mode)'}")
    print(f"   OpenAI Model: {openai_model}")
    print(f"   Predictions: {'✅' if prediction_enabled.lower() == 'true' else '❌'} {prediction_enabled}")
    print(f"   Insights: {'✅' if insights_enabled.lower() == 'true' else '❌'} {insights_enabled}")
    print(f"   Smart Notifications: {'✅' if notifications_enabled.lower() == 'true' else '❌'} {notifications_enabled}")
    
    return True

def check_ai_files():
    """Check AI-related files exist"""
    print(f"\n📁 AI Files Check:")
    
    ai_files = [
        'app/services/ai_service.py',
        'app/services/ai_checklist_service.py',
        'app/models/ai_automation.py'
    ]
    
    all_exist = True
    for file_path in ai_files:
        if os.path.exists(file_path):
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} (missing)")
            all_exist = False
    
    return all_exist

def check_ai_endpoints():
    """Check AI endpoints in enhanced_server.py"""
    print(f"\n🌐 AI Endpoints Check:")
    
    try:
        with open('enhanced_server.py', 'r') as f:
            content = f.read()
        
        ai_endpoints = [
            '/api/v1/ai/models',
            '/api/v1/ai/workflows',
            'ai_models',
            'ai_workflows'
        ]
        
        found_endpoints = []
        for endpoint in ai_endpoints:
            if endpoint in content:
                found_endpoints.append(endpoint)
                print(f"   ✅ {endpoint}")
            else:
                print(f"   ❌ {endpoint} (not found)")
        
        return len(found_endpoints) > 0
        
    except Exception as e:
        print(f"   ❌ Error checking endpoints: {str(e)}")
        return False

def main():
    """Main function"""
    print("🚀 Starting simple AI integration check...")
    
    # Run checks
    config_ok = check_ai_config()
    files_ok = check_ai_files()
    endpoints_ok = check_ai_endpoints()
    
    # Overall status
    overall_ok = config_ok and files_ok and endpoints_ok
    
    print(f"\n🎯 AI INTEGRATION STATUS: {'✅ READY' if overall_ok else '⚠️ PARTIAL'}")
    
    print(f"\n📊 Summary:")
    print(f"   Configuration: {'✅' if config_ok else '❌'}")
    print(f"   Files: {'✅' if files_ok else '❌'}")
    print(f"   Endpoints: {'✅' if endpoints_ok else '❌'}")
    
    print(f"\n🎯 AI Features Available:")
    print(f"   🎯 Task Priority Prediction (Demo Mode)")
    print(f"   ⏱️ Completion Time Estimation (Demo Mode)")
    print(f"   ⚠️ Risk Level Assessment (Demo Mode)")
    print(f"   📊 Project Health Analysis (Demo Mode)")
    print(f"   🔔 Smart Notifications (Demo Mode)")
    print(f"   📈 Performance Insights (Demo Mode)")
    
    if not os.getenv("OPENAI_API_KEY"):
        print(f"\n💡 Note: Running in demo mode without OpenAI API key")
        print(f"   AI features will use mock predictions and insights")
        print(f"   For production, set OPENAI_API_KEY in .env file")
    
    print(f"\n✅ AI integration is ready for live testing!")

if __name__ == "__main__":
    main()
