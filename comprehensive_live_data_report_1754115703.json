{"timestamp": "2025-08-02T11:51:36.828112", "phases": {"Phase 1": {"tests": [{"name": "Database Connection", "status": "FAIL", "details": "password authentication failed for user \"postgres\""}], "score": 0.0, "max_score": 100, "critical_issues": ["Database connection failed: password authentication failed for user \"postgres\""], "live_data_verified": [], "database_stats": {}}, "Phase 2": {"tests": [{"name": "Health Endpoint", "status": "PASS", "details": "Health check passed: {'success': True, 'data': {'status': 'healthy', 'version': '1.0.0', 'environment': 'development', 'mode': 'enhanced', 'email_configured': True}, 'timestamp': **********.0645785}"}, {"name": "User Registration", "status": "FAIL", "details": "Registration failed with status 404: {\"detail\":\"Not Found\"}"}], "score": 50.0, "max_score": 100, "critical_issues": ["User registration failed: 404"], "live_data_verified": ["API server healthy and responding"], "api_responses": {}}, "Phase 3": {"tests": [{"name": "Frontend Accessibility", "status": "PASS", "details": "Frontend accessible with React: True, Responsive: True"}], "score": 100.0, "max_score": 100, "critical_issues": [], "live_data_verified": ["Frontend React application accessible"], "frontend_stats": {"content_length": 1711, "has_react": true, "has_viewport": true}}, "Phase 4": {"tests": [{"name": "Domain Validation Concept", "status": "PASS", "details": "Valid domains: ['agnoshin.com', 'agno.com'], Invalid domains: ['gmail.com', 'yahoo.com']"}], "score": 100.0, "max_score": 100, "critical_issues": [], "live_data_verified": ["Domain validation concept verified"], "invitation_tests": {}}, "Phase 5": {"tests": [{"name": "RBAC Database Structure", "status": "FAIL", "details": "password authentication failed for user \"postgres\""}], "score": 0.0, "max_score": 100, "critical_issues": ["RBAC database check failed: password authentication failed for user \"postgres\""], "live_data_verified": [], "rbac_stats": {}}, "Phase 6": {"tests": [{"name": "Multi-Tenant Structure", "status": "FAIL", "details": "password authentication failed for user \"postgres\""}], "score": 0.0, "max_score": 100, "critical_issues": ["Multi-tenant check failed: password authentication failed for user \"postgres\""], "live_data_verified": [], "multitenant_stats": {}}, "Phase 7": {"tests": [{"name": "API Response Time", "status": "FAIL", "details": "Slow response: 2029.4ms"}, {"name": "Database Query Performance", "status": "FAIL", "details": "password authentication failed for user \"postgres\""}], "score": 0.0, "max_score": 100, "critical_issues": ["Slow API response: 2029.4ms", "Database performance test failed: password authentication failed for user \"postgres\""], "live_data_verified": [], "performance_stats": {"api_response_time_ms": 2029.4315814971924}}, "Phase 8": {"tests": [{"name": "Data Persistence", "status": "FAIL", "details": "password authentication failed for user \"postgres\""}], "score": 0.0, "max_score": 100, "critical_issues": ["Data persistence test failed: password authentication failed for user \"postgres\""], "live_data_verified": [], "realtime_stats": {}}}, "overall_score": 31.25, "critical_issues": [], "live_data_validation": {}, "test_data_created": {}}