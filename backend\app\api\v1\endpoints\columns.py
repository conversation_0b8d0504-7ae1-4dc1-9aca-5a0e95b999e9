"""
Column management endpoints
"""
from typing import List
from fastapi import APIRouter, Depends
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update
from sqlalchemy.orm import selectinload

from app.core.database import get_db
from app.core.deps import get_current_active_user
from app.core.exceptions import ResourceNotFoundError, InsufficientPermissionsError
from app.models.user import User
from app.models.organization import OrganizationMember
from app.models.board import Board
from app.models.column import Column
from app.schemas.project import ColumnCreate, ColumnUpdate, ColumnResponse, ColumnOrderUpdate
from app.schemas.card import CardCreate, CardResponse, CardAssignmentResponse
from app.models.card import Card, CardAssignment

router = APIRouter()


@router.get("/{column_id}", response_model=ColumnResponse)
async def get_column(
    column_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get column by ID"""
    result = await db.execute(
        select(Column)
        .options(selectinload(Column.board).selectinload(Board.project))
        .where(Column.id == column_id)
    )
    column = result.scalar_one_or_none()
    if not column:
        raise ResourceNotFoundError("Column not found")
    
    # Check access
    org_member_result = await db.execute(
        select(OrganizationMember).where(
            OrganizationMember.organization_id == column.board.project.organization_id,
            OrganizationMember.user_id == current_user.id
        )
    )
    if not org_member_result.scalar_one_or_none():
        raise InsufficientPermissionsError("Access denied")
    
    return ColumnResponse.from_orm(column)


@router.put("/{column_id}", response_model=ColumnResponse)
async def update_column(
    column_id: str,
    column_data: ColumnUpdate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Update column"""
    result = await db.execute(
        select(Column)
        .options(selectinload(Column.board).selectinload(Board.project))
        .where(Column.id == column_id)
    )
    column = result.scalar_one_or_none()
    if not column:
        raise ResourceNotFoundError("Column not found")
    
    # Check access and permissions
    org_member_result = await db.execute(
        select(OrganizationMember).where(
            OrganizationMember.organization_id == column.board.project.organization_id,
            OrganizationMember.user_id == current_user.id
        )
    )
    org_member = org_member_result.scalar_one_or_none()
    if not org_member:
        raise InsufficientPermissionsError("Access denied")
    
    if org_member.role not in ['member', 'admin', 'owner']:
        raise InsufficientPermissionsError("Insufficient permissions")
    
    # Update fields
    if column_data.name is not None:
        column.name = column_data.name
    if column_data.position is not None:
        column.position = column_data.position
    if column_data.color is not None:
        column.color = column_data.color
    
    await db.commit()
    await db.refresh(column)
    
    return ColumnResponse.from_orm(column)


@router.delete("/{column_id}")
async def delete_column(
    column_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Delete column"""
    result = await db.execute(
        select(Column)
        .options(selectinload(Column.board).selectinload(Board.project))
        .where(Column.id == column_id)
    )
    column = result.scalar_one_or_none()
    if not column:
        raise ResourceNotFoundError("Column not found")
    
    # Check access and permissions
    org_member_result = await db.execute(
        select(OrganizationMember).where(
            OrganizationMember.organization_id == column.board.project.organization_id,
            OrganizationMember.user_id == current_user.id
        )
    )
    org_member = org_member_result.scalar_one_or_none()
    if not org_member:
        raise InsufficientPermissionsError("Access denied")
    
    if org_member.role not in ['member', 'admin', 'owner']:
        raise InsufficientPermissionsError("Insufficient permissions")
    
    await db.delete(column)
    await db.commit()
    
    return {"success": True, "message": "Column deleted successfully"}


@router.get("/{board_id}/columns", response_model=List[ColumnResponse])
async def get_board_columns(
    board_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get columns for a board"""
    # Check board access
    board_result = await db.execute(
        select(Board)
        .options(selectinload(Board.project))
        .where(Board.id == board_id)
    )
    board = board_result.scalar_one_or_none()
    if not board:
        raise ResourceNotFoundError("Board not found")
    
    # Check organization membership
    org_member_result = await db.execute(
        select(OrganizationMember).where(
            OrganizationMember.organization_id == board.project.organization_id,
            OrganizationMember.user_id == current_user.id
        )
    )
    if not org_member_result.scalar_one_or_none():
        raise InsufficientPermissionsError("Access denied")
    
    # Get columns
    result = await db.execute(
        select(Column)
        .where(Column.board_id == board_id)
        .order_by(Column.position)
    )
    columns = result.scalars().all()
    
    return [ColumnResponse.from_orm(column) for column in columns]


@router.post("/{board_id}/columns", response_model=ColumnResponse)
async def create_column(
    board_id: str,
    column_data: ColumnCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a new column"""
    # Check board access
    board_result = await db.execute(
        select(Board)
        .options(selectinload(Board.project))
        .where(Board.id == board_id)
    )
    board = board_result.scalar_one_or_none()
    if not board:
        raise ResourceNotFoundError("Board not found")
    
    # Check organization membership and permissions
    org_member_result = await db.execute(
        select(OrganizationMember).where(
            OrganizationMember.organization_id == board.project.organization_id,
            OrganizationMember.user_id == current_user.id
        )
    )
    org_member = org_member_result.scalar_one_or_none()
    if not org_member:
        raise InsufficientPermissionsError("Access denied")
    
    if org_member.role not in ['member', 'admin', 'owner']:
        raise InsufficientPermissionsError("Insufficient permissions")
    
    # Create column
    column = Column(
        board_id=board_id,
        name=column_data.name,
        position=column_data.position,
        color=column_data.color
    )
    
    db.add(column)
    await db.commit()
    await db.refresh(column)
    
    return ColumnResponse.from_orm(column)


@router.put("/{board_id}/columns/order")
async def reorder_columns(
    board_id: str,
    order_data: ColumnOrderUpdate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Reorder columns in a board"""
    # Check board access and permissions
    board_result = await db.execute(
        select(Board)
        .options(selectinload(Board.project))
        .where(Board.id == board_id)
    )
    board = board_result.scalar_one_or_none()
    if not board:
        raise ResourceNotFoundError("Board not found")
    
    org_member_result = await db.execute(
        select(OrganizationMember).where(
            OrganizationMember.organization_id == board.project.organization_id,
            OrganizationMember.user_id == current_user.id
        )
    )
    org_member = org_member_result.scalar_one_or_none()
    if not org_member:
        raise InsufficientPermissionsError("Access denied")
    
    if org_member.role not in ['member', 'admin', 'owner']:
        raise InsufficientPermissionsError("Insufficient permissions")
    
    # Update column positions
    for column_order in order_data.column_orders:
        await db.execute(
            update(Column)
            .where(Column.id == column_order["id"], Column.board_id == board_id)
            .values(position=column_order["position"])
        )
    
    await db.commit()
    
    return {"success": True, "message": "Columns reordered successfully"}


@router.get("/{column_id}/cards", response_model=List[CardResponse])
async def get_column_cards(
    column_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get all cards in a column"""
    # Check column access
    column_result = await db.execute(
        select(Column)
        .options(
            selectinload(Column.board)
            .selectinload(Board.project)
        )
        .where(Column.id == column_id)
    )
    column = column_result.scalar_one_or_none()
    if not column:
        raise ResourceNotFoundError("Column not found")

    # Check organization membership
    org_member_result = await db.execute(
        select(OrganizationMember).where(
            OrganizationMember.organization_id == column.board.project.organization_id,
            OrganizationMember.user_id == current_user.id
        )
    )
    if not org_member_result.scalar_one_or_none():
        raise InsufficientPermissionsError("Access denied")

    # Get cards
    result = await db.execute(
        select(Card)
        .options(selectinload(Card.assignments).selectinload(CardAssignment.user))
        .where(Card.column_id == column_id)
        .order_by(Card.position)
    )
    cards = result.scalars().all()

    # Format response
    response = []
    for card in cards:
        card_response = CardResponse.from_orm(card)
        if card.assignments:
            card_response.assignments = []
            for assignment in card.assignments:
                assignment_data = CardAssignmentResponse.from_orm(assignment)
                assignment_data.user = {
                    "id": str(assignment.user.id),
                    "email": assignment.user.email,
                    "first_name": assignment.user.first_name,
                    "last_name": assignment.user.last_name,
                    "avatar_url": assignment.user.avatar_url
                }
                card_response.assignments.append(assignment_data)
        response.append(card_response)

    return response


@router.post("/{column_id}/cards", response_model=CardResponse)
async def create_column_card(
    column_id: str,
    card_data: CardCreate,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a new card in a column"""
    # Check column access
    column_result = await db.execute(
        select(Column)
        .options(
            selectinload(Column.board)
            .selectinload(Board.project)
        )
        .where(Column.id == column_id)
    )
    column = column_result.scalar_one_or_none()
    if not column:
        raise ResourceNotFoundError("Column not found")

    # Check organization membership and permissions
    org_member_result = await db.execute(
        select(OrganizationMember).where(
            OrganizationMember.organization_id == column.board.project.organization_id,
            OrganizationMember.user_id == current_user.id
        )
    )
    org_member = org_member_result.scalar_one_or_none()
    if not org_member:
        raise InsufficientPermissionsError("Access denied")

    if org_member.role not in ['member', 'admin', 'owner']:
        raise InsufficientPermissionsError("Insufficient permissions")

    # Get next position
    position_result = await db.execute(
        select(Card.position)
        .where(Card.column_id == column_id)
        .order_by(Card.position.desc())
        .limit(1)
    )
    max_position = position_result.scalar_one_or_none()
    next_position = (max_position or 0) + 1

    # Create card
    card = Card(
        column_id=column_id,
        title=card_data.title,
        description=card_data.description,
        priority=card_data.priority,
        due_date=card_data.due_date,
        position=next_position,
        created_by=current_user.id
    )

    db.add(card)
    await db.commit()
    await db.refresh(card)

    return CardResponse.from_orm(card)
