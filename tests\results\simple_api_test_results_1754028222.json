{"timestamp": **********.4129612, "total_time": 32.599101305007935, "summary": {"total": 15, "passed": 9, "failed": 6, "skipped": 0}, "results": [{"test": "Root Endpoint", "status": "FAIL", "details": "Expected 200, got 404: {\"detail\":\"Not Found\"}", "timestamp": **********.8543305}, {"test": "Health Check", "status": "FAIL", "details": "Expected 200, got 404: {\"detail\":\"Not Found\"}", "timestamp": **********.8998394}, {"test": "User Registration", "status": "PASS", "details": null, "timestamp": **********.2281747}, {"test": "User Login", "status": "PASS", "details": null, "timestamp": **********.9805064}, {"test": "Get User Profile", "status": "PASS", "details": null, "timestamp": **********.0185485}, {"test": "Update User Profile", "status": "FAIL", "details": "Expected 200, got 405: {\"detail\":\"Method Not Allowed\"}", "timestamp": **********.0595493}, {"test": "Get Organizations", "status": "PASS", "details": null, "timestamp": **********.1010642}, {"test": "Get Organization Details", "status": "FAIL", "details": "Expected 200, got 404: {\"detail\":\"Not Found\"}", "timestamp": **********.140217}, {"test": "Create Project", "status": "PASS", "details": null, "timestamp": **********.1952364}, {"test": "Get Projects", "status": "PASS", "details": null, "timestamp": 1754028212.2494442}, {"test": "Create Board", "status": "FAIL", "details": "Expected 200, got 404: {\"detail\":\"Not Found\"}", "timestamp": 1754028214.2912996}, {"test": "Get Boards", "status": "FAIL", "details": "Expected 200, got 404: {\"detail\":\"Not Found\"}", "timestamp": 1754028216.3228352}, {"test": "Unauthorized Access Block", "status": "PASS", "details": null, "timestamp": 1754028218.3612597}, {"test": "Invalid Token Block", "status": "PASS", "details": null, "timestamp": 1754028220.3838682}, {"test": "Non-existent Resource", "status": "PASS", "details": null, "timestamp": **********.4129612}]}