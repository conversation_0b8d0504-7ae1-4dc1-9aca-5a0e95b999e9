"""
AI Project Management endpoints for enhanced task management modal
"""
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from sqlalchemy.orm import selectinload
from pydantic import BaseModel, Field
from uuid import UUID
import uuid
from datetime import datetime

from app.core.database import get_db
from app.core.deps import get_current_active_user, require_member
from app.core.exceptions import ValidationError, ResourceNotFoundError, InsufficientPermissionsError
from app.models.user import User
from app.models.organization import OrganizationMember
from app.models.project import Project
from app.models.board import Board
from app.models.column import Column
from app.models.card import Card
from app.services.ai_service import AIService
from app.services.role_permissions import role_permissions

router = APIRouter()

# Pydantic models for AI project endpoints
class AIProjectPreviewRequest(BaseModel):
    name: str = Field(..., min_length=1, max_length=200)
    project_type: str = Field(default="general")
    team_size: int = Field(default=5, ge=2, le=20)
    team_experience: str = Field(default="intermediate")
    organization_id: str

class AIProjectPreviewResponse(BaseModel):
    project: Dict[str, Any]
    workflow: Dict[str, Any]
    tasks: List[Dict[str, Any]]
    metadata: Dict[str, Any]
    estimated_duration: int
    estimated_cost: float

class TaskReviewRequest(BaseModel):
    tasks: List[Dict[str, Any]]
    project_id: str
    modifications: Optional[Dict[str, Any]] = None

class TaskBulkUpdateRequest(BaseModel):
    task_ids: List[str]
    updates: Dict[str, Any]
    operation: str = Field(regex="^(update|delete|assign|prioritize)$")

class ProjectWorkflowRequest(BaseModel):
    project_id: str
    workflow_data: Dict[str, Any]
    phase: str = Field(regex="^(configure|overview|tech_stack|workflows|tasks)$")

class SmartSuggestionRequest(BaseModel):
    project_id: str
    context: Dict[str, Any]
    suggestion_type: str = Field(regex="^(task_optimization|dependency|priority|assignment)$")

class ProjectConfirmationRequest(BaseModel):
    project_id: str
    confirmation_data: Dict[str, Any]
    final_tasks: List[Dict[str, Any]]
    workflow: Dict[str, Any]

@router.post("/ai-preview", response_model=AIProjectPreviewResponse)
async def generate_ai_project_preview(
    request: AIProjectPreviewRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Generate AI project preview without creating the actual project"""
    # Check organization access
    org_member_result = await db.execute(
        select(OrganizationMember).where(
            OrganizationMember.organization_id == request.organization_id,
            OrganizationMember.user_id == current_user.id,
            OrganizationMember.role.in_(["owner"])  # Only owners can create AI projects
        )
    )
    org_member = org_member_result.scalar_one_or_none()
    if not org_member:
        raise InsufficientPermissionsError("Only organization owners can create AI projects")

    try:
        ai_service = AIService(db)
        preview_result = await ai_service.generate_ai_project_preview(
            project_name=request.name,
            organization_id=request.organization_id,
            user_id=str(current_user.id),
            project_type=request.project_type,
            team_size=request.team_size,
            team_experience=request.team_experience
        )

        return AIProjectPreviewResponse(**preview_result)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate AI project preview: {str(e)}")

@router.post("/ai-create")
async def create_ai_project_from_preview(
    request: ProjectConfirmationRequest,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Create actual AI project from confirmed preview data"""
    try:
        ai_service = AIService(db)
        
        # Create project with confirmed data
        result = await ai_service.create_project_from_confirmation(
            confirmation_data=request.confirmation_data,
            final_tasks=request.final_tasks,
            workflow=request.workflow,
            user_id=str(current_user.id)
        )

        # Schedule background tasks for integrations
        background_tasks.add_task(
            ai_service.setup_project_integrations,
            result["project"]["id"],
            request.workflow
        )

        return {
            "success": True,
            "data": result,
            "message": "AI project created successfully"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create AI project: {str(e)}")

@router.put("/tasks/bulk-update")
async def bulk_update_tasks(
    request: TaskBulkUpdateRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Bulk update multiple tasks"""
    try:
        # Validate access to all tasks
        cards_result = await db.execute(
            select(Card)
            .options(
                selectinload(Card.column)
                .selectinload(Column.board)
                .selectinload(Board.project)
            )
            .where(Card.id.in_(request.task_ids))
        )
        cards = cards_result.scalars().all()

        if len(cards) != len(request.task_ids):
            raise ResourceNotFoundError("Some tasks not found")

        # Check permissions for each task
        for card in cards:
            org_member_result = await db.execute(
                select(OrganizationMember).where(
                    OrganizationMember.organization_id == card.column.board.project.organization_id,
                    OrganizationMember.user_id == current_user.id
                )
            )
            org_member = org_member_result.scalar_one_or_none()
            if not org_member:
                raise InsufficientPermissionsError("Access denied to one or more tasks")

        # Perform bulk operation
        if request.operation == "update":
            await db.execute(
                update(Card)
                .where(Card.id.in_(request.task_ids))
                .values(**request.updates)
            )
        elif request.operation == "delete":
            await db.execute(
                delete(Card)
                .where(Card.id.in_(request.task_ids))
            )

        await db.commit()

        return {
            "success": True,
            "data": {
                "updated_count": len(request.task_ids),
                "operation": request.operation
            },
            "message": f"Successfully {request.operation}d {len(request.task_ids)} tasks"
        }

    except Exception as e:
        await db.rollback()
        raise HTTPException(status_code=500, detail=f"Bulk update failed: {str(e)}")

@router.post("/workflow/update")
async def update_project_workflow(
    request: ProjectWorkflowRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Update project workflow for specific phase"""
    try:
        # Get project and check access
        project_result = await db.execute(
            select(Project).where(Project.id == request.project_id)
        )
        project = project_result.scalar_one_or_none()
        if not project:
            raise ResourceNotFoundError("Project not found")

        # Check permissions
        org_member_result = await db.execute(
            select(OrganizationMember).where(
                OrganizationMember.organization_id == project.organization_id,
                OrganizationMember.user_id == current_user.id,
                OrganizationMember.role.in_(["owner", "admin"])
            )
        )
        org_member = org_member_result.scalar_one_or_none()
        if not org_member:
            raise InsufficientPermissionsError("Insufficient permissions to update workflow")

        ai_service = AIService(db)
        updated_workflow = await ai_service.update_project_workflow(
            project_id=request.project_id,
            phase=request.phase,
            workflow_data=request.workflow_data
        )

        return {
            "success": True,
            "data": updated_workflow,
            "message": f"Workflow updated for {request.phase} phase"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update workflow: {str(e)}")

@router.post("/suggestions/generate")
async def generate_smart_suggestions(
    request: SmartSuggestionRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Generate smart suggestions for project optimization"""
    try:
        # Get project and check access
        project_result = await db.execute(
            select(Project).where(Project.id == request.project_id)
        )
        project = project_result.scalar_one_or_none()
        if not project:
            raise ResourceNotFoundError("Project not found")

        # Check permissions
        org_member_result = await db.execute(
            select(OrganizationMember).where(
                OrganizationMember.organization_id == project.organization_id,
                OrganizationMember.user_id == current_user.id
            )
        )
        org_member = org_member_result.scalar_one_or_none()
        if not org_member:
            raise InsufficientPermissionsError("Access denied to project")

        ai_service = AIService(db)
        suggestions = await ai_service.generate_smart_suggestions(
            project_id=request.project_id,
            suggestion_type=request.suggestion_type,
            context=request.context
        )

        return {
            "success": True,
            "data": suggestions,
            "message": "Smart suggestions generated successfully"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to generate suggestions: {str(e)}")

@router.post("/suggestions/apply")
async def apply_smart_suggestion(
    suggestion_id: str,
    project_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Apply a smart suggestion to the project"""
    try:
        ai_service = AIService(db)
        result = await ai_service.apply_smart_suggestion(
            suggestion_id=suggestion_id,
            project_id=project_id,
            user_id=str(current_user.id)
        )

        return {
            "success": True,
            "data": result,
            "message": "Suggestion applied successfully"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to apply suggestion: {str(e)}")

@router.get("/templates")
async def get_project_templates(
    project_type: Optional[str] = None,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get available project templates"""
    try:
        ai_service = AIService(db)
        templates = await ai_service.get_project_templates(project_type=project_type)

        return {
            "success": True,
            "data": templates,
            "message": "Project templates retrieved successfully"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get templates: {str(e)}")

@router.get("/tech-stacks")
async def get_tech_stacks(
    project_type: Optional[str] = None,
    current_user: User = Depends(get_current_active_user)
):
    """Get available technology stacks for projects"""
    try:
        # This would typically come from a database or external service
        tech_stacks = {
            "web_application": [
                {"name": "React + Node.js", "technologies": ["React", "Node.js", "Express", "PostgreSQL"]},
                {"name": "Vue + Python", "technologies": ["Vue.js", "Python", "Django", "PostgreSQL"]},
                {"name": "Angular + .NET", "technologies": ["Angular", ".NET Core", "SQL Server"]}
            ],
            "mobile_app": [
                {"name": "React Native", "technologies": ["React Native", "Node.js", "MongoDB"]},
                {"name": "Flutter", "technologies": ["Flutter", "Dart", "Firebase"]},
                {"name": "Native iOS/Android", "technologies": ["Swift", "Kotlin", "REST APIs"]}
            ],
            "general": [
                {"name": "Modern Web Stack", "technologies": ["JavaScript", "HTML5", "CSS3", "Database"]},
                {"name": "Cloud Native", "technologies": ["Docker", "Kubernetes", "Microservices"]},
                {"name": "Data Analytics", "technologies": ["Python", "Pandas", "Jupyter", "SQL"]}
            ]
        }

        filtered_stacks = tech_stacks.get(project_type, tech_stacks["general"]) if project_type else tech_stacks

        return {
            "success": True,
            "data": filtered_stacks,
            "message": "Technology stacks retrieved successfully"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get tech stacks: {str(e)}")
