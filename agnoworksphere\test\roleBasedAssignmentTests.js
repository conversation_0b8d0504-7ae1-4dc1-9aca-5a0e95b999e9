/**
 * Comprehensive tests for role-based task assignment system
 * Run these tests to verify that role permissions work correctly
 */

import { 
  getRolePermissions, 
  canAssignTaskToUser, 
  getAssignableMembers,
  getAssignmentRestrictionMessage,
  canReceiveTaskAssignments,
  hasMinimumRole 
} from '../src/utils/rolePermissions.js';

import { 
  validateTaskAssignment,
  handleAIError,
  handlePermissionError,
  ERROR_TYPES 
} from '../src/utils/errorHandling.js';

import { generateAIChecklist, getSuggestedItems } from '../src/utils/aiChecklistService.js';

// Test data
const mockUsers = {
  viewer: { id: 'user1', role: 'viewer', name: '<PERSON>' },
  member: { id: 'user2', role: 'member', name: '<PERSON>' },
  admin: { id: 'user3', role: 'admin', name: '<PERSON> Admin' },
  owner: { id: 'user4', role: 'owner', name: '<PERSON>' }
};

const mockMembers = [
  { id: 'user1', name: '<PERSON>', role: 'viewer' },
  { id: 'user2', name: '<PERSON>', role: 'member' },
  { id: 'user3', name: '<PERSON> Admin', role: 'admin' },
  { id: 'user4', name: 'Alice Owner', role: 'owner' },
  { id: 'user5', name: 'Tom Developer', role: 'member' }
];

// Test suite for role permissions
const testRolePermissions = () => {
  console.log('🧪 Testing Role Permissions...');
  
  // Test 1: Viewer permissions
  const viewerPerms = getRolePermissions('viewer');
  console.assert(viewerPerms.canAssignTasksToSelf === true, 'Viewer should be able to assign to self');
  console.assert(viewerPerms.canAssignTasksToOthers === false, 'Viewer should not assign to others');
  console.assert(viewerPerms.assignmentScope === 'self', 'Viewer scope should be self');
  
  // Test 2: Member permissions
  const memberPerms = getRolePermissions('member');
  console.assert(memberPerms.canAssignTasksToSelf === true, 'Member should be able to assign to self');
  console.assert(memberPerms.canAssignTasksToOthers === false, 'Member should not assign to others');
  console.assert(memberPerms.assignmentScope === 'self', 'Member scope should be self');
  
  // Test 3: Admin permissions
  const adminPerms = getRolePermissions('admin');
  console.assert(adminPerms.canAssignTasksToOthers === true, 'Admin should assign to others');
  console.assert(adminPerms.assignmentScope === 'project', 'Admin scope should be project');
  
  // Test 4: Owner permissions
  const ownerPerms = getRolePermissions('owner');
  console.assert(ownerPerms.canAssignTasksToOthers === true, 'Owner should assign to others');
  console.assert(ownerPerms.assignmentScope === 'organization', 'Owner scope should be organization');
  
  console.log('✅ Role Permissions tests passed');
};

// Test suite for task assignment validation
const testTaskAssignmentValidation = () => {
  console.log('🧪 Testing Task Assignment Validation...');
  
  // Test 1: Viewer can only assign to self
  console.assert(
    canAssignTaskToUser('viewer', 'user1', 'user1') === true,
    'Viewer should assign to self'
  );
  console.assert(
    canAssignTaskToUser('viewer', 'user1', 'user2') === false,
    'Viewer should not assign to others'
  );
  
  // Test 2: Member can only assign to self
  console.assert(
    canAssignTaskToUser('member', 'user2', 'user2') === true,
    'Member should assign to self'
  );
  console.assert(
    canAssignTaskToUser('member', 'user2', 'user1') === false,
    'Member should not assign to others'
  );
  
  // Test 3: Admin can assign to others
  console.assert(
    canAssignTaskToUser('admin', 'user3', 'user1') === true,
    'Admin should assign to others'
  );
  console.assert(
    canAssignTaskToUser('admin', 'user3', 'user3') === true,
    'Admin should assign to self'
  );
  
  // Test 4: Owner can assign to anyone
  console.assert(
    canAssignTaskToUser('owner', 'user4', 'user1') === true,
    'Owner should assign to anyone'
  );
  
  console.log('✅ Task Assignment Validation tests passed');
};

// Test suite for assignable members filtering
const testAssignableMembersFiltering = () => {
  console.log('🧪 Testing Assignable Members Filtering...');
  
  // Test 1: Viewer gets only themselves
  const viewerAssignable = getAssignableMembers(mockMembers, 'viewer', 'user1');
  console.assert(
    viewerAssignable.length === 1 && viewerAssignable[0].id === 'user1',
    'Viewer should only see themselves'
  );
  
  // Test 2: Member gets only themselves
  const memberAssignable = getAssignableMembers(mockMembers, 'member', 'user2');
  console.assert(
    memberAssignable.length === 1 && memberAssignable[0].id === 'user2',
    'Member should only see themselves'
  );
  
  // Test 3: Admin gets all members
  const adminAssignable = getAssignableMembers(mockMembers, 'admin', 'user3');
  console.assert(
    adminAssignable.length === mockMembers.length,
    'Admin should see all members'
  );
  
  // Test 4: Owner gets all members
  const ownerAssignable = getAssignableMembers(mockMembers, 'owner', 'user4');
  console.assert(
    ownerAssignable.length === mockMembers.length,
    'Owner should see all members'
  );
  
  console.log('✅ Assignable Members Filtering tests passed');
};

// Test suite for error handling
const testErrorHandling = () => {
  console.log('🧪 Testing Error Handling...');
  
  // Test 1: Invalid assignment validation
  const viewerError = validateTaskAssignment('viewer', 'user2', 'user1');
  console.assert(
    viewerError !== null && viewerError.type === ERROR_TYPES.INVALID_ASSIGNMENT,
    'Should return error for invalid viewer assignment'
  );
  
  const memberError = validateTaskAssignment('member', 'user3', 'user2');
  console.assert(
    memberError !== null && memberError.type === ERROR_TYPES.INVALID_ASSIGNMENT,
    'Should return error for invalid member assignment'
  );
  
  // Test 2: Valid assignments should return null
  const validAssignment = validateTaskAssignment('admin', 'user2', 'user3');
  console.assert(
    validAssignment === null,
    'Valid assignment should return null'
  );
  
  // Test 3: AI error handling
  const aiError = handleAIError(new Error('Network timeout'), 'checklist');
  console.assert(
    aiError.type === ERROR_TYPES.AI_GENERATION_FAILED,
    'Should handle AI errors correctly'
  );
  
  console.log('✅ Error Handling tests passed');
};

// Test suite for AI checklist generation
const testAIChecklistGeneration = async () => {
  console.log('🧪 Testing AI Checklist Generation...');
  
  try {
    // Test 1: Basic checklist generation
    const result = await generateAIChecklist(
      'Implement user authentication',
      'Create login and registration system',
      'high',
      'development'
    );
    
    console.assert(result.success === true, 'AI generation should succeed');
    console.assert(result.items.length > 0, 'Should generate checklist items');
    console.assert(result.items.length <= 8, 'Should not exceed 8 items');
    
    // Test 2: Check item structure
    const firstItem = result.items[0];
    console.assert(firstItem.id !== undefined, 'Items should have IDs');
    console.assert(firstItem.text !== undefined, 'Items should have text');
    console.assert(firstItem.aiGenerated === true, 'Items should be marked as AI generated');
    console.assert(firstItem.confidence !== undefined, 'Items should have confidence scores');
    
    // Test 3: Suggested items
    const suggestions = getSuggestedItems('development');
    console.assert(suggestions.length > 0, 'Should provide suggested items');
    
    console.log('✅ AI Checklist Generation tests passed');
  } catch (error) {
    console.error('❌ AI Checklist Generation tests failed:', error);
  }
};

// Test suite for role hierarchy
const testRoleHierarchy = () => {
  console.log('🧪 Testing Role Hierarchy...');
  
  // Test minimum role requirements
  console.assert(hasMinimumRole('owner', 'admin') === true, 'Owner should meet admin requirement');
  console.assert(hasMinimumRole('admin', 'member') === true, 'Admin should meet member requirement');
  console.assert(hasMinimumRole('member', 'viewer') === true, 'Member should meet viewer requirement');
  console.assert(hasMinimumRole('viewer', 'member') === false, 'Viewer should not meet member requirement');
  
  // Test task assignment reception
  console.assert(canReceiveTaskAssignments('member') === true, 'Members should receive assignments');
  console.assert(canReceiveTaskAssignments('admin') === true, 'Admins should receive assignments');
  console.assert(canReceiveTaskAssignments('owner') === true, 'Owners should receive assignments');
  
  console.log('✅ Role Hierarchy tests passed');
};

// Test suite for restriction messages
const testRestrictionMessages = () => {
  console.log('🧪 Testing Restriction Messages...');
  
  const viewerMessage = getAssignmentRestrictionMessage('viewer');
  console.assert(
    viewerMessage.includes('Viewers can only assign tasks to themselves'),
    'Viewer message should be correct'
  );
  
  const memberMessage = getAssignmentRestrictionMessage('member');
  console.assert(
    memberMessage.includes('Members can only assign tasks to themselves'),
    'Member message should be correct'
  );
  
  const adminMessage = getAssignmentRestrictionMessage('admin');
  console.assert(
    adminMessage.includes('Admins can assign tasks to project team members'),
    'Admin message should be correct'
  );
  
  console.log('✅ Restriction Messages tests passed');
};

// Main test runner
export const runAllTests = async () => {
  console.log('🚀 Starting Role-Based Assignment System Tests...\n');
  
  try {
    testRolePermissions();
    testTaskAssignmentValidation();
    testAssignableMembersFiltering();
    testErrorHandling();
    await testAIChecklistGeneration();
    testRoleHierarchy();
    testRestrictionMessages();
    
    console.log('\n🎉 All tests passed! Role-based assignment system is working correctly.');
    return true;
  } catch (error) {
    console.error('\n❌ Tests failed:', error);
    return false;
  }
};

// Export for use in browser console or test runners
if (typeof window !== 'undefined') {
  window.runRoleBasedTests = runAllTests;
}

export default {
  runAllTests,
  testRolePermissions,
  testTaskAssignmentValidation,
  testAssignableMembersFiltering,
  testErrorHandling,
  testAIChecklistGeneration,
  testRoleHierarchy,
  testRestrictionMessages
};
