"""
Analytics and reporting API endpoints
"""
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import func, and_, or_, select
from typing import List, Optional, Dict, Any
from uuid import UUID
from datetime import datetime, timedelta
import json

from app.core.database import get_db
from app.core.deps import get_current_active_user
from app.models.user import User
from app.models.organization import Organization, OrganizationMember
from app.models.project import Project
from app.models.card import Card
from app.models.analytics import (
    AnalyticsReport, ReportExecution, DashboardWidget, 
    MetricSnapshot, DataExport, PerformanceMetric
)
from app.schemas.analytics import (
    AnalyticsReportCreate, AnalyticsReportResponse,
    DashboardWidgetCreate, DashboardWidgetResponse,
    DataExportCreate, DataExportResponse,
    OrganizationAnalytics, ProjectAnalytics, UserAnalytics
)

router = APIRouter()


@router.get("/dashboard/stats", response_model=dict)
async def get_dashboard_stats(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get dashboard statistics"""
    # Get user's organizations
    result = await db.execute(
        select(OrganizationMember).where(OrganizationMember.user_id == current_user.id)
    )
    user_orgs = result.scalars().all()

    if not user_orgs:
        return {
            "total_projects": 0,
            "total_boards": 0,
            "total_cards": 0,
            "organizations": 0,
            "completion_rate": 0
        }

    # Mock dashboard stats for now
    return {
        "total_projects": 8,
        "total_boards": 12,
        "total_cards": 45,
        "organizations": len(user_orgs),
        "completion_rate": 78.5,
        "active_projects": 6,
        "completed_tasks": 35,
        "pending_tasks": 10,
        "team_members": 15
    }


@router.get("", response_model=dict)
async def get_analytics_overview(
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get analytics overview for current user's organizations"""
    # Get user's organizations
    result = await db.execute(
        select(OrganizationMember).where(OrganizationMember.user_id == current_user.id)
    )
    user_orgs = result.scalars().all()

    if not user_orgs:
        return {
            "total_projects": 0,
            "total_tasks": 0,
            "completed_tasks": 0,
            "organizations": 0,
            "completion_rate": 0
        }

    # Get first organization for demo
    org_id = user_orgs[0].organization_id

    # Mock analytics data
    return {
        "total_projects": 5,
        "total_tasks": 25,
        "completed_tasks": 18,
        "organizations": len(user_orgs),
        "completion_rate": 72.0,
        "recent_activity": [
            {"type": "task_completed", "count": 3, "date": "2025-08-03"},
            {"type": "project_created", "count": 1, "date": "2025-08-02"},
            {"type": "task_created", "count": 5, "date": "2025-08-01"}
        ]
    }


@router.get("/projects/{project_id}", response_model=dict)
async def get_project_analytics(
    project_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get analytics for a specific project"""
    # Mock project analytics data
    return {
        "project_id": project_id,
        "total_tasks": 12,
        "completed_tasks": 8,
        "in_progress_tasks": 3,
        "pending_tasks": 1,
        "completion_rate": 66.7,
        "average_completion_time": "2.5 days",
        "team_members": 4,
        "recent_activity": [
            {"type": "task_completed", "count": 2, "date": "2025-08-03"},
            {"type": "task_created", "count": 1, "date": "2025-08-02"}
        ],
        "task_distribution": {
            "todo": 1,
            "in_progress": 3,
            "done": 8
        }
    }


@router.get("/organizations/{organization_id}/analytics/overview", response_model=OrganizationAnalytics)
async def get_organization_analytics(
    organization_id: UUID,
    date_range: Optional[str] = Query("30d", regex="^(7d|30d|90d|1y|all)$"),
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get comprehensive organization analytics"""
    # Check if user has access to this organization
    member = db.query(OrganizationMember).filter(
        OrganizationMember.organization_id == organization_id,
        OrganizationMember.user_id == current_user.id
    ).first()
    
    if not member:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to organization"
        )
    
    # Calculate date range
    end_date = datetime.utcnow()
    if date_range == "7d":
        start_date = end_date - timedelta(days=7)
    elif date_range == "30d":
        start_date = end_date - timedelta(days=30)
    elif date_range == "90d":
        start_date = end_date - timedelta(days=90)
    elif date_range == "1y":
        start_date = end_date - timedelta(days=365)
    else:
        start_date = None
    
    # Get basic counts
    total_members = db.query(OrganizationMember).filter(
        OrganizationMember.organization_id == organization_id
    ).count()
    
    total_projects = db.query(Project).filter(
        Project.organization_id == organization_id
    ).count()
    
    active_projects = db.query(Project).filter(
        Project.organization_id == organization_id,
        Project.status == 'active'
    ).count()
    
    # Get task statistics
    total_tasks = db.query(Card).join(Project).filter(
        Project.organization_id == organization_id
    ).count()
    
    completed_tasks = db.query(Card).join(Project).filter(
        Project.organization_id == organization_id,
        Card.status == 'completed'
    ).count()
    
    # Calculate completion rate
    completion_rate = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
    
    # Get member growth data
    member_growth = []
    if start_date:
        # Group members by join date
        growth_data = db.query(
            func.date_trunc('day', OrganizationMember.joined_at).label('date'),
            func.count(OrganizationMember.id).label('count')
        ).filter(
            OrganizationMember.organization_id == organization_id,
            OrganizationMember.joined_at >= start_date
        ).group_by(func.date_trunc('day', OrganizationMember.joined_at)).all()
        
        member_growth = [
            {"date": row.date.isoformat(), "count": row.count}
            for row in growth_data
        ]
    
    # Get project completion data
    project_completion_data = db.query(
        func.count(Project.id).label('total'),
        func.sum(func.case([(Project.status == 'completed', 1)], else_=0)).label('completed')
    ).filter(Project.organization_id == organization_id).first()
    
    project_completion_rate = 0
    if project_completion_data.total > 0:
        project_completion_rate = (project_completion_data.completed / project_completion_data.total * 100)
    
    # Calculate average task completion time
    avg_completion_time = None
    completed_tasks_with_time = db.query(
        func.avg(
            func.extract('epoch', Card.updated_at - Card.created_at) / 3600
        ).label('avg_hours')
    ).join(Project).filter(
        Project.organization_id == organization_id,
        Card.status == 'completed',
        Card.updated_at.isnot(None)
    ).first()
    
    if completed_tasks_with_time.avg_hours:
        avg_completion_time = float(completed_tasks_with_time.avg_hours)
    
    return OrganizationAnalytics(
        total_members=total_members,
        active_members=total_members,  # Assuming all members are active for now
        total_projects=total_projects,
        active_projects=active_projects,
        total_tasks=total_tasks,
        completed_tasks=completed_tasks,
        completion_rate=completion_rate,
        project_completion_rate=project_completion_rate,
        member_growth=member_growth,
        average_task_completion_time=avg_completion_time
    )


@router.get("/organizations/{organization_id}/analytics/projects/{project_id}", response_model=ProjectAnalytics)
async def get_project_analytics(
    organization_id: UUID,
    project_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get detailed project analytics"""
    # Check if user has access to this organization
    member = db.query(OrganizationMember).filter(
        OrganizationMember.organization_id == organization_id,
        OrganizationMember.user_id == current_user.id
    ).first()
    
    if not member:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to organization"
        )
    
    # Verify project belongs to organization
    project = db.query(Project).filter(
        Project.id == project_id,
        Project.organization_id == organization_id
    ).first()
    
    if not project:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Project not found"
        )
    
    # Get task statistics by status
    task_stats = db.query(
        Card.status,
        func.count(Card.id).label('count')
    ).filter(Card.project_id == project_id).group_by(Card.status).all()
    
    task_by_status = {row.status: row.count for row in task_stats}
    
    # Get task statistics by priority
    priority_stats = db.query(
        Card.priority,
        func.count(Card.id).label('count')
    ).filter(Card.project_id == project_id).group_by(Card.priority).all()
    
    task_by_priority = {row.priority: row.count for row in priority_stats}
    
    # Get assignee statistics
    assignee_stats = db.query(
        Card.assigned_to,
        func.count(Card.id).label('count')
    ).filter(
        Card.project_id == project_id,
        Card.assigned_to.isnot(None)
    ).group_by(Card.assigned_to).all()
    
    task_by_assignee = [
        {"user_id": str(row.assigned_to), "count": row.count}
        for row in assignee_stats
    ]
    
    # Calculate project progress
    total_tasks = sum(task_by_status.values())
    completed_tasks = task_by_status.get('completed', 0)
    progress_percentage = (completed_tasks / total_tasks * 100) if total_tasks > 0 else 0
    
    # Get overdue tasks
    overdue_tasks = db.query(Card).filter(
        Card.project_id == project_id,
        Card.due_date < datetime.utcnow(),
        Card.status != 'completed'
    ).count()
    
    return ProjectAnalytics(
        project_id=project_id,
        total_tasks=total_tasks,
        completed_tasks=completed_tasks,
        progress_percentage=progress_percentage,
        overdue_tasks=overdue_tasks,
        task_by_status=task_by_status,
        task_by_priority=task_by_priority,
        task_by_assignee=task_by_assignee,
        created_at=project.created_at,
        updated_at=project.updated_at
    )


@router.get("/organizations/{organization_id}/dashboard/widgets", response_model=List[DashboardWidgetResponse])
async def get_dashboard_widgets(
    organization_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get dashboard widgets for user"""
    # Check if user has access to this organization
    member = db.query(OrganizationMember).filter(
        OrganizationMember.organization_id == organization_id,
        OrganizationMember.user_id == current_user.id
    ).first()
    
    if not member:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to organization"
        )
    
    # Get user-specific and organization-wide widgets
    widgets = db.query(DashboardWidget).filter(
        DashboardWidget.organization_id == organization_id,
        or_(
            DashboardWidget.user_id == current_user.id,
            DashboardWidget.user_id.is_(None)
        ),
        DashboardWidget.is_visible == True
    ).order_by(DashboardWidget.position_y, DashboardWidget.position_x).all()
    
    return widgets


@router.post("/organizations/{organization_id}/dashboard/widgets", response_model=DashboardWidgetResponse)
async def create_dashboard_widget(
    organization_id: UUID,
    widget_data: DashboardWidgetCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new dashboard widget"""
    # Check if user has access to this organization
    member = db.query(OrganizationMember).filter(
        OrganizationMember.organization_id == organization_id,
        OrganizationMember.user_id == current_user.id
    ).first()
    
    if not member:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to organization"
        )
    
    widget = DashboardWidget(
        organization_id=organization_id,
        user_id=current_user.id,
        widget_type=widget_data.widget_type,
        widget_name=widget_data.widget_name,
        configuration=widget_data.configuration,
        position_x=widget_data.position_x,
        position_y=widget_data.position_y,
        width=widget_data.width,
        height=widget_data.height,
        refresh_interval=widget_data.refresh_interval
    )
    
    db.add(widget)
    db.commit()
    db.refresh(widget)
    
    return widget


@router.put("/organizations/{organization_id}/dashboard/widgets/{widget_id}")
async def update_dashboard_widget(
    organization_id: UUID,
    widget_id: UUID,
    widget_data: DashboardWidgetCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Update dashboard widget"""
    widget = db.query(DashboardWidget).filter(
        DashboardWidget.id == widget_id,
        DashboardWidget.organization_id == organization_id,
        DashboardWidget.user_id == current_user.id
    ).first()
    
    if not widget:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Widget not found"
        )
    
    # Update widget properties
    widget.widget_name = widget_data.widget_name
    widget.configuration = widget_data.configuration
    widget.position_x = widget_data.position_x
    widget.position_y = widget_data.position_y
    widget.width = widget_data.width
    widget.height = widget_data.height
    widget.refresh_interval = widget_data.refresh_interval
    
    db.commit()
    db.refresh(widget)
    
    return widget


@router.delete("/organizations/{organization_id}/dashboard/widgets/{widget_id}")
async def delete_dashboard_widget(
    organization_id: UUID,
    widget_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Delete dashboard widget"""
    widget = db.query(DashboardWidget).filter(
        DashboardWidget.id == widget_id,
        DashboardWidget.organization_id == organization_id,
        DashboardWidget.user_id == current_user.id
    ).first()
    
    if not widget:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Widget not found"
        )
    
    db.delete(widget)
    db.commit()
    
    return {"message": "Widget deleted successfully"}


@router.post("/organizations/{organization_id}/exports", response_model=DataExportResponse)
async def create_data_export(
    organization_id: UUID,
    export_data: DataExportCreate,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new data export"""
    # Check if user has access to this organization
    member = db.query(OrganizationMember).filter(
        OrganizationMember.organization_id == organization_id,
        OrganizationMember.user_id == current_user.id
    ).first()
    
    if not member:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to organization"
        )
    
    # Create export record
    export = DataExport(
        organization_id=organization_id,
        export_type=export_data.export_type,
        export_format=export_data.export_format,
        file_name=f"{export_data.export_type}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}.{export_data.export_format}",
        file_path="",  # Will be set by background task
        filters=export_data.filters,
        created_by=current_user.id
    )
    
    db.add(export)
    db.commit()
    db.refresh(export)
    
    # Start background export processing
    # background_tasks.add_task(process_data_export, export.id, db)
    
    return export


@router.get("/organizations/{organization_id}/exports", response_model=List[DataExportResponse])
async def get_data_exports(
    organization_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all data exports for organization"""
    # Check if user has access to this organization
    member = db.query(OrganizationMember).filter(
        OrganizationMember.organization_id == organization_id,
        OrganizationMember.user_id == current_user.id
    ).first()
    
    if not member:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to organization"
        )
    
    exports = db.query(DataExport).filter(
        DataExport.organization_id == organization_id
    ).order_by(DataExport.created_at.desc()).all()
    
    return exports
