"""
Meeting management endpoints for AI Task Management Modal
"""
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, BackgroundTasks
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from sqlalchemy.orm import selectinload
from pydantic import BaseModel, Field
from uuid import UUID
import uuid
from datetime import datetime, timedelta

from app.core.database import get_db
from app.core.deps import get_current_active_user
from app.core.exceptions import ValidationError, ResourceNotFoundError, InsufficientPermissionsError
from app.models.user import User
from app.models.organization import OrganizationMember
from app.models.project import Project

router = APIRouter()

# Pydantic models for meeting endpoints
class MeetingCreate(BaseModel):
    title: str = Field(..., min_length=1, max_length=200)
    description: Optional[str] = None
    project_id: str
    meeting_type: str = Field(regex="^(standup|planning|review|retrospective|custom)$")
    start_time: datetime
    duration: int = Field(ge=15, le=480)  # 15 minutes to 8 hours
    attendees: List[str] = []
    agenda: Optional[List[str]] = []
    is_recurring: bool = False
    recurrence_pattern: Optional[Dict[str, Any]] = None

class MeetingUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    start_time: Optional[datetime] = None
    duration: Optional[int] = None
    attendees: Optional[List[str]] = None
    agenda: Optional[List[str]] = None
    status: Optional[str] = None

class InstantMeetingRequest(BaseModel):
    project_id: str
    meeting_type: str
    title: str
    attendees: List[str] = []
    duration: int = Field(default=30, ge=15, le=120)

class CalendarIntegrationRequest(BaseModel):
    provider: str = Field(regex="^(google|microsoft|outlook)$")
    auth_code: str
    project_id: Optional[str] = None

class MeetingResponse(BaseModel):
    id: str
    title: str
    description: Optional[str]
    project_id: str
    meeting_type: str
    start_time: datetime
    duration: int
    attendees: List[Dict[str, Any]]
    agenda: List[str]
    status: str
    meeting_url: Optional[str]
    is_recurring: bool
    created_at: datetime
    created_by: str

# In-memory storage for demo (replace with database models)
meetings_db = {}
calendar_integrations_db = {}

@router.post("/create", response_model=MeetingResponse)
async def create_meeting(
    meeting_data: MeetingCreate,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a new meeting"""
    try:
        # Verify project access
        project_result = await db.execute(
            select(Project).where(Project.id == meeting_data.project_id)
        )
        project = project_result.scalar_one_or_none()
        if not project:
            raise ResourceNotFoundError("Project not found")

        # Check organization membership
        org_member_result = await db.execute(
            select(OrganizationMember).where(
                OrganizationMember.organization_id == project.organization_id,
                OrganizationMember.user_id == current_user.id
            )
        )
        org_member = org_member_result.scalar_one_or_none()
        if not org_member:
            raise InsufficientPermissionsError("Access denied to project")

        # Generate meeting ID and URL
        meeting_id = str(uuid.uuid4())
        meeting_url = await generate_meeting_url(meeting_data.meeting_type)

        # Create meeting record
        meeting = {
            "id": meeting_id,
            "title": meeting_data.title,
            "description": meeting_data.description,
            "project_id": meeting_data.project_id,
            "meeting_type": meeting_data.meeting_type,
            "start_time": meeting_data.start_time,
            "duration": meeting_data.duration,
            "attendees": await get_attendee_details(meeting_data.attendees, db),
            "agenda": meeting_data.agenda or [],
            "status": "scheduled",
            "meeting_url": meeting_url,
            "is_recurring": meeting_data.is_recurring,
            "recurrence_pattern": meeting_data.recurrence_pattern,
            "created_at": datetime.utcnow(),
            "created_by": str(current_user.id),
            "organization_id": project.organization_id
        }

        meetings_db[meeting_id] = meeting

        # Schedule notifications
        background_tasks.add_task(
            send_meeting_notifications,
            meeting_id,
            meeting_data.attendees,
            "created"
        )

        # Create calendar events if integrations exist
        background_tasks.add_task(
            create_calendar_events,
            meeting_id,
            project.organization_id
        )

        return MeetingResponse(**meeting)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create meeting: {str(e)}")

@router.post("/instant", response_model=MeetingResponse)
async def create_instant_meeting(
    meeting_data: InstantMeetingRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Create and start an instant meeting"""
    try:
        # Verify project access
        project_result = await db.execute(
            select(Project).where(Project.id == meeting_data.project_id)
        )
        project = project_result.scalar_one_or_none()
        if not project:
            raise ResourceNotFoundError("Project not found")

        # Generate instant meeting
        meeting_id = str(uuid.uuid4())
        meeting_url = await generate_instant_meeting_url()

        meeting = {
            "id": meeting_id,
            "title": meeting_data.title,
            "description": f"Instant {meeting_data.meeting_type} meeting",
            "project_id": meeting_data.project_id,
            "meeting_type": meeting_data.meeting_type,
            "start_time": datetime.utcnow(),
            "duration": meeting_data.duration,
            "attendees": await get_attendee_details(meeting_data.attendees, db),
            "agenda": [],
            "status": "in_progress",
            "meeting_url": meeting_url,
            "is_recurring": False,
            "created_at": datetime.utcnow(),
            "created_by": str(current_user.id),
            "organization_id": project.organization_id
        }

        meetings_db[meeting_id] = meeting

        # Send instant notifications
        await send_instant_meeting_notifications(meeting_url, meeting_data.attendees)

        return MeetingResponse(**meeting)

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to create instant meeting: {str(e)}")

@router.get("/project/{project_id}")
async def get_project_meetings(
    project_id: str,
    status: Optional[str] = None,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get meetings for a project"""
    try:
        # Verify project access
        project_result = await db.execute(
            select(Project).where(Project.id == project_id)
        )
        project = project_result.scalar_one_or_none()
        if not project:
            raise ResourceNotFoundError("Project not found")

        # Filter meetings
        project_meetings = [
            meeting for meeting in meetings_db.values()
            if meeting["project_id"] == project_id
        ]

        if status:
            project_meetings = [
                meeting for meeting in project_meetings
                if meeting["status"] == status
            ]

        return {
            "success": True,
            "data": project_meetings,
            "message": f"Retrieved {len(project_meetings)} meetings"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to get meetings: {str(e)}")

@router.put("/{meeting_id}")
async def update_meeting(
    meeting_id: str,
    meeting_data: MeetingUpdate,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Update a meeting"""
    try:
        if meeting_id not in meetings_db:
            raise ResourceNotFoundError("Meeting not found")

        meeting = meetings_db[meeting_id]

        # Check permissions
        if meeting["created_by"] != str(current_user.id):
            # Check if user is admin/owner of organization
            org_member_result = await db.execute(
                select(OrganizationMember).where(
                    OrganizationMember.organization_id == meeting["organization_id"],
                    OrganizationMember.user_id == current_user.id,
                    OrganizationMember.role.in_(["owner", "admin"])
                )
            )
            org_member = org_member_result.scalar_one_or_none()
            if not org_member:
                raise InsufficientPermissionsError("Insufficient permissions to update meeting")

        # Update meeting
        update_data = meeting_data.dict(exclude_unset=True)
        meeting.update(update_data)
        meetings_db[meeting_id] = meeting

        # Notify attendees of changes
        background_tasks.add_task(
            send_meeting_notifications,
            meeting_id,
            [attendee["id"] for attendee in meeting["attendees"]],
            "updated"
        )

        return {
            "success": True,
            "data": meeting,
            "message": "Meeting updated successfully"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to update meeting: {str(e)}")

@router.delete("/{meeting_id}")
async def delete_meeting(
    meeting_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Delete a meeting"""
    try:
        if meeting_id not in meetings_db:
            raise ResourceNotFoundError("Meeting not found")

        meeting = meetings_db[meeting_id]

        # Check permissions
        if meeting["created_by"] != str(current_user.id):
            org_member_result = await db.execute(
                select(OrganizationMember).where(
                    OrganizationMember.organization_id == meeting["organization_id"],
                    OrganizationMember.user_id == current_user.id,
                    OrganizationMember.role.in_(["owner", "admin"])
                )
            )
            org_member = org_member_result.scalar_one_or_none()
            if not org_member:
                raise InsufficientPermissionsError("Insufficient permissions to delete meeting")

        # Delete meeting
        del meetings_db[meeting_id]

        return {
            "success": True,
            "message": "Meeting deleted successfully"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to delete meeting: {str(e)}")

@router.post("/calendar/integrate")
async def integrate_calendar(
    integration_data: CalendarIntegrationRequest,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Integrate with external calendar provider"""
    try:
        # This would typically involve OAuth flow with the calendar provider
        integration_id = str(uuid.uuid4())
        
        integration = {
            "id": integration_id,
            "user_id": str(current_user.id),
            "provider": integration_data.provider,
            "status": "connected",
            "created_at": datetime.utcnow(),
            "access_token": "encrypted_token_here",  # Would be encrypted
            "refresh_token": "encrypted_refresh_token_here"
        }

        calendar_integrations_db[integration_id] = integration

        return {
            "success": True,
            "data": integration,
            "message": f"Successfully integrated with {integration_data.provider}"
        }

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Failed to integrate calendar: {str(e)}")

# Helper functions
async def generate_meeting_url(meeting_type: str) -> str:
    """Generate meeting URL based on type"""
    # This would integrate with actual video conferencing APIs
    base_urls = {
        "standup": "https://meet.google.com/",
        "planning": "https://zoom.us/j/",
        "review": "https://teams.microsoft.com/",
        "retrospective": "https://meet.google.com/",
        "custom": "https://meet.google.com/"
    }
    
    base_url = base_urls.get(meeting_type, "https://meet.google.com/")
    room_id = str(uuid.uuid4())[:10]
    return f"{base_url}{room_id}"

async def generate_instant_meeting_url() -> str:
    """Generate instant meeting URL"""
    room_id = str(uuid.uuid4())[:10]
    return f"https://meet.google.com/{room_id}"

async def get_attendee_details(attendee_ids: List[str], db: AsyncSession) -> List[Dict[str, Any]]:
    """Get attendee details from user IDs"""
    if not attendee_ids:
        return []
    
    users_result = await db.execute(
        select(User).where(User.id.in_(attendee_ids))
    )
    users = users_result.scalars().all()
    
    return [
        {
            "id": str(user.id),
            "name": f"{user.first_name} {user.last_name}",
            "email": user.email,
            "avatar": user.avatar_url
        }
        for user in users
    ]

async def send_meeting_notifications(meeting_id: str, attendee_ids: List[str], action: str):
    """Send meeting notifications to attendees"""
    # This would integrate with notification service
    print(f"Sending {action} notifications for meeting {meeting_id} to {len(attendee_ids)} attendees")

async def send_instant_meeting_notifications(meeting_url: str, attendee_ids: List[str]):
    """Send instant meeting notifications"""
    # This would send immediate notifications
    print(f"Sending instant meeting notifications to {len(attendee_ids)} attendees: {meeting_url}")

async def create_calendar_events(meeting_id: str, organization_id: str):
    """Create calendar events for integrated calendars"""
    # This would create events in external calendars
    print(f"Creating calendar events for meeting {meeting_id} in organization {organization_id}")
