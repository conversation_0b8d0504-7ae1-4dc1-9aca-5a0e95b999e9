"""
Team management endpoints
"""
from typing import List, Optional
from fastapi import APIRouter, Depends, Query
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy.orm import selectinload

from app.core.database import get_db
from app.core.deps import get_current_active_user, require_admin, require_member
from app.core.exceptions import ResourceNotFoundError, InsufficientPermissionsError, ValidationError
from app.models.user import User
from app.models.organization import OrganizationMember
from app.schemas.organization import OrganizationMemberResponse

router = APIRouter()


@router.get("", response_model=List[dict])
async def get_teams(
    organization_id: str = Query(..., description="Organization ID to filter teams"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get all teams for an organization"""
    # Check organization membership
    org_member_result = await db.execute(
        select(OrganizationMember).where(
            OrganizationMember.organization_id == organization_id,
            OrganizationMember.user_id == current_user.id
        )
    )
    if not org_member_result.scalar_one_or_none():
        raise InsufficientPermissionsError("Access denied")

    # Mock teams data for now
    teams = [
        {
            "id": "team-1",
            "name": "Development Team",
            "description": "Frontend and backend developers",
            "member_count": 8,
            "organization_id": organization_id
        },
        {
            "id": "team-2",
            "name": "Design Team",
            "description": "UI/UX designers and researchers",
            "member_count": 4,
            "organization_id": organization_id
        }
    ]

    return teams


@router.post("", response_model=dict)
async def create_team(
    team_data: dict,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Create a new team"""
    # Mock team creation
    team = {
        "id": f"team-{len(team_data.get('name', 'new'))}",
        "name": team_data.get("name", "New Team"),
        "description": team_data.get("description", ""),
        "member_count": 1,
        "organization_id": team_data.get("organization_id"),
        "created_by": str(current_user.id)
    }

    return team


@router.get("/{team_id}", response_model=dict)
async def get_team(
    team_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Get team by ID"""
    # Mock team data
    team = {
        "id": team_id,
        "name": "Development Team",
        "description": "Frontend and backend developers",
        "member_count": 8,
        "members": [
            {
                "id": str(current_user.id),
                "email": current_user.email,
                "first_name": current_user.first_name,
                "last_name": current_user.last_name,
                "role": "member"
            }
        ],
        "created_at": "2025-08-01T00:00:00Z",
        "updated_at": "2025-08-03T00:00:00Z"
    }

    return team


@router.get("/{org_id}/members", response_model=List[OrganizationMemberResponse])
async def get_team_members(
    org_id: str,
    search: Optional[str] = Query(None),
    role: Optional[str] = Query(None),
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    current_user: User = Depends(get_current_active_user),
    org_member: OrganizationMember = Depends(require_member),
    db: AsyncSession = Depends(get_db)
):
    """Get team members with search and filters"""
    offset = (page - 1) * limit
    
    query = select(OrganizationMember).options(
        selectinload(OrganizationMember.user)
    ).where(OrganizationMember.organization_id == org_id)
    
    # Apply filters
    if role:
        query = query.where(OrganizationMember.role == role)
    
    if search:
        # Search in user's name and email
        query = query.join(User).where(
            (User.first_name.ilike(f"%{search}%")) |
            (User.last_name.ilike(f"%{search}%")) |
            (User.email.ilike(f"%{search}%"))
        )
    
    query = query.offset(offset).limit(limit).order_by(OrganizationMember.joined_at)
    
    result = await db.execute(query)
    members = result.scalars().all()
    
    # Format response
    response = []
    for member in members:
        member_data = OrganizationMemberResponse.from_orm(member)
        member_data.user = {
            "id": str(member.user.id),
            "email": member.user.email,
            "first_name": member.user.first_name,
            "last_name": member.user.last_name,
            "avatar_url": member.user.avatar_url
        }
        response.append(member_data)
    
    return response


@router.get("/{org_id}/members/{user_id}/activity")
async def get_member_activity(
    org_id: str,
    user_id: str,
    page: int = Query(1, ge=1),
    limit: int = Query(20, ge=1, le=100),
    current_user: User = Depends(get_current_active_user),
    org_member: OrganizationMember = Depends(require_member),
    db: AsyncSession = Depends(get_db)
):
    """Get activity for a team member"""
    # Check if target user is member of organization
    target_member_result = await db.execute(
        select(OrganizationMember).where(
            OrganizationMember.organization_id == org_id,
            OrganizationMember.user_id == user_id
        )
    )
    if not target_member_result.scalar_one_or_none():
        raise ResourceNotFoundError("User is not a member of this organization")
    
    # TODO: Get activity logs from database
    # For now, return empty list
    return {
        "success": True,
        "data": {
            "items": [],
            "pagination": {
                "page": page,
                "limit": limit,
                "total": 0,
                "totalPages": 0,
                "hasNext": False,
                "hasPrev": False
            }
        }
    }


@router.post("/{org_id}/members/bulk-action")
async def bulk_member_action(
    org_id: str,
    action: str,
    member_ids: List[str],
    current_user: User = Depends(get_current_active_user),
    org_member: OrganizationMember = Depends(require_admin),
    db: AsyncSession = Depends(get_db)
):
    """Perform bulk actions on team members"""
    if action not in ['remove', 'change_role']:
        raise ValidationError("Invalid action. Must be 'remove' or 'change_role'")
    
    # TODO: Implement bulk actions
    # For now, just return success
    return {
        "success": True,
        "message": f"Bulk action '{action}' completed for {len(member_ids)} members"
    }
