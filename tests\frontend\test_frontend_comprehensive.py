#!/usr/bin/env python3
"""
Comprehensive Frontend Testing Suite for Agno WorkSphere
Tests frontend functionality, navigation, UI components, and integration
"""
import time
import json
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from dataclasses import dataclass
from typing import List, Optional, Dict, Any
import sys

@dataclass
class FrontendTestResult:
    test_name: str
    status: str  # PASS, FAIL, SKIP
    execution_time: float
    error_message: Optional[str] = None
    screenshot_path: Optional[str] = None

class FrontendTester:
    def __init__(self, headless: bool = True):
        self.results: List[FrontendTestResult] = []
        self.driver: Optional[webdriver.Chrome] = None
        self.base_url = "http://localhost:3000"
        self.wait_timeout = 10
        self.headless = headless
        self.test_user_email = f"frontend_test_{int(time.time())}@testcompany.com"
        self.test_password = "FrontendTest123!"
        
    def setup_driver(self):
        """Setup Chrome WebDriver"""
        chrome_options = Options()
        if self.headless:
            chrome_options.add_argument("--headless")
        chrome_options.add_argument("--no-sandbox")
        chrome_options.add_argument("--disable-dev-shm-usage")
        chrome_options.add_argument("--disable-gpu")
        chrome_options.add_argument("--window-size=1920,1080")
        
        try:
            self.driver = webdriver.Chrome(options=chrome_options)
            self.driver.implicitly_wait(5)
            return True
        except Exception as e:
            print(f"❌ Failed to setup Chrome driver: {e}")
            print("💡 Make sure ChromeDriver is installed and in PATH")
            return False
    
    def teardown_driver(self):
        """Cleanup WebDriver"""
        if self.driver:
            self.driver.quit()
    
    def log_result(self, result: FrontendTestResult):
        """Log test result"""
        self.results.append(result)
        status_emoji = "✅" if result.status == "PASS" else "❌" if result.status == "FAIL" else "⏭️"
        print(f"{status_emoji} {result.test_name} - {result.status} ({result.execution_time:.2f}s)")
        if result.error_message:
            print(f"   Error: {result.error_message}")
    
    def take_screenshot(self, name: str) -> Optional[str]:
        """Take screenshot for debugging"""
        if not self.driver:
            return None
        
        try:
            filename = f"screenshot_{name}_{int(time.time())}.png"
            self.driver.save_screenshot(filename)
            return filename
        except Exception as e:
            print(f"Failed to take screenshot: {e}")
            return None
    
    def wait_for_element(self, by: By, value: str, timeout: int = None) -> bool:
        """Wait for element to be present"""
        if not timeout:
            timeout = self.wait_timeout
        
        try:
            WebDriverWait(self.driver, timeout).until(
                EC.presence_of_element_located((by, value))
            )
            return True
        except TimeoutException:
            return False
    
    def wait_for_clickable(self, by: By, value: str, timeout: int = None) -> bool:
        """Wait for element to be clickable"""
        if not timeout:
            timeout = self.wait_timeout
        
        try:
            WebDriverWait(self.driver, timeout).until(
                EC.element_to_be_clickable((by, value))
            )
            return True
        except TimeoutException:
            return False
    
    def test_page_load(self):
        """Test basic page loading"""
        start_time = time.time()
        
        try:
            self.driver.get(self.base_url)
            
            # Wait for page to load
            if self.wait_for_element(By.TAG_NAME, "body"):
                # Check if React app loaded
                if self.wait_for_element(By.ID, "root", timeout=15):
                    execution_time = time.time() - start_time
                    self.log_result(FrontendTestResult(
                        test_name="Page Load",
                        status="PASS",
                        execution_time=execution_time
                    ))
                    return True
                else:
                    execution_time = time.time() - start_time
                    self.log_result(FrontendTestResult(
                        test_name="Page Load",
                        status="FAIL",
                        execution_time=execution_time,
                        error_message="React root element not found"
                    ))
                    return False
            else:
                execution_time = time.time() - start_time
                self.log_result(FrontendTestResult(
                    test_name="Page Load",
                    status="FAIL",
                    execution_time=execution_time,
                    error_message="Page body not loaded"
                ))
                return False
                
        except Exception as e:
            execution_time = time.time() - start_time
            self.log_result(FrontendTestResult(
                test_name="Page Load",
                status="FAIL",
                execution_time=execution_time,
                error_message=str(e)
            ))
            return False
    
    def test_navigation_elements(self):
        """Test navigation elements are present"""
        start_time = time.time()
        
        try:
            # Look for common navigation elements
            nav_elements = [
                ("Navigation Bar", "nav"),
                ("Header", "header"),
                ("Main Content", "main"),
                ("Footer", "footer")
            ]
            
            found_elements = []
            missing_elements = []
            
            for name, tag in nav_elements:
                try:
                    element = self.driver.find_element(By.TAG_NAME, tag)
                    if element.is_displayed():
                        found_elements.append(name)
                    else:
                        missing_elements.append(f"{name} (hidden)")
                except NoSuchElementException:
                    missing_elements.append(f"{name} (not found)")
            
            execution_time = time.time() - start_time
            
            if len(found_elements) >= 2:  # At least 2 navigation elements should be present
                self.log_result(FrontendTestResult(
                    test_name="Navigation Elements",
                    status="PASS",
                    execution_time=execution_time
                ))
                return True
            else:
                self.log_result(FrontendTestResult(
                    test_name="Navigation Elements",
                    status="FAIL",
                    execution_time=execution_time,
                    error_message=f"Missing elements: {', '.join(missing_elements)}"
                ))
                return False
                
        except Exception as e:
            execution_time = time.time() - start_time
            self.log_result(FrontendTestResult(
                test_name="Navigation Elements",
                status="FAIL",
                execution_time=execution_time,
                error_message=str(e)
            ))
            return False
    
    def test_responsive_design(self):
        """Test responsive design at different screen sizes"""
        start_time = time.time()
        
        try:
            screen_sizes = [
                ("Desktop", 1920, 1080),
                ("Tablet", 768, 1024),
                ("Mobile", 375, 667)
            ]
            
            responsive_issues = []
            
            for size_name, width, height in screen_sizes:
                self.driver.set_window_size(width, height)
                time.sleep(1)  # Allow time for responsive changes
                
                # Check if page is still functional
                body = self.driver.find_element(By.TAG_NAME, "body")
                if not body.is_displayed():
                    responsive_issues.append(f"{size_name}: Body not displayed")
                
                # Check for horizontal scrollbar (might indicate responsive issues)
                scroll_width = self.driver.execute_script("return document.body.scrollWidth")
                client_width = self.driver.execute_script("return document.body.clientWidth")
                
                if scroll_width > client_width + 20:  # Allow small tolerance
                    responsive_issues.append(f"{size_name}: Horizontal overflow detected")
            
            execution_time = time.time() - start_time
            
            if not responsive_issues:
                self.log_result(FrontendTestResult(
                    test_name="Responsive Design",
                    status="PASS",
                    execution_time=execution_time
                ))
                return True
            else:
                self.log_result(FrontendTestResult(
                    test_name="Responsive Design",
                    status="FAIL",
                    execution_time=execution_time,
                    error_message="; ".join(responsive_issues)
                ))
                return False
                
        except Exception as e:
            execution_time = time.time() - start_time
            self.log_result(FrontendTestResult(
                test_name="Responsive Design",
                status="FAIL",
                execution_time=execution_time,
                error_message=str(e)
            ))
            return False
        finally:
            # Reset to desktop size
            self.driver.set_window_size(1920, 1080)
    
    def test_authentication_flow(self):
        """Test authentication user interface"""
        start_time = time.time()
        
        try:
            # Look for login/register elements
            auth_elements = []
            
            # Common selectors for auth elements
            selectors = [
                ("Login Button", "button", "login"),
                ("Register Button", "button", "register"),
                ("Sign In Button", "button", "sign"),
                ("Email Input", "input", "email"),
                ("Password Input", "input", "password"),
                ("Login Form", "form", ""),
                ("Auth Container", "div", "auth")
            ]
            
            for name, tag, text_contains in selectors:
                try:
                    if text_contains:
                        elements = self.driver.find_elements(By.TAG_NAME, tag)
                        for element in elements:
                            if text_contains.lower() in element.text.lower() or \
                               text_contains.lower() in element.get_attribute("placeholder", "").lower() or \
                               text_contains.lower() in element.get_attribute("type", "").lower():
                                auth_elements.append(name)
                                break
                    else:
                        element = self.driver.find_element(By.TAG_NAME, tag)
                        if element:
                            auth_elements.append(name)
                except NoSuchElementException:
                    pass
            
            execution_time = time.time() - start_time
            
            if len(auth_elements) >= 2:  # Should have at least some auth elements
                self.log_result(FrontendTestResult(
                    test_name="Authentication UI",
                    status="PASS",
                    execution_time=execution_time
                ))
                return True
            else:
                self.log_result(FrontendTestResult(
                    test_name="Authentication UI",
                    status="FAIL",
                    execution_time=execution_time,
                    error_message=f"Found auth elements: {', '.join(auth_elements) if auth_elements else 'None'}"
                ))
                return False
                
        except Exception as e:
            execution_time = time.time() - start_time
            self.log_result(FrontendTestResult(
                test_name="Authentication UI",
                status="FAIL",
                execution_time=execution_time,
                error_message=str(e)
            ))
            return False

    def test_console_errors(self):
        """Test for JavaScript console errors"""
        start_time = time.time()

        try:
            # Get browser logs
            logs = self.driver.get_log('browser')

            # Filter for errors
            errors = [log for log in logs if log['level'] == 'SEVERE']
            warnings = [log for log in logs if log['level'] == 'WARNING']

            execution_time = time.time() - start_time

            if not errors:
                self.log_result(FrontendTestResult(
                    test_name="Console Errors",
                    status="PASS",
                    execution_time=execution_time
                ))
                return True
            else:
                error_messages = [error['message'][:100] for error in errors[:3]]  # First 3 errors
                self.log_result(FrontendTestResult(
                    test_name="Console Errors",
                    status="FAIL",
                    execution_time=execution_time,
                    error_message=f"{len(errors)} errors found: {'; '.join(error_messages)}"
                ))
                return False

        except Exception as e:
            execution_time = time.time() - start_time
            self.log_result(FrontendTestResult(
                test_name="Console Errors",
                status="FAIL",
                execution_time=execution_time,
                error_message=str(e)
            ))
            return False

    def test_performance_metrics(self):
        """Test basic performance metrics"""
        start_time = time.time()

        try:
            # Get navigation timing
            nav_timing = self.driver.execute_script("""
                return {
                    loadEventEnd: performance.timing.loadEventEnd,
                    navigationStart: performance.timing.navigationStart,
                    domContentLoaded: performance.timing.domContentLoadedEventEnd,
                    firstPaint: performance.getEntriesByType('paint')[0] ?
                               performance.getEntriesByType('paint')[0].startTime : null
                }
            """)

            load_time = (nav_timing['loadEventEnd'] - nav_timing['navigationStart']) / 1000
            dom_ready_time = (nav_timing['domContentLoaded'] - nav_timing['navigationStart']) / 1000

            execution_time = time.time() - start_time

            # Performance thresholds
            if load_time < 5.0 and dom_ready_time < 3.0:  # Reasonable thresholds
                self.log_result(FrontendTestResult(
                    test_name="Performance Metrics",
                    status="PASS",
                    execution_time=execution_time
                ))
                return True
            else:
                self.log_result(FrontendTestResult(
                    test_name="Performance Metrics",
                    status="FAIL",
                    execution_time=execution_time,
                    error_message=f"Load time: {load_time:.2f}s, DOM ready: {dom_ready_time:.2f}s"
                ))
                return False

        except Exception as e:
            execution_time = time.time() - start_time
            self.log_result(FrontendTestResult(
                test_name="Performance Metrics",
                status="FAIL",
                execution_time=execution_time,
                error_message=str(e)
            ))
            return False

    def test_accessibility_basics(self):
        """Test basic accessibility features"""
        start_time = time.time()

        try:
            accessibility_issues = []

            # Check for alt text on images
            images = self.driver.find_elements(By.TAG_NAME, "img")
            images_without_alt = [img for img in images if not img.get_attribute("alt")]
            if images_without_alt:
                accessibility_issues.append(f"{len(images_without_alt)} images without alt text")

            # Check for form labels
            inputs = self.driver.find_elements(By.TAG_NAME, "input")
            inputs_without_labels = []
            for input_elem in inputs:
                input_id = input_elem.get_attribute("id")
                aria_label = input_elem.get_attribute("aria-label")
                placeholder = input_elem.get_attribute("placeholder")

                if not input_id and not aria_label and not placeholder:
                    inputs_without_labels.append(input_elem)

            if inputs_without_labels:
                accessibility_issues.append(f"{len(inputs_without_labels)} inputs without labels")

            # Check for heading structure
            headings = self.driver.find_elements(By.CSS_SELECTOR, "h1, h2, h3, h4, h5, h6")
            if not headings:
                accessibility_issues.append("No heading elements found")

            execution_time = time.time() - start_time

            if not accessibility_issues:
                self.log_result(FrontendTestResult(
                    test_name="Accessibility Basics",
                    status="PASS",
                    execution_time=execution_time
                ))
                return True
            else:
                self.log_result(FrontendTestResult(
                    test_name="Accessibility Basics",
                    status="FAIL",
                    execution_time=execution_time,
                    error_message="; ".join(accessibility_issues)
                ))
                return False

        except Exception as e:
            execution_time = time.time() - start_time
            self.log_result(FrontendTestResult(
                test_name="Accessibility Basics",
                status="FAIL",
                execution_time=execution_time,
                error_message=str(e)
            ))
            return False

    def run_all_tests(self):
        """Run all frontend tests"""
        print("🚀 Starting Comprehensive Frontend Testing Suite")
        print("=" * 60)

        if not self.setup_driver():
            print("❌ Failed to setup WebDriver. Exiting.")
            return

        start_time = time.time()

        try:
            # Run all test methods
            self.test_page_load()
            self.test_navigation_elements()
            self.test_responsive_design()
            self.test_authentication_flow()
            self.test_console_errors()
            self.test_performance_metrics()
            self.test_accessibility_basics()

        except Exception as e:
            print(f"\n💥 Frontend test suite crashed: {e}")
            import traceback
            traceback.print_exc()

        finally:
            self.teardown_driver()

        total_time = time.time() - start_time
        self.print_summary(total_time)

    def print_summary(self, total_time: float):
        """Print test summary"""
        print("\n" + "=" * 60)
        print("📊 FRONTEND TEST SUMMARY")
        print("=" * 60)

        total_tests = len(self.results)
        passed_tests = len([r for r in self.results if r.status == "PASS"])
        failed_tests = len([r for r in self.results if r.status == "FAIL"])
        skipped_tests = len([r for r in self.results if r.status == "SKIP"])

        print(f"Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"⏭️ Skipped: {skipped_tests}")
        print(f"⏱️ Total Time: {total_time:.2f}s")
        print(f"📈 Success Rate: {(passed_tests/total_tests*100):.1f}%" if total_tests > 0 else "N/A")

        if failed_tests > 0:
            print(f"\n❌ FAILED TESTS:")
            for result in self.results:
                if result.status == "FAIL":
                    print(f"   {result.test_name} - {result.error_message}")

        print(f"\n🌐 Test Environment:")
        print(f"   Frontend URL: {self.base_url}")
        print(f"   Headless Mode: {self.headless}")

        # Save results to file
        self.save_results_to_file()

    def save_results_to_file(self):
        """Save test results to JSON file"""
        results_data = {
            "timestamp": time.time(),
            "frontend_url": self.base_url,
            "headless_mode": self.headless,
            "summary": {
                "total": len(self.results),
                "passed": len([r for r in self.results if r.status == "PASS"]),
                "failed": len([r for r in self.results if r.status == "FAIL"]),
                "skipped": len([r for r in self.results if r.status == "SKIP"])
            },
            "results": [
                {
                    "test_name": r.test_name,
                    "status": r.status,
                    "execution_time": r.execution_time,
                    "error_message": r.error_message,
                    "screenshot_path": r.screenshot_path
                }
                for r in self.results
            ]
        }

        filename = f"frontend_test_results_{int(time.time())}.json"
        with open(filename, 'w') as f:
            json.dump(results_data, f, indent=2)

        print(f"\n💾 Detailed results saved to: {filename}")

if __name__ == "__main__":
    # Check if headless mode should be disabled
    headless = "--no-headless" not in sys.argv

    tester = FrontendTester(headless=headless)
    tester.run_all_tests()
