#!/usr/bin/env python3
"""
Complete test of the login flow and organization data
"""

import requests
import json

def test_complete_flow():
    """Test the complete login flow and verify organization data"""
    base_url = "http://localhost:3001"
    
    print("🧪 Testing complete authentication and organization flow...")
    
    # Step 1: Login
    login_data = {
        "email": "<EMAIL>",
        "password": "Owner123!"
    }
    
    print("\n1️⃣ Testing login...")
    login_response = requests.post(f"{base_url}/api/v1/auth/login", json=login_data)
    
    if login_response.status_code != 200:
        print(f"❌ Login failed: {login_response.status_code}")
        print(login_response.text)
        return False
    
    login_result = login_response.json()
    print("✅ Login successful!")
    
    # Extract token
    if 'data' in login_result and 'tokens' in login_result['data']:
        token = login_result['data']['tokens']['access_token']
        print(f"🎫 Token obtained: {token[:20]}...")
    else:
        print("❌ No token found in login response")
        return False
    
    # Step 2: Get current user
    print("\n2️⃣ Testing get current user...")
    headers = {"Authorization": f"Bearer {token}"}
    user_response = requests.get(f"{base_url}/api/v1/users/me", headers=headers)
    
    if user_response.status_code != 200:
        print(f"❌ Get user failed: {user_response.status_code}")
        print(user_response.text)
        return False
    
    user_result = user_response.json()
    print("✅ Get user successful!")
    
    # Step 3: Verify organization data
    print("\n3️⃣ Verifying organization data...")
    if 'data' in user_result and 'organizations' in user_result['data']:
        orgs = user_result['data']['organizations']
        print(f"📊 Found {len(orgs)} organizations")
        
        if len(orgs) > 0:
            first_org = orgs[0]
            print(f"🏢 First organization structure:")
            print(json.dumps(first_org, indent=2))
            
            # Check if organization has the expected structure
            if 'organization' in first_org:
                org_data = first_org['organization']
                org_name = org_data.get('name', 'N/A')
                org_domain = org_data.get('domain', 'N/A')
                user_role = first_org.get('role', 'N/A')
                
                print(f"\n✅ Organization Details:")
                print(f"   Name: {org_name}")
                print(f"   Domain: {org_domain}")
                print(f"   User Role: {user_role}")
                
                if org_name == "ACME Corporation":
                    print("🎉 SUCCESS: Organization name is correct!")
                    return True
                else:
                    print(f"❌ FAIL: Expected 'ACME Corporation', got '{org_name}'")
                    return False
            else:
                print("❌ Organization data structure is incorrect")
                return False
        else:
            print("❌ No organizations found")
            return False
    else:
        print("❌ No organization data in response")
        return False

if __name__ == "__main__":
    success = test_complete_flow()
    if success:
        print("\n🎉 All tests passed! Organization data is working correctly.")
    else:
        print("\n❌ Tests failed. There's an issue with the organization data.")
