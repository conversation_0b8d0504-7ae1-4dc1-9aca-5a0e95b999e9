#!/usr/bin/env python3
"""
Test script to validate registration data
"""
from typing import Optional
from pydantic import BaseModel, EmailStr, ValidationError

class UserRegister(BaseModel):
    email: EmailStr
    password: str
    first_name: str
    last_name: Optional[str] = ""
    organization_name: Optional[str] = None
    organization_slug: Optional[str] = None

def test_registration_data():
    """Test the registration data that was failing"""
    
    # This is the exact data that was failing from the logs
    test_data = {
        'email': '<EMAIL>', 
        'password': '<PERSON>@123', 
        'first_name': '<PERSON>', 
        'organization_name': 'Agnoshin'
        # Note: last_name is missing - this was the issue
    }
    
    print("Testing registration data validation...")
    print(f"Input data: {test_data}")
    
    try:
        # Try to create UserRegister object
        user_data = UserRegister(**test_data)
        print("✅ SUCCESS: Registration data validation passed!")
        print(f"Validated data: {user_data.dict()}")
        return True
    except ValidationError as e:
        print("❌ FAILED: Registration data validation failed!")
        print(f"Validation errors: {e.errors()}")
        return False

def test_registration_with_last_name():
    """Test registration with last_name included"""
    
    test_data = {
        'email': '<EMAIL>', 
        'password': 'Vishnu@123', 
        'first_name': 'Vishnu', 
        'last_name': '',  # Empty string should be allowed now
        'organization_name': 'Agnoshin'
    }
    
    print("\nTesting registration data with empty last_name...")
    print(f"Input data: {test_data}")
    
    try:
        user_data = UserRegister(**test_data)
        print("✅ SUCCESS: Registration with empty last_name passed!")
        print(f"Validated data: {user_data.dict()}")
        return True
    except ValidationError as e:
        print("❌ FAILED: Registration with empty last_name failed!")
        print(f"Validation errors: {e.errors()}")
        return False

def test_registration_with_full_name():
    """Test registration with proper first and last name"""
    
    test_data = {
        'email': '<EMAIL>', 
        'password': 'Vishnu@123', 
        'first_name': 'Vishnu', 
        'last_name': 'Balaguru',
        'organization_name': 'Agnoshin'
    }
    
    print("\nTesting registration data with full name...")
    print(f"Input data: {test_data}")
    
    try:
        user_data = UserRegister(**test_data)
        print("✅ SUCCESS: Registration with full name passed!")
        print(f"Validated data: {user_data.dict()}")
        return True
    except ValidationError as e:
        print("❌ FAILED: Registration with full name failed!")
        print(f"Validation errors: {e.errors()}")
        return False

if __name__ == "__main__":
    print("🧪 Testing UserRegister model validation\n")
    
    # Test the original failing case
    result1 = test_registration_data()
    
    # Test with empty last_name
    result2 = test_registration_with_last_name()
    
    # Test with proper last_name
    result3 = test_registration_with_full_name()
    
    print(f"\n📊 Test Results:")
    print(f"   Original data (missing last_name): {'✅ PASS' if result1 else '❌ FAIL'}")
    print(f"   With empty last_name: {'✅ PASS' if result2 else '❌ FAIL'}")
    print(f"   With full name: {'✅ PASS' if result3 else '❌ FAIL'}")
    
    if all([result1, result2, result3]):
        print("\n🎉 All tests passed! Registration should work now.")
    else:
        print("\n⚠️  Some tests failed. Registration may still have issues.")
