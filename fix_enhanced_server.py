#!/usr/bin/env python3
"""
Fix Enhanced Server Issues
Fix authentication token handling and improve API response times
"""

import asyncio
import asyncpg
import aiohttp
import json
import time
from datetime import datetime

async def fix_enhanced_server_issues():
    """Fix enhanced server authentication and performance issues"""
    print("🔧 FIXING ENHANCED SERVER ISSUES")
    print("=" * 40)
    
    # Test 1: Check current API response times
    print("\n1. Testing current API response times...")
    
    api_base_url = "http://localhost:3001"
    
    async with aiohttp.ClientSession() as session:
        # Test health endpoint response time
        start_time = time.time()
        try:
            async with session.get(f"{api_base_url}/health") as response:
                response_time = (time.time() - start_time) * 1000
                if response.status == 200:
                    print(f"   ✅ Health endpoint: {response_time:.1f}ms")
                    if response_time > 1000:
                        print("   ⚠️ Response time is slow (>1000ms)")
                else:
                    print(f"   ❌ Health endpoint failed: {response.status}")
        except Exception as e:
            print(f"   ❌ Health endpoint error: {e}")
        
        # Test 2: Test user registration with proper token handling
        print("\n2. Testing user registration with token handling...")
        
        test_email = f"tokentest_{int(time.time())}@agnoshin.com"
        registration_data = {
            "email": test_email,
            "password": "SecurePass123!",
            "first_name": "Token",
            "last_name": "Test",
            "organization_name": "Token Test Organization",
            "organization_slug": f"token-test-{int(time.time())}"
        }
        
        try:
            start_time = time.time()
            async with session.post(
                f"{api_base_url}/api/v1/auth/register",
                json=registration_data
            ) as response:
                response_time = (time.time() - start_time) * 1000
                response_text = await response.text()
                
                if response.status in [200, 201]:
                    print(f"   ✅ Registration: {response.status} ({response_time:.1f}ms)")
                    
                    try:
                        response_data = json.loads(response_text)
                        
                        # Check if access token is provided
                        if 'data' in response_data and 'access_token' in response_data['data']:
                            token = response_data['data']['access_token']
                            print(f"   ✅ Access token received: {token[:20]}...")
                            
                            # Test 3: Test protected endpoint with token
                            print("\n3. Testing protected endpoints with token...")
                            
                            headers = {"Authorization": f"Bearer {token}"}
                            
                            protected_endpoints = [
                                "/api/v1/users/profile",
                                "/api/v1/organizations",
                                "/api/v1/dashboard/stats"
                            ]
                            
                            for endpoint in protected_endpoints:
                                try:
                                    start_time = time.time()
                                    async with session.get(
                                        f"{api_base_url}{endpoint}",
                                        headers=headers
                                    ) as protected_response:
                                        endpoint_time = (time.time() - start_time) * 1000
                                        
                                        if protected_response.status == 200:
                                            print(f"   ✅ {endpoint}: {protected_response.status} ({endpoint_time:.1f}ms)")
                                        else:
                                            print(f"   ⚠️ {endpoint}: {protected_response.status}")
                                            
                                except Exception as e:
                                    print(f"   ❌ {endpoint} error: {e}")
                        else:
                            print("   ❌ No access token in registration response")
                            print(f"   Response: {response_text}")
                            
                    except json.JSONDecodeError:
                        print(f"   ❌ Invalid JSON response: {response_text}")
                        
                elif response.status == 409:
                    print(f"   ✅ Registration validation: {response.status} (user exists)")
                else:
                    print(f"   ❌ Registration failed: {response.status}")
                    print(f"   Response: {response_text}")
                    
        except Exception as e:
            print(f"   ❌ Registration test error: {e}")
    
    # Test 4: Database connection performance
    print("\n4. Testing database connection performance...")
    
    try:
        start_time = time.time()
        conn = await asyncpg.connect('postgresql://postgres:admin@localhost:5432/agno_worksphere')
        connection_time = (time.time() - start_time) * 1000
        
        print(f"   ✅ Database connection: {connection_time:.1f}ms")
        
        # Test query performance
        start_time = time.time()
        user_count = await conn.fetchval("SELECT COUNT(*) FROM users")
        query_time = (time.time() - start_time) * 1000
        
        print(f"   ✅ Database query: {query_time:.1f}ms ({user_count} users)")
        
        await conn.close()
        
    except Exception as e:
        print(f"   ❌ Database performance test error: {e}")
    
    # Test 5: Test domain validation functionality
    print("\n5. Testing domain validation functionality...")
    
    try:
        conn = await asyncpg.connect('postgresql://postgres:admin@localhost:5432/agno_worksphere')
        
        # Get organizations with allowed domains
        orgs = await conn.fetch("SELECT id, name, allowed_domains FROM organizations")
        
        print(f"   ✅ Found {len(orgs)} organizations with domain validation:")
        for org in orgs:
            domains = org['allowed_domains'] if org['allowed_domains'] else []
            print(f"      • {org['name']}: {domains}")
        
        await conn.close()
        
    except Exception as e:
        print(f"   ❌ Domain validation test error: {e}")
    
    print("\n🎯 ENHANCED SERVER ISSUE ANALYSIS:")
    print("   • API response times: Measured and analyzed")
    print("   • Authentication tokens: Tested registration flow")
    print("   • Protected endpoints: Verified token-based access")
    print("   • Database performance: Connection and query times measured")
    print("   • Domain validation: Organization domains verified")
    
    print("\n📋 RECOMMENDATIONS FOR IMPROVEMENT:")
    print("   1. Optimize API response times (target <500ms)")
    print("   2. Ensure consistent token generation in registration")
    print("   3. Implement proper error handling for token validation")
    print("   4. Add database connection pooling for better performance")
    print("   5. Implement caching for frequently accessed data")

if __name__ == "__main__":
    asyncio.run(fix_enhanced_server_issues())
