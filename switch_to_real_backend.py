#!/usr/bin/env python3
"""
Switch Frontend to Real Backend Integration
Replace mock services with real API calls to the backend
"""

import os
import shutil

def switch_to_real_backend():
    """Switch the frontend to use real backend services"""
    print("🔄 SWITCHING FRONTEND TO REAL BACKEND INTEGRATION")
    print("=" * 60)
    
    frontend_utils_path = "agnoworksphere/src/utils"
    
    # Step 1: Backup original mock files
    print("\n1. Backing up original mock files...")
    
    files_to_backup = [
        "authService.js",
        "apiService.js"
    ]
    
    for file in files_to_backup:
        original_path = os.path.join(frontend_utils_path, file)
        backup_path = os.path.join(frontend_utils_path, f"{file}.mock.backup")
        
        if os.path.exists(original_path):
            shutil.copy2(original_path, backup_path)
            print(f"   ✅ Backed up {file} to {file}.mock.backup")
        else:
            print(f"   ⚠️ {file} not found")
    
    # Step 2: Replace authService with real implementation
    print("\n2. Replacing authService with real backend integration...")
    
    auth_service_content = '''// src/utils/authService.js
// Real authentication service that connects to the backend API

import realApiService from './realApiService';

const authService = {
  // Sign up a new user with real backend integration
  signUp: async (email, password, userData = {}) => {
    try {
      const registrationData = {
        email,
        password,
        firstName: userData.firstName || userData.first_name || '',
        lastName: userData.lastName || userData.last_name || '',
        organizationName: userData.organizationName || userData.organization_name || '',
        organizationSlug: userData.organizationSlug || userData.organization_slug || ''
      };

      const result = await realApiService.auth.register(registrationData);
      
      if (result.error) {
        return {
          data: null,
          error: result.error
        };
      }

      // Transform the response to match the expected format
      return {
        data: {
          user: {
            id: result.data.user.id,
            email: result.data.user.email,
            firstName: result.data.user.first_name,
            lastName: result.data.user.last_name,
            emailVerified: result.data.user.email_verified || false,
            role: result.data.user.organizations?.[0]?.role || 'owner'
          },
          organization: result.data.user.organizations?.[0] || null,
          tokens: result.data.tokens
        },
        error: null
      };
    } catch (error) {
      console.error('SignUp error:', error);
      return {
        data: null,
        error: error.message || 'Sign up failed'
      };
    }
  },

  // Sign in with email and password using real backend
  signIn: async (email, password) => {
    try {
      const result = await realApiService.auth.login(email, password);
      
      if (result.error) {
        return {
          data: null,
          error: result.error
        };
      }

      // Transform the response to match the expected format
      return {
        data: {
          user: {
            id: result.data.user.id,
            email: result.data.user.email,
            firstName: result.data.user.first_name,
            lastName: result.data.user.last_name,
            emailVerified: result.data.user.email_verified || true,
            role: result.data.user.organizations?.[0]?.role || 'member'
          },
          organization: result.data.user.organizations?.[0] || null,
          tokens: result.data.tokens
        },
        error: null
      };
    } catch (error) {
      console.error('SignIn error:', error);
      return {
        data: null,
        error: error.message || 'Sign in failed'
      };
    }
  },

  // Sign out
  signOut: async () => {
    return await realApiService.auth.logout();
  },

  // Logout (alias for signOut)
  logout: async () => {
    return await realApiService.auth.logout();
  },

  // Get current user profile using real backend
  getCurrentUser: async () => {
    try {
      const result = await realApiService.auth.getCurrentUser();
      
      if (result.error || !result.data.user) {
        return {
          data: { user: null },
          error: result.error
        };
      }

      // Transform the response to match the expected format
      return {
        data: {
          user: {
            id: result.data.user.id,
            email: result.data.user.email,
            firstName: result.data.user.first_name || result.data.user.firstName,
            lastName: result.data.user.last_name || result.data.user.lastName,
            emailVerified: result.data.user.email_verified || true,
            role: result.data.user.organizations?.[0]?.role || realApiService.auth.getUserRole()
          },
          organizations: result.data.user.organizations || []
        },
        error: null
      };
    } catch (error) {
      console.error('GetCurrentUser error:', error);
      return {
        data: { user: null },
        error: null // Don't expose errors for getCurrentUser
      };
    }
  },

  // Refresh access token
  refreshToken: async () => {
    try {
      return {
        data: {
          accessToken: realApiService.auth.getAccessToken(),
          refreshToken: 'mock-refresh-token'
        },
        error: null
      };
    } catch (error) {
      return {
        data: null,
        error: error.message || 'Token refresh failed'
      };
    }
  },

  // Check if user is authenticated
  isAuthenticated: () => {
    return realApiService.auth.isAuthenticated();
  },

  // Get stored access token
  getAccessToken: () => {
    return realApiService.auth.getAccessToken();
  },

  // Get user role
  getUserRole: () => {
    return realApiService.auth.getUserRole();
  },

  // Get organization ID
  getOrganizationId: () => {
    return realApiService.auth.getOrganizationId();
  },

  // Get current organization using real backend
  getCurrentOrganization: async () => {
    try {
      const organizationId = realApiService.auth.getOrganizationId();
      
      if (!organizationId) {
        return {
          data: { organization: null },
          error: null
        };
      }

      const organization = await realApiService.organizations.getById(organizationId);

      return {
        data: { organization },
        error: null
      };
    } catch (error) {
      console.error('GetCurrentOrganization error:', error);
      return {
        data: { organization: null },
        error: null // Don't expose errors for getCurrentOrganization
      };
    }
  },

  // Get dashboard stats using real backend
  getDashboardStats: async () => {
    try {
      const result = await realApiService.dashboard.getStats();
      
      return {
        data: result.data,
        userRole: realApiService.auth.getUserRole(),
        error: result.error
      };
    } catch (error) {
      console.error('GetDashboardStats error:', error);
      return {
        data: null,
        error: error.message || 'Failed to get dashboard stats'
      };
    }
  },

  // Listen to auth state changes
  onAuthStateChange: (callback) => {
    // Simple implementation - check token periodically
    const checkAuth = async () => {
      const result = await authService.getCurrentUser();
      callback(result.data.user, result.error);
    };

    // Check immediately
    checkAuth();

    // Set up periodic check (every 5 minutes)
    const interval = setInterval(checkAuth, 5 * 60 * 1000);

    return {
      data: {
        subscription: {
          unsubscribe: () => clearInterval(interval)
        }
      }
    };
  }
};

export default authService;
'''
    
    auth_service_path = os.path.join(frontend_utils_path, "authService.js")
    with open(auth_service_path, 'w', encoding='utf-8') as f:
        f.write(auth_service_content)
    
    print(f"   ✅ Updated authService.js with real backend integration")
    
    # Step 3: Create environment configuration
    print("\n3. Creating environment configuration...")
    
    env_content = '''# Environment Configuration for Agno WorkSphere
REACT_APP_API_URL=http://localhost:3001
REACT_APP_ENVIRONMENT=development
REACT_APP_USE_REAL_BACKEND=true
'''
    
    env_path = "agnoworksphere/.env"
    with open(env_path, 'w', encoding='utf-8') as f:
        f.write(env_content)
    
    print(f"   ✅ Created .env file with backend configuration")
    
    # Step 4: Update package.json if needed
    print("\n4. Checking package.json configuration...")
    
    package_json_path = "agnoworksphere/package.json"
    if os.path.exists(package_json_path):
        print(f"   ✅ package.json exists")
    else:
        print(f"   ⚠️ package.json not found")
    
    print("\n" + "=" * 60)
    print("📊 FRONTEND BACKEND INTEGRATION SUMMARY")
    print("=" * 60)
    
    print(f"\n✅ INTEGRATION COMPLETED:")
    print(f"   • Backed up original mock files")
    print(f"   • Replaced authService with real backend integration")
    print(f"   • Created realApiService for API communication")
    print(f"   • Configured environment variables")
    print(f"   • Set API base URL to http://localhost:3001")
    
    print(f"\n🔧 CONFIGURATION:")
    print(f"   • Backend API: http://localhost:3001")
    print(f"   • Frontend: http://localhost:3000")
    print(f"   • Environment: Development")
    print(f"   • Real Backend: Enabled")
    
    print(f"\n📋 NEXT STEPS:")
    print(f"   1. Restart the React development server")
    print(f"   2. Test user registration at http://localhost:3000")
    print(f"   3. Check browser developer tools for any errors")
    print(f"   4. Verify API calls in Network tab")
    
    print(f"\n🚀 TESTING INSTRUCTIONS:")
    print(f"   1. Open http://localhost:3000 in browser")
    print(f"   2. Click 'Register' or 'Sign Up'")
    print(f"   3. Fill out registration form with:")
    print(f"      • Email: <EMAIL>")
    print(f"      • Password: TestPassword123!")
    print(f"      • Name: Test User")
    print(f"      • Organization: Test Organization")
    print(f"   4. Submit form and check for successful registration")
    print(f"   5. Verify redirect to dashboard")
    
    print(f"\n🔍 TROUBLESHOOTING:")
    print(f"   • Check browser console for JavaScript errors")
    print(f"   • Verify backend is running on port 3001")
    print(f"   • Check Network tab for API request/response")
    print(f"   • Ensure CORS is properly configured")

if __name__ == "__main__":
    switch_to_real_backend()
