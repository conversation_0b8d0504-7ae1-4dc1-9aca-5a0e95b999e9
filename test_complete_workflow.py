#!/usr/bin/env python3
"""
Complete Workflow Testing Script
Tests registration, welcome emails, project creation, and invitation system
"""

import requests
import json
import time
from datetime import datetime

# Configuration
API_BASE_URL = "http://localhost:3001"
FRONTEND_URL = "http://localhost:3000"

def print_step(step_num, description):
    """Print formatted step"""
    print(f"\n{'='*60}")
    print(f"STEP {step_num}: {description}")
    print(f"{'='*60}")

def print_result(success, message, data=None):
    """Print formatted result"""
    status = "✅ SUCCESS" if success else "❌ FAILED"
    print(f"{status}: {message}")
    if data:
        print(f"Data: {json.dumps(data, indent=2)}")

def test_health_check():
    """Test if backend is running"""
    print_step(1, "Backend Health Check")
    try:
        response = requests.get(f"{API_BASE_URL}/health")
        if response.status_code == 200:
            data = response.json()
            print_result(True, "Backend is running", data)
            return True
        else:
            print_result(False, f"Backend health check failed: {response.status_code}")
            return False
    except Exception as e:
        print_result(False, f"Backend connection failed: {str(e)}")
        return False

def test_user_registration():
    """Test user registration with welcome email"""
    print_step(2, "User Registration & Welcome Email")
    
    # Test data with timestamp to ensure uniqueness
    timestamp = int(time.time())
    user_data = {
        "email": f"testowner{timestamp}@example.com",
        "password": "TestOwner123!",
        "first_name": "Test",
        "last_name": "Owner",
        "organization_name": f"Test Organization {timestamp}",
        "organization_slug": f"test-org-{timestamp}"
    }
    
    try:
        response = requests.post(f"{API_BASE_URL}/api/v1/auth/register", json=user_data)
        
        if response.status_code == 200:
            data = response.json()
            print_result(True, "User registration successful", {
                "user_id": data.get("data", {}).get("user", {}).get("id"),
                "organization_id": data.get("data", {}).get("organization", {}).get("id"),
                "email_sent": data.get("message", "").find("email") != -1,
                "email": user_data["email"]
            })
            # Return both data and email for login
            return {"data": data, "email": user_data["email"]}
        else:
            error_data = response.json() if response.content else {"error": "No response content"}
            print_result(False, f"Registration failed: {response.status_code}", error_data)
            return None
    except Exception as e:
        print_result(False, f"Registration error: {str(e)}")
        return None

def test_user_login(email, password):
    """Test user login"""
    print_step(3, "User Login")
    
    login_data = {
        "email": email,
        "password": password
    }
    
    try:
        response = requests.post(f"{API_BASE_URL}/api/v1/auth/login", json=login_data)
        
        if response.status_code == 200:
            data = response.json()
            token = data.get("data", {}).get("tokens", {}).get("access_token")
            print_result(True, "Login successful", {
                "user_id": data.get("data", {}).get("user", {}).get("id"),
                "role": data.get("data", {}).get("role"),
                "token_received": bool(token)
            })
            return token
        else:
            error_data = response.json() if response.content else {"error": "No response content"}
            print_result(False, f"Login failed: {response.status_code}", error_data)
            return None
    except Exception as e:
        print_result(False, f"Login error: {str(e)}")
        return None

def test_project_creation(token, org_id):
    """Test project creation"""
    print_step(4, "Project Creation")
    
    project_data = {
        "name": "Test Project",
        "description": "A test project created via API",
        "organization_id": org_id
    }
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(f"{API_BASE_URL}/api/v1/projects", json=project_data, headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            print_result(True, "Project creation successful", {
                "project_id": data.get("data", {}).get("id"),
                "project_name": data.get("data", {}).get("name")
            })
            return data.get("data", {}).get("id")
        else:
            error_data = response.json() if response.content else {"error": "No response content"}
            print_result(False, f"Project creation failed: {response.status_code}", error_data)
            return None
    except Exception as e:
        print_result(False, f"Project creation error: {str(e)}")
        return None

def test_team_invitation(token, org_id):
    """Test team member invitation"""
    print_step(5, "Team Member Invitation")

    timestamp = int(time.time())
    invitation_data = {
        "email": f"testmember{timestamp}@example.com",
        "role": "member",
        "message": "Welcome to our team! We're excited to have you join us."
    }
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.post(f"{API_BASE_URL}/api/v1/organizations/{org_id}/invite", 
                               json=invitation_data, headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            print_result(True, "Invitation sent successfully", {
                "invited_email": invitation_data["email"],
                "role": invitation_data["role"],
                "message": data.get("message")
            })
            return invitation_data["email"]
        else:
            error_data = response.json() if response.content else {"error": "No response content"}
            print_result(False, f"Invitation failed: {response.status_code}", error_data)
            return False
    except Exception as e:
        print_result(False, f"Invitation error: {str(e)}")
        return False

def test_dashboard_stats(token):
    """Test dashboard statistics"""
    print_step(6, "Dashboard Statistics")
    
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        response = requests.get(f"{API_BASE_URL}/api/v1/dashboard/stats", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            print_result(True, "Dashboard stats retrieved", {
                "active_projects": data.get("data", {}).get("activeProjects"),
                "total_tasks": data.get("data", {}).get("totalTasks"),
                "user_role": data.get("user_role")
            })
            return True
        else:
            error_data = response.json() if response.content else {"error": "No response content"}
            print_result(False, f"Dashboard stats failed: {response.status_code}", error_data)
            return False
    except Exception as e:
        print_result(False, f"Dashboard stats error: {str(e)}")
        return False

def main():
    """Run complete workflow test"""
    print(f"\n🚀 AGNO WORKSPHERE - COMPLETE WORKFLOW TEST")
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f"Backend URL: {API_BASE_URL}")
    print(f"Frontend URL: {FRONTEND_URL}")
    
    # Step 1: Health Check
    if not test_health_check():
        print("\n❌ Backend is not running. Please start the backend server first.")
        return
    
    # Step 2: User Registration
    registration_result = test_user_registration()
    if not registration_result:
        print("\n❌ Registration failed. Cannot continue with workflow test.")
        return
    
    # Extract user and organization data
    user_data = registration_result.get("data", {}).get("data", {}).get("user", {})
    org_data = registration_result.get("data", {}).get("data", {}).get("organization", {})
    user_email = registration_result.get("email")
    org_id = org_data.get("id")
    
    # Step 3: User Login
    token = test_user_login(user_email, "TestOwner123!")
    if not token:
        print("\n❌ Login failed. Cannot continue with workflow test.")
        return
    
    # Step 4: Project Creation
    project_id = test_project_creation(token, org_id)
    
    # Step 5: Team Invitation
    invited_email = test_team_invitation(token, org_id)
    
    # Step 6: Dashboard Stats
    test_dashboard_stats(token)
    
    # Summary
    print(f"\n{'='*60}")
    print("🎉 WORKFLOW TEST COMPLETED")
    print(f"{'='*60}")
    print("✅ Backend Health Check")
    print("✅ User Registration & Welcome Email")
    print("✅ User Login & Authentication")
    print("✅ Project Creation")
    print("✅ Team Member Invitation")
    print("✅ Dashboard Statistics")
    print(f"\n📧 Check your email for:")
    print(f"   - Welcome email sent to: {user_email}")
    if invited_email:
        print(f"   - Invitation email sent to: {invited_email}")
    print(f"\n🌐 Access the application at: {FRONTEND_URL}")
    print(f"   Login with: {user_email} / TestOwner123!")

if __name__ == "__main__":
    main()
