{"timestamp": 1754028503.166438, "total_time": 27.320792198181152, "test_users": {"owner": {"email": "<EMAIL>", "org_id": "8a5de1e9-0d6a-43b2-af4d-0f75a5ede974"}, "member": {"email": "<EMAIL>", "org_id": "4fa01483-8c93-4d02-adc2-548d7ed6c54c"}}, "summary": {"total": 12, "passed": 12, "failed": 0, "skipped": 0}, "results": [{"test": "Create Owner User", "status": "PASS", "details": "<EMAIL>", "timestamp": 1754028478.0885139}, {"test": "Create Member User", "status": "PASS", "details": "<EMAIL>", "timestamp": 1754028481.673919}, {"test": "Auth Required: /users/profile", "status": "PASS", "details": "Properly requires authentication", "timestamp": 1754028484.8600736}, {"test": "Auth Required: /organizations", "status": "PASS", "details": "Properly requires authentication", "timestamp": 1754028486.8964632}, {"test": "Auth Required: /projects", "status": "PASS", "details": "Properly requires authentication", "timestamp": 1754028488.934606}, {"test": "Invalid Token Rejection", "status": "PASS", "details": "Invalid token properly rejected", "timestamp": 1754028490.9735641}, {"test": "Malformed Token Rejection", "status": "PASS", "details": "Malformed token properly rejected", "timestamp": 1754028492.9984202}, {"test": "owner Profile Access", "status": "PASS", "details": "Can access own profile", "timestamp": 1754028495.052871}, {"test": "member Profile Access", "status": "PASS", "details": "Can access own profile", "timestamp": 1754028497.0766985}, {"test": "owner Organizations Access", "status": "PASS", "details": "Can access organizations", "timestamp": 1754028499.0955403}, {"test": "member Organizations Access", "status": "PASS", "details": "Can access organizations", "timestamp": 1754028501.1295786}, {"test": "Cross-Organization Access Block", "status": "PASS", "details": "Access properly blocked", "timestamp": 1754028503.166438}]}