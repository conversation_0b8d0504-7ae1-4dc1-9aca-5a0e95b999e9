// src/contexts/AuthContext.jsx

import React, { createContext, useContext, useState, useEffect } from 'react';
import authService from '../utils/authService';

// Create context
const AuthContext = createContext();

// Enhanced context provider with mock authentication
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [loading, setLoading] = useState(true);

  // Check for existing session on mount
  useEffect(() => {
    const checkExistingSession = async () => {
      try {
        // First check if there's a stored token
        const token = localStorage.getItem('accessToken');
        if (!token) {
          console.log('No access token found, user not authenticated');
          setLoading(false);
          return;
        }

        // If token exists, verify it with the API
        const result = await authService.getCurrentUser();
        if (result?.data?.user) {
          setUser(result.data.user);
          console.log('Session restored for user:', result.data.user.email);
        } else {
          // Invalid token, remove it
          localStorage.removeItem('accessToken');
          console.log('Invalid access token removed');
        }
      } catch (error) {
        console.error('Session check failed:', error);
        // If API call fails, remove the invalid token
        localStorage.removeItem('accessToken');
        // Don't set user to null, just let it remain undefined
        // This allows the app to work even when the API is not available
      } finally {
        setLoading(false);
      }
    };

    checkExistingSession();
  }, []);

  const login = async (email, password) => {
    try {
      const result = await authService.signIn(email, password);
      if (result.error) {
        return { success: false, error: result.error };
      }

      setUser(result.data.user);
      return { success: true, data: result.data };
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  const logout = async () => {
    try {
      await authService.signOut();
      setUser(null);
      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  const register = async (email, password, userData) => {
    try {
      const result = await authService.signUp(email, password, userData);
      if (result.error) {
        return { success: false, error: result.error };
      }

      setUser(result.data.user);
      return { success: true, data: result.data };
    } catch (error) {
      return { success: false, error: error.message };
    }
  };

  const value = {
    user,
    login,
    logout,
    register,
    isAuthenticated: !!user,
    loading
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook
export const useAuth = () => useContext(AuthContext);
