version: '3.8'

services:
  db:
    image: postgres:15
    environment:
      POSTGRES_DB: agno_worksphere
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

  api:
    build: .
    ports:
      - "3001:3001"
    environment:
      - DATABASE_URL=postgresql+asyncpg://postgres:postgres@db:5432/agno_worksphere
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
      - DEBUG=True
      - ENVIRONMENT=development
    volumes:
      - ./uploads:/app/uploads
      - ./.env:/app/.env
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_started
    command: >
      sh -c "
        echo 'Waiting for database...' &&
        sleep 10 &&
        echo 'Running migrations...' &&
        alembic upgrade head &&
        echo 'Starting server...' &&
        python run.py
      "

volumes:
  postgres_data:
  redis_data:
