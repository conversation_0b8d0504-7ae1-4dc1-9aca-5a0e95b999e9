// src/utils/authService.js

// Enhanced authentication service (MOCK - No Backend Required)
// const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api';

// Helper function to handle API responses (not used in mock mode)
// const handleResponse = async (response) => {
//   const result = await response.json();

//   if (!response.ok) {
//     throw new Error(result.error?.message || result.message || 'API request failed');
//   }

//   return result;
// };

const authService = {
  // Sign up a new user with enhanced registration (MOCK - No Backend)
  signUp: async (email, password, userData = {}) => {
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Mock successful registration
      const mockUser = {
        id: `user_${Date.now()}`,
        email: email,
        first_name: userData.firstName || userData.first_name || '',
        last_name: userData.lastName || userData.last_name || '',
        email_verified: false
      };

      const mockOrganization = {
        id: `org_${Date.now()}`,
        name: userData.organizationName || userData.organization_name || 'My Organization',
        domain: userData.organizationDomain || 'example.com'
      };

      const mockTokens = {
        access_token: `mock_token_${Date.now()}`,
        refresh_token: `mock_refresh_${Date.now()}`
      };

      // Store tokens and user info
      localStorage.setItem('accessToken', mockTokens.access_token);
      localStorage.setItem('userRole', 'owner'); // New organization owner
      localStorage.setItem('organizationId', mockOrganization.id);
      localStorage.setItem('currentUser', JSON.stringify({
        ...mockUser,
        role: 'owner',
        organization: mockOrganization
      }));

      return {
        data: {
          user: {
            id: mockUser.id,
            email: mockUser.email,
            firstName: mockUser.first_name,
            lastName: mockUser.last_name,
            emailVerified: mockUser.email_verified,
            role: 'owner'
          },
          organization: mockOrganization,
          tokens: mockTokens
        },
        error: null
      };
    } catch (error) {
      return {
        data: null,
        error: error.message || 'Sign up failed'
      };
    }
  },

  // Sign in with email and password (MOCK - No Backend)
  signIn: async (email, password) => {
    try {
      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Mock credentials for testing - 4 different roles
      const mockCredentials = [
        { email: '<EMAIL>', password: 'owner123', role: 'owner' },
        { email: '<EMAIL>', password: 'admin123', role: 'admin' },
        { email: '<EMAIL>', password: 'member123', role: 'member' },
        { email: '<EMAIL>', password: 'viewer123', role: 'viewer' }
      ];

      const validCredential = mockCredentials.find(
        cred => cred.email === email && cred.password === password
      );

      if (!validCredential) {
        return {
          data: null,
          error: 'Invalid email or password'
        };
      }

      const mockUser = {
        id: `user_${Date.now()}`,
        email: email,
        first_name: email.split('@')[0],
        last_name: 'User',
        email_verified: true
      };

      const mockOrganization = {
        id: `org_${Date.now()}`,
        name: 'Demo Organization',
        domain: 'demo.com'
      };

      const mockTokens = {
        access_token: `mock_token_${Date.now()}`,
        refresh_token: `mock_refresh_${Date.now()}`
      };

      // Store tokens and user info
      localStorage.setItem('accessToken', mockTokens.access_token);
      localStorage.setItem('userRole', validCredential.role);
      localStorage.setItem('organizationId', mockOrganization.id);
      localStorage.setItem('currentUser', JSON.stringify({
        ...mockUser,
        role: validCredential.role,
        organization: mockOrganization
      }));

      return {
        data: {
          user: {
            id: mockUser.id,
            email: mockUser.email,
            firstName: mockUser.first_name,
            lastName: mockUser.last_name,
            emailVerified: mockUser.email_verified,
            role: validCredential.role
          },
          organization: mockOrganization,
          tokens: mockTokens
        },
        error: null
      };

    } catch (error) {
      return {
        data: null,
        error: error.message || 'Sign in failed'
      };
    }
  },

  // Sign out
  signOut: async () => {
    try {
      // Clear stored tokens and user data
      localStorage.removeItem('accessToken');
      localStorage.removeItem('refreshToken');
      localStorage.removeItem('userRole');
      localStorage.removeItem('organizationId');
      localStorage.removeItem('currentUser');

      return {
        error: null
      };
    } catch (error) {
      return {
        error: error.message || 'Sign out failed'
      };
    }
  },

  // Logout (alias for signOut)
  logout: async () => {
    return await authService.signOut();
  },

  // Get current user profile (MOCK - No Backend)
  getCurrentUser: async () => {
    try {
      const token = localStorage.getItem('accessToken');
      const currentUser = localStorage.getItem('currentUser');

      if (!token || !currentUser) {
        return {
          data: { user: null },
          error: null
        };
      }

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 200));

      const userData = JSON.parse(currentUser);

      return {
        data: {
          user: {
            id: userData.id,
            email: userData.email,
            firstName: userData.first_name || userData.firstName,
            lastName: userData.last_name || userData.lastName,
            emailVerified: userData.email_verified || true,
            role: userData.role || localStorage.getItem('userRole') || 'member'
          },
          organizations: userData.organization ? [userData.organization] : []
        },
        error: null
      };
    } catch (error) {
      return {
        data: { user: null },
        error: null // Don't expose errors for getCurrentUser
      };
    }
  },

  // Refresh access token (authentication removed - mock implementation)
  refreshToken: async () => {
    try {
      // Mock successful token refresh
      await new Promise(resolve => setTimeout(resolve, 200)); // Simulate API delay

      return {
        data: {
          accessToken: 'mock-access-token',
          refreshToken: 'mock-refresh-token'
        },
        error: null
      };
    } catch (error) {
      return {
        data: null,
        error: error.message || 'Token refresh failed'
      };
    }
  },

  // Check if user is authenticated
  isAuthenticated: () => {
    const token = localStorage.getItem('accessToken');
    return !!token;
  },

  // Get stored access token
  getAccessToken: () => {
    return localStorage.getItem('accessToken');
  },

  // Get user role
  getUserRole: () => {
    return localStorage.getItem('userRole') || 'member';
  },

  // Get organization ID
  getOrganizationId: () => {
    return localStorage.getItem('organizationId');
  },

  // Get current organization (MOCK - No Backend)
  getCurrentOrganization: async () => {
    try {
      const token = localStorage.getItem('accessToken');
      const currentUser = localStorage.getItem('currentUser');

      if (!token || !currentUser) {
        return {
          data: { organization: null },
          error: null
        };
      }

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 200));

      const userData = JSON.parse(currentUser);

      return {
        data: {
          organization: userData.organization || {
            id: localStorage.getItem('organizationId'),
            name: 'Demo Organization',
            domain: 'demo.com'
          }
        },
        error: null
      };
    } catch (error) {
      return {
        data: { organization: null },
        error: null // Don't expose errors for getCurrentOrganization
      };
    }
  },

  // Get dashboard stats (MOCK - No Backend)
  getDashboardStats: async () => {
    try {
      const token = localStorage.getItem('accessToken');
      if (!token) {
        return {
          data: null,
          error: 'No access token'
        };
      }

      // Simulate API delay
      await new Promise(resolve => setTimeout(resolve, 300));

      // Mock dashboard stats
      const mockStats = {
        projects: {
          total: 8,
          active: 5,
          completed: 3
        },
        tasks: {
          total: 42,
          pending: 15,
          in_progress: 18,
          completed: 9
        },
        team: {
          total_members: 12,
          active_members: 10,
          pending_invites: 2
        },
        integrations: {
          total: 6,
          active: 4,
          inactive: 2
        }
      };

      return {
        data: mockStats,
        userRole: localStorage.getItem('userRole') || 'member',
        error: null
      };
    } catch (error) {
      return {
        data: null,
        error: error.message || 'Failed to get dashboard stats'
      };
    }
  },

  // Listen to auth state changes
  onAuthStateChange: (callback) => {
    // Simple implementation - check token periodically
    const checkAuth = async () => {
      const result = await authService.getCurrentUser();
      callback(result.data.user, result.error);
    };

    // Check immediately
    checkAuth();

    // Set up periodic check (every 5 minutes)
    const interval = setInterval(checkAuth, 5 * 60 * 1000);

    return {
      data: {
        subscription: {
          unsubscribe: () => clearInterval(interval)
        }
      }
    };
  }
};

export default authService;
