"""
Dependency injection utilities
"""
from typing import <PERSON><PERSON>, Generator
from fastapi import Depends, HTTPException, Header, Request
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.core.database import get_db
from app.core.security import verify_token
from app.core.exceptions import AuthenticationError, InsufficientPermissionsError
from app.models.user import User
from app.models.organization import OrganizationMember


security = HTTPBearer(auto_error=False)


async def get_current_user(
    credentials: Optional[HTTPAuthorizationCredentials] = Depends(security),
    x_mock_user_id: Optional[str] = Header(None),
    db: AsyncSession = Depends(get_db)
) -> User:
    """
    Get current authenticated user
    For development, supports mock user ID header
    """
    # Development mode: use mock user ID if provided
    if x_mock_user_id:
        result = await db.execute(select(User).where(User.id == x_mock_user_id))
        user = result.scalar_one_or_none()
        if user:
            return user
        # If mock user doesn't exist, create one
        mock_user = User(
            id=x_mock_user_id,
            email="<EMAIL>",
            password_hash="mock_hash",
            first_name="Demo",
            last_name="User",
            email_verified=True
        )
        db.add(mock_user)
        await db.commit()
        await db.refresh(mock_user)
        return mock_user
    
    # Production mode: require valid JWT token
    if not credentials:
        raise AuthenticationError("Authentication required")
    
    try:
        payload = verify_token(credentials.credentials)
        user_id = payload.get("sub")
        if not user_id:
            raise AuthenticationError("Invalid token payload")
        
        result = await db.execute(select(User).where(User.id == user_id))
        user = result.scalar_one_or_none()
        if not user:
            raise AuthenticationError("User not found")
        
        return user
        
    except Exception as e:
        raise AuthenticationError(str(e))


async def get_current_active_user(
    current_user: User = Depends(get_current_user)
) -> User:
    """Get current active user"""
    if not current_user.email_verified:
        raise AuthenticationError("Email not verified")
    return current_user


async def get_organization_member(
    organization_id: str = Header(None, alias="X-Organization-ID"),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
) -> Optional[OrganizationMember]:
    """Get organization member for current user"""
    if not organization_id:
        return None
    
    result = await db.execute(
        select(OrganizationMember).where(
            OrganizationMember.organization_id == organization_id,
            OrganizationMember.user_id == current_user.id
        )
    )
    return result.scalar_one_or_none()


def require_organization_role(required_roles: list[str]):
    """Dependency factory to require specific organization roles"""
    async def check_role(
        org_member: Optional[OrganizationMember] = Depends(get_organization_member)
    ):
        if not org_member:
            raise InsufficientPermissionsError("Organization membership required")
        
        if org_member.role not in required_roles:
            raise InsufficientPermissionsError(
                f"Required role: {' or '.join(required_roles)}, current role: {org_member.role}"
            )
        
        return org_member
    
    return check_role


# Common role dependencies
require_admin = require_organization_role(["admin", "owner"])
require_member = require_organization_role(["member", "admin", "owner"])
require_viewer = require_organization_role(["viewer", "member", "admin", "owner"])
