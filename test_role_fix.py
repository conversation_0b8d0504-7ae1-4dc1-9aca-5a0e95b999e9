#!/usr/bin/env python3
"""
Test script to verify the role assignment fix
"""
import requests
import json
import time

# Test configuration
BASE_URL = "http://localhost:3001"
TEST_EMAIL = f"test_owner_{int(time.time())}@example.com"
TEST_PASSWORD = "TestPassword123!"

def test_registration_role_fix():
    """Test that registration correctly assigns owner role"""
    print("🧪 Testing Registration Role Assignment Fix...")
    
    # Test data
    registration_data = {
        "email": TEST_EMAIL,
        "password": TEST_PASSWORD,
        "firstName": "Test",
        "lastName": "Owner",
        "organizationName": "Test Organization",
        "organizationDomain": "example.com"
    }
    
    try:
        # 1. Register new user
        print(f"📝 Registering user: {TEST_EMAIL}")
        response = requests.post(f"{BASE_URL}/api/v1/auth/register", json=registration_data)
        
        if response.status_code != 200:
            print(f"❌ Registration failed: {response.status_code} - {response.text}")
            return False
            
        reg_result = response.json()
        print(f"✅ Registration successful")
        
        # Extract token
        if not reg_result.get("success") or not reg_result.get("data", {}).get("tokens", {}).get("access_token"):
            print(f"❌ No access token in registration response")
            return False
            
        access_token = reg_result["data"]["tokens"]["access_token"]
        print(f"🔑 Got access token")
        
        # Check role in registration response
        reg_role = reg_result.get("data", {}).get("role")
        print(f"📋 Registration response role: {reg_role}")
        
        # 2. Test /users/me endpoint
        print(f"🔍 Testing /users/me endpoint...")
        headers = {"Authorization": f"Bearer {access_token}"}
        me_response = requests.get(f"{BASE_URL}/api/v1/users/me", headers=headers)
        
        if me_response.status_code != 200:
            print(f"❌ /users/me failed: {me_response.status_code} - {me_response.text}")
            return False
            
        me_result = me_response.json()
        print(f"✅ /users/me successful")
        
        # Check user role in response
        user_role = me_result.get("data", {}).get("user", {}).get("role")
        print(f"👤 User role from /users/me: {user_role}")
        
        # Check organizations
        organizations = me_result.get("data", {}).get("organizations", [])
        print(f"🏢 Organizations count: {len(organizations)}")
        
        if organizations:
            org_role = organizations[0].get("role")
            print(f"🏢 First organization role: {org_role}")
        
        # Verify role is owner
        if user_role == "owner":
            print(f"✅ SUCCESS: User role correctly set to 'owner'")
            return True
        else:
            print(f"❌ FAILURE: User role is '{user_role}', expected 'owner'")
            return False
            
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        return False

def test_server_health():
    """Test if server is running"""
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print(f"✅ Server is running at {BASE_URL}")
            return True
        else:
            print(f"❌ Server health check failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Server not reachable: {e}")
        return False

if __name__ == "__main__":
    print("🚀 Starting Role Assignment Fix Test")
    print("=" * 50)
    
    # Check server health first
    if not test_server_health():
        print("❌ Server is not running. Please start the backend server first.")
        exit(1)
    
    # Run the test
    success = test_registration_role_fix()
    
    print("=" * 50)
    if success:
        print("🎉 TEST PASSED: Role assignment fix is working!")
    else:
        print("💥 TEST FAILED: Role assignment fix needs more work.")
    
    exit(0 if success else 1)
