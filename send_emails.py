#!/usr/bin/env python3
"""
Email Sender for Agno WorkSphere
Sends welcome and invitation emails
"""
import smtplib
import ssl
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from email.mime.base import MIMEBase
from email import encoders
import os
from datetime import datetime

class EmailSender:
    def __init__(self):
        # Email configuration from .env
        self.smtp_host = "smtp.gmail.com"
        self.smtp_port = 587
        self.smtp_user = "<EMAIL>"
        self.smtp_pass = "nhhkkbkauxvczkzf"
        self.from_email = "<EMAIL>"
        self.from_name = "Agno WorkSphere"
        
    def send_email(self, to_email, subject, html_content, text_content=None):
        """Send email with HTML content"""
        try:
            # Create message
            message = MIMEMultipart("alternative")
            message["Subject"] = subject
            message["From"] = f"{self.from_name} <{self.from_email}>"
            message["To"] = to_email
            
            # Create text and HTML parts
            if text_content:
                text_part = MIMEText(text_content, "plain")
                message.attach(text_part)
            
            html_part = MIMEText(html_content, "html")
            message.attach(html_part)
            
            # Create secure connection and send email
            context = ssl.create_default_context()
            with smtplib.SMTP(self.smtp_host, self.smtp_port) as server:
                server.starttls(context=context)
                server.login(self.smtp_user, self.smtp_pass)
                server.sendmail(self.from_email, to_email, message.as_string())
            
            return True, "Email sent successfully"
            
        except Exception as e:
            return False, str(e)
    
    def send_welcome_email(self, to_email, user_name="User"):
        """Send welcome email"""
        subject = "🎉 Welcome to Agno WorkSphere - Your Project Management Journey Begins!"
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Welcome to Agno WorkSphere</title>
            <style>
                body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }}
                .container {{ max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 0; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }}
                .header {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 40px 30px; text-align: center; border-radius: 10px 10px 0 0; }}
                .header h1 {{ margin: 0; font-size: 28px; font-weight: 300; }}
                .content {{ padding: 40px 30px; }}
                .welcome-message {{ font-size: 18px; color: #2c3e50; margin-bottom: 30px; }}
                .features {{ background-color: #f8f9fa; padding: 25px; border-radius: 8px; margin: 25px 0; }}
                .feature-item {{ margin: 15px 0; padding-left: 25px; position: relative; }}
                .feature-item:before {{ content: "✅"; position: absolute; left: 0; color: #27ae60; }}
                .cta-button {{ display: inline-block; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; font-weight: bold; margin: 20px 0; transition: transform 0.3s ease; }}
                .cta-button:hover {{ transform: translateY(-2px); }}
                .stats {{ display: flex; justify-content: space-around; margin: 30px 0; }}
                .stat {{ text-align: center; }}
                .stat-number {{ font-size: 24px; font-weight: bold; color: #667eea; }}
                .stat-label {{ font-size: 12px; color: #7f8c8d; text-transform: uppercase; }}
                .footer {{ background-color: #2c3e50; color: white; padding: 20px 30px; text-align: center; border-radius: 0 0 10px 10px; }}
                .footer a {{ color: #3498db; text-decoration: none; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🚀 Welcome to Agno WorkSphere</h1>
                    <p>Your Ultimate Project Management Platform</p>
                </div>
                
                <div class="content">
                    <div class="welcome-message">
                        <strong>Hello {user_name}!</strong><br><br>
                        Welcome to Agno WorkSphere! We're thrilled to have you join our community of productive teams and successful projects.
                    </div>
                    
                    <div class="stats">
                        <div class="stat">
                            <div class="stat-number">175+</div>
                            <div class="stat-label">API Endpoints</div>
                        </div>
                        <div class="stat">
                            <div class="stat-number">100%</div>
                            <div class="stat-label">Test Coverage</div>
                        </div>
                        <div class="stat">
                            <div class="stat-number">24/7</div>
                            <div class="stat-label">Support</div>
                        </div>
                    </div>
                    
                    <div class="features">
                        <h3>🎯 What You Can Do With Agno WorkSphere:</h3>
                        <div class="feature-item">Create and manage unlimited projects</div>
                        <div class="feature-item">Organize tasks with Kanban boards</div>
                        <div class="feature-item">Collaborate with team members in real-time</div>
                        <div class="feature-item">Track progress with advanced analytics</div>
                        <div class="feature-item">Integrate with your favorite tools</div>
                        <div class="feature-item">Secure role-based access control</div>
                        <div class="feature-item">AI-powered automation workflows</div>
                    </div>
                    
                    <div style="text-align: center;">
                        <a href="http://localhost:3000" class="cta-button">🚀 Start Your Journey</a>
                    </div>
                    
                    <div style="margin-top: 30px; padding: 20px; background-color: #e8f5e8; border-radius: 8px; border-left: 4px solid #27ae60;">
                        <h4>🎉 Your Account is Ready!</h4>
                        <p>Your Agno WorkSphere account has been successfully created and is ready to use. You can now:</p>
                        <ul>
                            <li>Create your first project</li>
                            <li>Invite team members</li>
                            <li>Set up your workspace</li>
                            <li>Explore all features</li>
                        </ul>
                    </div>
                    
                    <div style="margin-top: 30px; padding: 20px; background-color: #fff3cd; border-radius: 8px; border-left: 4px solid #ffc107;">
                        <h4>💡 Pro Tips for Getting Started:</h4>
                        <ol>
                            <li><strong>Complete your profile</strong> - Add your photo and bio</li>
                            <li><strong>Create your first project</strong> - Start with a simple project to get familiar</li>
                            <li><strong>Invite your team</strong> - Collaboration is key to success</li>
                            <li><strong>Explore the dashboard</strong> - Get insights into your productivity</li>
                        </ol>
                    </div>
                </div>
                
                <div class="footer">
                    <p>Need help? Contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>
                    <p>© 2025 Agno WorkSphere. Built with ❤️ for productive teams.</p>
                    <p><small>This email was sent to {to_email} on {datetime.now().strftime('%B %d, %Y at %I:%M %p')}</small></p>
                </div>
            </div>
        </body>
        </html>
        """
        
        text_content = f"""
        Welcome to Agno WorkSphere!
        
        Hello {user_name}!
        
        Welcome to Agno WorkSphere! We're thrilled to have you join our community of productive teams and successful projects.
        
        What You Can Do With Agno WorkSphere:
        ✅ Create and manage unlimited projects
        ✅ Organize tasks with Kanban boards
        ✅ Collaborate with team members in real-time
        ✅ Track progress with advanced analytics
        ✅ Integrate with your favorite tools
        ✅ Secure role-based access control
        ✅ AI-powered automation workflows
        
        Your Account is Ready!
        Your Agno WorkSphere account has been successfully created and is ready to use.
        
        Get started: http://localhost:3000
        
        Need help? Contact <NAME_EMAIL>
        
        © 2025 Agno WorkSphere. Built with ❤️ for productive teams.
        """
        
        return self.send_email(to_email, subject, html_content, text_content)
    
    def send_invitation_email(self, to_email, inviter_name="Team Member", organization_name="Your Organization"):
        """Send invitation email"""
        subject = f"🎯 You're Invited to Join {organization_name} on Agno WorkSphere!"
        
        html_content = f"""
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Invitation to Agno WorkSphere</title>
            <style>
                body {{ font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; line-height: 1.6; color: #333; margin: 0; padding: 0; background-color: #f4f4f4; }}
                .container {{ max-width: 600px; margin: 0 auto; background-color: #ffffff; padding: 0; border-radius: 10px; box-shadow: 0 0 20px rgba(0,0,0,0.1); }}
                .header {{ background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); color: white; padding: 40px 30px; text-align: center; border-radius: 10px 10px 0 0; }}
                .header h1 {{ margin: 0; font-size: 28px; font-weight: 300; }}
                .content {{ padding: 40px 30px; }}
                .invitation-message {{ font-size: 18px; color: #2c3e50; margin-bottom: 30px; }}
                .organization-info {{ background-color: #f8f9fa; padding: 25px; border-radius: 8px; margin: 25px 0; border-left: 4px solid #ff6b6b; }}
                .cta-button {{ display: inline-block; background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%); color: white; padding: 15px 30px; text-decoration: none; border-radius: 25px; font-weight: bold; margin: 20px 0; transition: transform 0.3s ease; }}
                .cta-button:hover {{ transform: translateY(-2px); }}
                .features {{ margin: 30px 0; }}
                .feature-item {{ margin: 15px 0; padding-left: 25px; position: relative; }}
                .feature-item:before {{ content: "🚀"; position: absolute; left: 0; }}
                .footer {{ background-color: #2c3e50; color: white; padding: 20px 30px; text-align: center; border-radius: 0 0 10px 10px; }}
                .footer a {{ color: #3498db; text-decoration: none; }}
            </style>
        </head>
        <body>
            <div class="container">
                <div class="header">
                    <h1>🎯 You're Invited!</h1>
                    <p>Join {organization_name} on Agno WorkSphere</p>
                </div>
                
                <div class="content">
                    <div class="invitation-message">
                        <strong>Great news!</strong><br><br>
                        <strong>{inviter_name}</strong> has invited you to join <strong>{organization_name}</strong> on Agno WorkSphere, the ultimate project management platform.
                    </div>
                    
                    <div class="organization-info">
                        <h3>🏢 About {organization_name}</h3>
                        <p>You've been invited to collaborate with this organization on Agno WorkSphere. Once you join, you'll be able to:</p>
                        <div class="features">
                            <div class="feature-item">Access shared projects and boards</div>
                            <div class="feature-item">Collaborate with team members in real-time</div>
                            <div class="feature-item">Track progress and deadlines</div>
                            <div class="feature-item">Participate in team discussions</div>
                            <div class="feature-item">Contribute to organizational goals</div>
                        </div>
                    </div>
                    
                    <div style="text-align: center;">
                        <a href="http://localhost:3000/register?invitation=true&org={organization_name}" class="cta-button">🚀 Accept Invitation</a>
                    </div>
                    
                    <div style="margin-top: 30px; padding: 20px; background-color: #e3f2fd; border-radius: 8px; border-left: 4px solid #2196f3;">
                        <h4>🔐 Secure & Professional</h4>
                        <p>Agno WorkSphere provides enterprise-grade security with:</p>
                        <ul>
                            <li>Role-based access control</li>
                            <li>Data encryption and privacy protection</li>
                            <li>Audit logs and compliance features</li>
                            <li>24/7 monitoring and support</li>
                        </ul>
                    </div>
                    
                    <div style="margin-top: 30px; padding: 20px; background-color: #f3e5f5; border-radius: 8px; border-left: 4px solid #9c27b0;">
                        <h4>🎯 Why Teams Choose Agno WorkSphere</h4>
                        <ul>
                            <li><strong>175+ API endpoints</strong> for complete functionality</li>
                            <li><strong>100% test coverage</strong> for reliability</li>
                            <li><strong>Real-time collaboration</strong> features</li>
                            <li><strong>AI-powered automation</strong> workflows</li>
                            <li><strong>Advanced analytics</strong> and reporting</li>
                        </ul>
                    </div>
                    
                    <div style="margin-top: 30px; text-align: center; padding: 20px; background-color: #fff8e1; border-radius: 8px;">
                        <p><strong>⏰ This invitation is waiting for you!</strong></p>
                        <p>Click the button above to join {organization_name} and start collaborating today.</p>
                    </div>
                </div>
                
                <div class="footer">
                    <p>Questions? Contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>
                    <p>© 2025 Agno WorkSphere. Empowering teams worldwide.</p>
                    <p><small>This invitation was sent to {to_email} on {datetime.now().strftime('%B %d, %Y at %I:%M %p')}</small></p>
                </div>
            </div>
        </body>
        </html>
        """
        
        text_content = f"""
        You're Invited to Join {organization_name} on Agno WorkSphere!
        
        Great news!
        
        {inviter_name} has invited you to join {organization_name} on Agno WorkSphere, the ultimate project management platform.
        
        About {organization_name}:
        You've been invited to collaborate with this organization on Agno WorkSphere. Once you join, you'll be able to:
        
        🚀 Access shared projects and boards
        🚀 Collaborate with team members in real-time
        🚀 Track progress and deadlines
        🚀 Participate in team discussions
        🚀 Contribute to organizational goals
        
        Accept your invitation: http://localhost:3000/register?invitation=true&org={organization_name}
        
        Why Teams Choose Agno WorkSphere:
        • 175+ API endpoints for complete functionality
        • 100% test coverage for reliability
        • Real-time collaboration features
        • AI-powered automation workflows
        • Advanced analytics and reporting
        
        Questions? Contact <NAME_EMAIL>
        
        © 2025 Agno WorkSphere. Empowering teams worldwide.
        """
        
        return self.send_email(to_email, subject, html_content, text_content)

def main():
    """Send welcome and invitation emails"""
    email_sender = EmailSender()
    target_email = "<EMAIL>"
    
    print("📧 Sending Welcome and Invitation Emails")
    print("=" * 50)
    
    # Send welcome email
    print("📨 Sending welcome email...")
    success, message = email_sender.send_welcome_email(target_email, "Vishnu Balaguru")
    if success:
        print("✅ Welcome email sent successfully!")
    else:
        print(f"❌ Failed to send welcome email: {message}")
    
    # Send invitation email
    print("📨 Sending invitation email...")
    success, message = email_sender.send_invitation_email(target_email, "Agno WorkSphere Team", "Agno WorkSphere Development")
    if success:
        print("✅ Invitation email sent successfully!")
    else:
        print(f"❌ Failed to send invitation email: {message}")
    
    print(f"\n📬 Both emails have been sent to: {target_email}")
    print("🎉 Email delivery complete!")

if __name__ == "__main__":
    main()
