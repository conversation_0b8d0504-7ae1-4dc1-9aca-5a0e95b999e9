#!/usr/bin/env python3
"""
Master Test Suite for Agno WorkSphere
Orchestrates comprehensive end-to-end testing including:
- Backend API coverage (175+ endpoints)
- Frontend functionality
- Role-based access control
- End-to-end user flows
"""
import subprocess
import sys
import time
import json
import os
from typing import Dict, List, Any
from dataclasses import dataclass

@dataclass
class TestSuiteResult:
    name: str
    status: str  # PASS, FAIL, SKIP
    execution_time: float
    details: Dict[str, Any]
    error_message: str = None

class MasterTestSuite:
    def __init__(self):
        self.results: List[TestSuiteResult] = []
        self.start_time = time.time()
        
    def check_prerequisites(self) -> bool:
        """Check if all prerequisites are met"""
        print("🔍 Checking Prerequisites...")
        print("-" * 50)
        
        prerequisites = []
        
        # Check if backend server is running
        try:
            import requests
            response = requests.get("http://localhost:3001/health", timeout=5)
            if response.status_code == 200:
                print("✅ Backend server is running")
                prerequisites.append(True)
            else:
                print("❌ Backend server is not responding correctly")
                prerequisites.append(False)
        except Exception as e:
            print(f"❌ Backend server is not accessible: {e}")
            prerequisites.append(False)
        
        # Check if frontend server is running
        try:
            response = requests.get("http://localhost:3000", timeout=5)
            if response.status_code == 200:
                print("✅ Frontend server is running")
                prerequisites.append(True)
            else:
                print("❌ Frontend server is not responding correctly")
                prerequisites.append(False)
        except Exception as e:
            print(f"❌ Frontend server is not accessible: {e}")
            prerequisites.append(False)
        
        # Check if required Python packages are installed
        required_packages = ['requests', 'selenium']
        for package in required_packages:
            try:
                __import__(package)
                print(f"✅ {package} is installed")
                prerequisites.append(True)
            except ImportError:
                print(f"❌ {package} is not installed")
                prerequisites.append(False)
        
        # Check if ChromeDriver is available (for frontend tests)
        try:
            from selenium import webdriver
            from selenium.webdriver.chrome.options import Options
            
            chrome_options = Options()
            chrome_options.add_argument("--headless")
            chrome_options.add_argument("--no-sandbox")
            chrome_options.add_argument("--disable-dev-shm-usage")
            
            driver = webdriver.Chrome(options=chrome_options)
            driver.quit()
            print("✅ ChromeDriver is available")
            prerequisites.append(True)
        except Exception as e:
            print(f"⚠️ ChromeDriver not available (frontend tests will be skipped): {e}")
            prerequisites.append(False)
        
        all_good = all(prerequisites[:2])  # Backend and frontend are essential
        print(f"\n{'✅' if all_good else '❌'} Prerequisites check {'passed' if all_good else 'failed'}")
        return all_good
    
    def run_test_suite(self, script_name: str, suite_name: str, timeout: int = 300) -> TestSuiteResult:
        """Run a specific test suite"""
        print(f"\n🚀 Running {suite_name}")
        print("=" * 60)
        
        start_time = time.time()
        
        try:
            # Run the test script
            result = subprocess.run(
                [sys.executable, script_name],
                capture_output=True,
                text=True,
                timeout=timeout,
                cwd=os.getcwd()
            )
            
            execution_time = time.time() - start_time
            
            # Print the output
            if result.stdout:
                print(result.stdout)
            
            if result.stderr:
                print("STDERR:", result.stderr)
            
            # Determine status
            if result.returncode == 0:
                status = "PASS"
                error_message = None
            else:
                status = "FAIL"
                error_message = f"Exit code: {result.returncode}"
                if result.stderr:
                    error_message += f", Error: {result.stderr[:200]}"
            
            # Try to parse results from output files
            details = self.parse_test_results(script_name)
            
            return TestSuiteResult(
                name=suite_name,
                status=status,
                execution_time=execution_time,
                details=details,
                error_message=error_message
            )
            
        except subprocess.TimeoutExpired:
            execution_time = time.time() - start_time
            return TestSuiteResult(
                name=suite_name,
                status="FAIL",
                execution_time=execution_time,
                details={},
                error_message=f"Test suite timed out after {timeout} seconds"
            )
        except Exception as e:
            execution_time = time.time() - start_time
            return TestSuiteResult(
                name=suite_name,
                status="FAIL",
                execution_time=execution_time,
                details={},
                error_message=str(e)
            )
    
    def parse_test_results(self, script_name: str) -> Dict[str, Any]:
        """Parse test results from JSON files"""
        details = {}
        
        # Look for result files based on script name
        if "api" in script_name:
            pattern = "api_test_results_*.json"
        elif "frontend" in script_name:
            pattern = "frontend_test_results_*.json"
        elif "rbac" in script_name:
            pattern = "rbac_test_results_*.json"
        else:
            return details
        
        # Find the most recent result file
        import glob
        files = glob.glob(pattern)
        if files:
            latest_file = max(files, key=os.path.getctime)
            try:
                with open(latest_file, 'r') as f:
                    details = json.load(f)
            except Exception as e:
                details = {"error": f"Failed to parse results: {e}"}
        
        return details
    
    def run_all_tests(self):
        """Run all test suites"""
        print("🎯 AGNO WORKSPHERE - COMPREHENSIVE END-TO-END TESTING")
        print("=" * 80)
        print(f"Started at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        # Check prerequisites
        if not self.check_prerequisites():
            print("\n💥 Prerequisites not met. Please ensure:")
            print("   1. Backend server is running on http://localhost:3001")
            print("   2. Frontend server is running on http://localhost:3000")
            print("   3. Required Python packages are installed")
            return
        
        # Test suites to run
        test_suites = [
            ("test_comprehensive_api.py", "Backend API Coverage Testing", 600),
            ("test_rbac_comprehensive.py", "Role-Based Access Control Testing", 400),
            ("test_frontend_comprehensive.py", "Frontend Functionality Testing", 300),
            ("test_complete_flow.py", "End-to-End User Flow Testing", 200)
        ]
        
        # Run each test suite
        for script, name, timeout in test_suites:
            if os.path.exists(script):
                result = self.run_test_suite(script, name, timeout)
                self.results.append(result)
            else:
                print(f"⏭️ Skipping {name} - {script} not found")
                self.results.append(TestSuiteResult(
                    name=name,
                    status="SKIP",
                    execution_time=0,
                    details={},
                    error_message=f"Script {script} not found"
                ))
        
        # Generate comprehensive report
        self.generate_final_report()
    
    def generate_final_report(self):
        """Generate comprehensive test report"""
        total_time = time.time() - self.start_time
        
        print("\n" + "=" * 80)
        print("📊 COMPREHENSIVE TEST REPORT")
        print("=" * 80)
        
        # Overall summary
        total_suites = len(self.results)
        passed_suites = len([r for r in self.results if r.status == "PASS"])
        failed_suites = len([r for r in self.results if r.status == "FAIL"])
        skipped_suites = len([r for r in self.results if r.status == "SKIP"])
        
        print(f"📈 Overall Results:")
        print(f"   Total Test Suites: {total_suites}")
        print(f"   ✅ Passed: {passed_suites}")
        print(f"   ❌ Failed: {failed_suites}")
        print(f"   ⏭️ Skipped: {skipped_suites}")
        print(f"   ⏱️ Total Execution Time: {total_time:.2f}s")
        print(f"   📊 Success Rate: {(passed_suites/total_suites*100):.1f}%" if total_suites > 0 else "N/A")
        
        # Detailed results for each suite
        print(f"\n📋 Detailed Results:")
        for result in self.results:
            status_emoji = "✅" if result.status == "PASS" else "❌" if result.status == "FAIL" else "⏭️"
            print(f"\n{status_emoji} {result.name}")
            print(f"   Status: {result.status}")
            print(f"   Execution Time: {result.execution_time:.2f}s")
            
            if result.error_message:
                print(f"   Error: {result.error_message}")
            
            # Show summary from details if available
            if result.details and "summary" in result.details:
                summary = result.details["summary"]
                if "total" in summary:
                    print(f"   Tests: {summary.get('passed', 0)}/{summary.get('total', 0)} passed")
        
        # Application readiness assessment
        self.assess_application_readiness()
        
        # Save comprehensive report
        self.save_comprehensive_report(total_time)
    
    def assess_application_readiness(self):
        """Assess overall application readiness"""
        print(f"\n🎯 APPLICATION READINESS ASSESSMENT")
        print("-" * 50)
        
        readiness_score = 0
        max_score = 0
        
        for result in self.results:
            max_score += 25  # Each suite worth 25 points
            
            if result.status == "PASS":
                readiness_score += 25
            elif result.status == "FAIL":
                # Partial credit based on success rate if available
                if result.details and "summary" in result.details:
                    summary = result.details["summary"]
                    if "total" in summary and summary["total"] > 0:
                        success_rate = summary.get("passed", 0) / summary["total"]
                        readiness_score += int(25 * success_rate)
        
        readiness_percentage = (readiness_score / max_score * 100) if max_score > 0 else 0
        
        print(f"📊 Readiness Score: {readiness_score}/{max_score} ({readiness_percentage:.1f}%)")
        
        if readiness_percentage >= 90:
            print("🟢 PRODUCTION READY - Application is ready for deployment")
        elif readiness_percentage >= 75:
            print("🟡 MOSTLY READY - Minor issues need to be addressed")
        elif readiness_percentage >= 50:
            print("🟠 NEEDS WORK - Significant issues need to be resolved")
        else:
            print("🔴 NOT READY - Major issues prevent production deployment")
        
        print(f"\n📝 Recommendations:")
        failed_suites = [r for r in self.results if r.status == "FAIL"]
        if not failed_suites:
            print("   ✅ All test suites passed successfully")
            print("   ✅ Application appears to be functioning correctly")
            print("   ✅ Ready for production deployment")
        else:
            print("   📋 Address the following issues:")
            for result in failed_suites:
                print(f"      - Fix issues in {result.name}")
                if result.error_message:
                    print(f"        Error: {result.error_message}")
    
    def save_comprehensive_report(self, total_time: float):
        """Save comprehensive report to file"""
        report_data = {
            "timestamp": time.time(),
            "execution_time": total_time,
            "summary": {
                "total_suites": len(self.results),
                "passed_suites": len([r for r in self.results if r.status == "PASS"]),
                "failed_suites": len([r for r in self.results if r.status == "FAIL"]),
                "skipped_suites": len([r for r in self.results if r.status == "SKIP"])
            },
            "test_suites": [
                {
                    "name": r.name,
                    "status": r.status,
                    "execution_time": r.execution_time,
                    "error_message": r.error_message,
                    "details": r.details
                }
                for r in self.results
            ]
        }
        
        filename = f"comprehensive_test_report_{int(time.time())}.json"
        with open(filename, 'w') as f:
            json.dump(report_data, f, indent=2)
        
        print(f"\n💾 Comprehensive report saved to: {filename}")

if __name__ == "__main__":
    master_suite = MasterTestSuite()
    master_suite.run_all_tests()
