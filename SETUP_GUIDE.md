# Agno WorkSphere - Complete Setup Guide

This guide will help you set up and run the complete Agno WorkSphere application with both frontend and backend.

## Prerequisites

Before you begin, ensure you have the following installed:

- **Node.js** (v16 or higher) - [Download here](https://nodejs.org/)
- **PostgreSQL** (v12 or higher) - [Download here](https://www.postgresql.org/download/)
- **Redis** (v6 or higher) - [Download here](https://redis.io/download)
- **Git** - [Download here](https://git-scm.com/)

## Quick Start

### 1. Clone and Setup

```bash
# Clone the repository
git clone <repository-url>
cd PM

# Install backend dependencies
cd backend
npm install

# Install frontend dependencies
cd ../agnoworksphere
npm install
```

### 2. Database Setup

```bash
# Start PostgreSQL service (varies by OS)
# Windows: Start PostgreSQL service from Services
# macOS: brew services start postgresql
# Linux: sudo systemctl start postgresql

# Create database
psql -U postgres
CREATE DATABASE agno_worksphere;
\q

# Run migrations (from backend directory)
cd backend
npm run migrate

# Seed with demo data (optional)
npm run seed
```

### 3. Redis Setup

```bash
# Start Redis service (varies by OS)
# Windows: Start Redis service or run redis-server.exe
# macOS: brew services start redis
# Linux: sudo systemctl start redis
```

### 4. Environment Configuration

**Backend (.env):**
```bash
cd backend
cp .env.example .env
# Edit .env with your database credentials
```

**Frontend (.env):**
```bash
cd agnoworksphere
cp .env.example .env
# Default configuration should work for local development
```

### 5. Start the Application

**Terminal 1 - Backend:**
```bash
cd backend
npm run dev
```

**Terminal 2 - Frontend:**
```bash
cd agnoworksphere
npm start
```

The application will be available at:
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:3001
- **API Health Check**: http://localhost:3001/health

## Demo Account

After seeding the database, you can use these demo accounts:

**Admin User:**
- Email: <EMAIL>
- Password: password123

**Regular User:**
- Email: <EMAIL>
- Password: password123

## Features Available

### ✅ Implemented Features

1. **Authentication System**
   - User registration and login
   - JWT token-based authentication
   - Password reset functionality
   - Email verification (development mode)
   - Two-factor authentication setup

2. **User Management**
   - User profile management
   - Avatar upload
   - Notification preferences
   - Account settings

3. **Organization Management**
   - Create and manage organizations
   - Member invitation system
   - Role-based access control (Owner, Admin, Member, Viewer)
   - Organization settings and branding

4. **Project Management**
   - Create and manage projects
   - Project status tracking
   - Project-level permissions

5. **Kanban Board System**
   - Create multiple boards per project
   - Default board creation with standard columns
   - Board customization (colors, backgrounds)
   - Column and card management

### 🚧 Partially Implemented

6. **Card Management**
   - Basic card structure in database
   - Card CRUD operations (backend ready)
   - Frontend integration pending

7. **Real-time Collaboration**
   - WebSocket server setup
   - Basic connection handling
   - Real-time updates pending

### 📋 Planned Features

8. **File Upload System**
   - Avatar and logo uploads working
   - Card attachments pending
   - File management system

9. **Advanced Features**
   - Comments and mentions
   - Activity tracking
   - Email notifications
   - Third-party integrations
   - Advanced reporting

## API Documentation

### Authentication Endpoints

```bash
# Register
POST /api/auth/register
{
  "email": "<EMAIL>",
  "password": "password123",
  "firstName": "John",
  "lastName": "Doe"
}

# Login
POST /api/auth/login
{
  "email": "<EMAIL>",
  "password": "password123"
}

# Get Profile
GET /api/users/profile
Headers: Authorization: Bearer <token>
```

### Organization Endpoints

```bash
# Get Organizations
GET /api/organizations
Headers: Authorization: Bearer <token>

# Create Organization
POST /api/organizations
Headers: Authorization: Bearer <token>
{
  "name": "My Organization",
  "description": "Organization description"
}
```

### Project Endpoints

```bash
# Get Projects
GET /api/projects
Headers: 
  Authorization: Bearer <token>
  X-Organization-ID: <org-uuid>

# Create Project
POST /api/projects
Headers: 
  Authorization: Bearer <token>
  X-Organization-ID: <org-uuid>
{
  "name": "My Project",
  "description": "Project description"
}
```

## Troubleshooting

### Common Issues

1. **Database Connection Error**
   - Ensure PostgreSQL is running
   - Check database credentials in .env
   - Verify database exists

2. **Redis Connection Error**
   - Ensure Redis is running
   - Check Redis URL in .env

3. **Port Already in Use**
   - Backend: Change PORT in backend/.env
   - Frontend: Change port when prompted or set PORT in frontend/.env

4. **CORS Errors**
   - Ensure FRONTEND_URL is correct in backend/.env
   - Check API_URL in frontend/.env

### Reset Database

```bash
cd backend
npm run seed:reset
```

### View Logs

```bash
# Backend logs
cd backend
npm run dev

# Frontend logs
cd agnoworksphere
npm start
```

## Development

### Backend Development

```bash
cd backend
npm run dev          # Start with nodemon
npm run migrate      # Run migrations
npm run seed         # Seed database
npm test            # Run tests
```

### Frontend Development

```bash
cd agnoworksphere
npm start           # Start development server
npm test            # Run tests
npm run build       # Build for production
```

## Production Deployment

### Backend

1. Set NODE_ENV=production
2. Configure production database
3. Set strong JWT secrets
4. Configure email service
5. Set up file storage (AWS S3)
6. Configure reverse proxy (nginx)

### Frontend

1. Build the application: `npm run build`
2. Serve static files
3. Configure environment variables
4. Set up CDN (optional)

## Support

For issues and questions:
1. Check this setup guide
2. Review the troubleshooting section
3. Check backend/README.md for detailed API documentation
4. Create an issue in the repository

## Next Steps

1. Complete card management frontend integration
2. Implement real-time collaboration features
3. Add file attachment system
4. Implement email notifications
5. Add third-party integrations
6. Create comprehensive test suite
7. Set up CI/CD pipeline
