"""
AI and automation API endpoints
"""
from fastapi import APIRouter, Depends, HTTPException, status, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from uuid import UUID
from datetime import datetime

from app.core.database import get_db
from app.core.auth import get_current_user
from app.models.user import User
from app.models.organization import Organization, OrganizationMember
from app.models.ai_automation import (
    WorkflowRule, WorkflowExecution, AIModel, AIPrediction,
    SmartNotification, CustomField, CustomFieldValue,
    AutomationTemplate, AIInsight
)
from app.schemas.ai_automation import (
    WorkflowRuleCreate, WorkflowRuleResponse,
    AIPredictionResponse, SmartNotificationResponse,
    CustomFieldCreate, CustomFieldResponse,
    AutomationTemplateResponse, AIInsightResponse
)
from app.services.ai_service import AIService
from app.services.automation_service import AutomationService

router = APIRouter()


@router.post("/organizations/{organization_id}/workflow-rules", response_model=WorkflowRuleResponse)
async def create_workflow_rule(
    organization_id: UUID,
    rule_data: WorkflowRuleCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a new workflow automation rule"""
    # Check if user is admin/owner of the organization
    member = db.query(OrganizationMember).filter(
        OrganizationMember.organization_id == organization_id,
        OrganizationMember.user_id == current_user.id,
        OrganizationMember.role.in_(['owner', 'admin'])
    ).first()
    
    if not member:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only owners and admins can create workflow rules"
        )
    
    rule = WorkflowRule(
        organization_id=organization_id,
        project_id=rule_data.project_id,
        rule_name=rule_data.rule_name,
        description=rule_data.description,
        trigger_type=rule_data.trigger_type,
        trigger_conditions=rule_data.trigger_conditions,
        actions=rule_data.actions,
        priority=rule_data.priority,
        created_by=current_user.id
    )
    
    db.add(rule)
    db.commit()
    db.refresh(rule)
    
    return rule


@router.get("/organizations/{organization_id}/workflow-rules", response_model=List[WorkflowRuleResponse])
async def get_workflow_rules(
    organization_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get all workflow rules for organization"""
    # Check if user has access to this organization
    member = db.query(OrganizationMember).filter(
        OrganizationMember.organization_id == organization_id,
        OrganizationMember.user_id == current_user.id
    ).first()
    
    if not member:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to organization"
        )
    
    rules = db.query(WorkflowRule).filter(
        WorkflowRule.organization_id == organization_id
    ).order_by(WorkflowRule.priority.desc(), WorkflowRule.created_at.desc()).all()
    
    return rules


@router.put("/organizations/{organization_id}/workflow-rules/{rule_id}/toggle")
async def toggle_workflow_rule(
    organization_id: UUID,
    rule_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Toggle workflow rule active status"""
    # Check if user is admin/owner of the organization
    member = db.query(OrganizationMember).filter(
        OrganizationMember.organization_id == organization_id,
        OrganizationMember.user_id == current_user.id,
        OrganizationMember.role.in_(['owner', 'admin'])
    ).first()
    
    if not member:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only owners and admins can modify workflow rules"
        )
    
    rule = db.query(WorkflowRule).filter(
        WorkflowRule.id == rule_id,
        WorkflowRule.organization_id == organization_id
    ).first()
    
    if not rule:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Workflow rule not found"
        )
    
    rule.is_active = not rule.is_active
    db.commit()
    
    return {"message": f"Workflow rule {'activated' if rule.is_active else 'deactivated'} successfully"}


@router.post("/organizations/{organization_id}/ai/predict")
async def get_ai_prediction(
    organization_id: UUID,
    prediction_request: Dict[str, Any],
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get AI prediction for an entity"""
    # Check if user has access to this organization
    member = db.query(OrganizationMember).filter(
        OrganizationMember.organization_id == organization_id,
        OrganizationMember.user_id == current_user.id
    ).first()
    
    if not member:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to organization"
        )
    
    ai_service = AIService(db)
    
    # Process prediction in background
    background_tasks.add_task(
        ai_service.generate_prediction,
        organization_id,
        prediction_request.get('entity_type'),
        prediction_request.get('entity_id'),
        prediction_request.get('prediction_type'),
        prediction_request.get('input_data', {})
    )
    
    return {"message": "Prediction request submitted", "status": "processing"}


@router.get("/organizations/{organization_id}/ai/predictions", response_model=List[AIPredictionResponse])
async def get_ai_predictions(
    organization_id: UUID,
    entity_type: Optional[str] = None,
    entity_id: Optional[UUID] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get AI predictions for organization"""
    # Check if user has access to this organization
    member = db.query(OrganizationMember).filter(
        OrganizationMember.organization_id == organization_id,
        OrganizationMember.user_id == current_user.id
    ).first()
    
    if not member:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to organization"
        )
    
    query = db.query(AIPrediction).filter(
        AIPrediction.organization_id == organization_id
    )
    
    if entity_type:
        query = query.filter(AIPrediction.entity_type == entity_type)
    
    if entity_id:
        query = query.filter(AIPrediction.entity_id == entity_id)
    
    predictions = query.order_by(AIPrediction.created_at.desc()).limit(100).all()
    
    return predictions


@router.get("/organizations/{organization_id}/smart-notifications", response_model=List[SmartNotificationResponse])
async def get_smart_notifications(
    organization_id: UUID,
    unread_only: bool = False,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get smart notifications for user"""
    # Check if user has access to this organization
    member = db.query(OrganizationMember).filter(
        OrganizationMember.organization_id == organization_id,
        OrganizationMember.user_id == current_user.id
    ).first()
    
    if not member:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to organization"
        )
    
    query = db.query(SmartNotification).filter(
        SmartNotification.organization_id == organization_id,
        SmartNotification.user_id == current_user.id
    )
    
    if unread_only:
        query = query.filter(SmartNotification.read_at.is_(None))
    
    notifications = query.order_by(SmartNotification.created_at.desc()).limit(50).all()
    
    return notifications


@router.put("/organizations/{organization_id}/smart-notifications/{notification_id}/read")
async def mark_notification_read(
    organization_id: UUID,
    notification_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Mark smart notification as read"""
    notification = db.query(SmartNotification).filter(
        SmartNotification.id == notification_id,
        SmartNotification.organization_id == organization_id,
        SmartNotification.user_id == current_user.id
    ).first()
    
    if not notification:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Notification not found"
        )
    
    notification.read_at = datetime.utcnow()
    db.commit()
    
    return {"message": "Notification marked as read"}


@router.post("/organizations/{organization_id}/custom-fields", response_model=CustomFieldResponse)
async def create_custom_field(
    organization_id: UUID,
    field_data: CustomFieldCreate,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Create a custom field"""
    # Check if user is admin/owner of the organization
    member = db.query(OrganizationMember).filter(
        OrganizationMember.organization_id == organization_id,
        OrganizationMember.user_id == current_user.id,
        OrganizationMember.role.in_(['owner', 'admin'])
    ).first()
    
    if not member:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only owners and admins can create custom fields"
        )
    
    custom_field = CustomField(
        organization_id=organization_id,
        field_name=field_data.field_name,
        field_type=field_data.field_type,
        entity_type=field_data.entity_type,
        description=field_data.description,
        field_options=field_data.field_options,
        validation_rules=field_data.validation_rules,
        is_required=field_data.is_required,
        is_searchable=field_data.is_searchable,
        display_order=field_data.display_order,
        created_by=current_user.id
    )
    
    db.add(custom_field)
    db.commit()
    db.refresh(custom_field)
    
    return custom_field


@router.get("/organizations/{organization_id}/custom-fields", response_model=List[CustomFieldResponse])
async def get_custom_fields(
    organization_id: UUID,
    entity_type: Optional[str] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get custom fields for organization"""
    # Check if user has access to this organization
    member = db.query(OrganizationMember).filter(
        OrganizationMember.organization_id == organization_id,
        OrganizationMember.user_id == current_user.id
    ).first()
    
    if not member:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to organization"
        )
    
    query = db.query(CustomField).filter(
        CustomField.organization_id == organization_id,
        CustomField.is_active == True
    )
    
    if entity_type:
        query = query.filter(CustomField.entity_type == entity_type)
    
    fields = query.order_by(CustomField.display_order, CustomField.field_name).all()
    
    return fields


@router.get("/automation-templates", response_model=List[AutomationTemplateResponse])
async def get_automation_templates(
    category: Optional[str] = None,
    featured_only: bool = False,
    db: Session = Depends(get_db)
):
    """Get automation templates"""
    query = db.query(AutomationTemplate).filter(
        AutomationTemplate.is_public == True
    )
    
    if category:
        query = query.filter(AutomationTemplate.category == category)
    
    if featured_only:
        query = query.filter(AutomationTemplate.is_featured == True)
    
    templates = query.order_by(
        AutomationTemplate.is_featured.desc(),
        AutomationTemplate.rating.desc(),
        AutomationTemplate.usage_count.desc()
    ).all()
    
    return templates


@router.post("/organizations/{organization_id}/automation-templates/{template_id}/apply")
async def apply_automation_template(
    organization_id: UUID,
    template_id: UUID,
    customizations: Optional[Dict[str, Any]] = None,
    background_tasks: BackgroundTasks,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Apply an automation template to organization"""
    # Check if user is admin/owner of the organization
    member = db.query(OrganizationMember).filter(
        OrganizationMember.organization_id == organization_id,
        OrganizationMember.user_id == current_user.id,
        OrganizationMember.role.in_(['owner', 'admin'])
    ).first()
    
    if not member:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only owners and admins can apply automation templates"
        )
    
    template = db.query(AutomationTemplate).filter(
        AutomationTemplate.id == template_id,
        AutomationTemplate.is_public == True
    ).first()
    
    if not template:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Automation template not found"
        )
    
    # Apply template in background
    automation_service = AutomationService(db)
    background_tasks.add_task(
        automation_service.apply_template,
        organization_id,
        template_id,
        current_user.id,
        customizations or {}
    )
    
    # Update usage count
    template.usage_count += 1
    db.commit()
    
    return {"message": "Automation template is being applied", "status": "processing"}


@router.get("/organizations/{organization_id}/ai-insights", response_model=List[AIInsightResponse])
async def get_ai_insights(
    organization_id: UUID,
    insight_type: Optional[str] = None,
    active_only: bool = True,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Get AI insights for organization"""
    # Check if user has access to this organization
    member = db.query(OrganizationMember).filter(
        OrganizationMember.organization_id == organization_id,
        OrganizationMember.user_id == current_user.id
    ).first()
    
    if not member:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to organization"
        )
    
    query = db.query(AIInsight).filter(
        AIInsight.organization_id == organization_id
    )
    
    if insight_type:
        query = query.filter(AIInsight.insight_type == insight_type)
    
    if active_only:
        query = query.filter(AIInsight.is_dismissed == False)
    
    insights = query.order_by(
        AIInsight.impact_score.desc(),
        AIInsight.confidence_level.desc(),
        AIInsight.created_at.desc()
    ).limit(20).all()
    
    return insights


@router.put("/organizations/{organization_id}/ai-insights/{insight_id}/dismiss")
async def dismiss_ai_insight(
    organization_id: UUID,
    insight_id: UUID,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Dismiss an AI insight"""
    # Check if user has access to this organization
    member = db.query(OrganizationMember).filter(
        OrganizationMember.organization_id == organization_id,
        OrganizationMember.user_id == current_user.id
    ).first()
    
    if not member:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Access denied to organization"
        )
    
    insight = db.query(AIInsight).filter(
        AIInsight.id == insight_id,
        AIInsight.organization_id == organization_id
    ).first()
    
    if not insight:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="AI insight not found"
        )
    
    insight.is_dismissed = True
    insight.dismissed_by = current_user.id
    insight.dismissed_at = datetime.utcnow()
    db.commit()
    
    return {"message": "AI insight dismissed successfully"}
