#!/usr/bin/env python3
"""
Simple test server to test registration endpoint
"""
from typing import Optional
from fastapi import Fast<PERSON><PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel, EmailStr, ValidationError
import uvicorn

app = FastAPI(title="Registration Test Server")

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

class UserRegister(BaseModel):
    email: EmailStr
    password: str
    first_name: str
    last_name: Optional[str] = ""
    organization_name: Optional[str] = None
    organization_slug: Optional[str] = None

@app.post("/api/v1/auth/register")
async def register_user(user_data: UserRegister):
    """Test registration endpoint"""
    try:
        print(f"📝 Registration attempt:")
        print(f"   Email: {user_data.email}")
        print(f"   First Name: {user_data.first_name}")
        print(f"   Last Name: '{user_data.last_name}'")
        print(f"   Organization: {user_data.organization_name}")
        print(f"   Org Slug: {user_data.organization_slug}")
        
        # Simulate successful registration
        return {
            "success": True,
            "message": "Registration successful!",
            "user": {
                "email": user_data.email,
                "first_name": user_data.first_name,
                "last_name": user_data.last_name,
                "organization_name": user_data.organization_name
            }
        }
    except Exception as e:
        print(f"❌ Registration error: {str(e)}")
        raise HTTPException(status_code=400, detail=str(e))

@app.get("/api/v1/test/status")
async def test_status():
    """Test endpoint to verify server is running"""
    return {"status": "Server is running", "message": "Registration test server is ready"}

if __name__ == "__main__":
    print("🚀 Starting Registration Test Server")
    print("📍 Server will be available at: http://localhost:3001")
    print("🧪 Test endpoint: http://localhost:3001/api/v1/test/status")
    
    uvicorn.run(
        "simple_test_server:app",
        host="0.0.0.0",
        port=3001,
        reload=False,  # Disable reload to avoid issues
        log_level="info"
    )
