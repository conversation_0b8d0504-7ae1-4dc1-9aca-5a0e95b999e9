#!/usr/bin/env python3
"""
Comprehensive API Testing Suite
Tests all endpoints including registration, authentication, and CRUD operations
"""

import requests
import json
import time
import uuid
from typing import Dict, Optional

# Configuration
API_BASE_URL = "http://localhost:3001"
TEST_USER_EMAIL = "<EMAIL>"
TEST_USER_PASSWORD = "Owner123!"

class ComprehensiveAPITester:
    def __init__(self):
        self.access_token = None
        self.user_data = None
        self.organization_id = None
        self.project_id = None
        self.board_id = None
        self.card_id = None
        self.test_results = []
        
    def log_result(self, test_name: str, success: bool, details: str = ""):
        """Log test result"""
        status = "✅ PASS" if success else "❌ FAIL"
        self.test_results.append({
            "test": test_name,
            "status": status,
            "success": success,
            "details": details
        })
        print(f"{status} {test_name}" + (f" - {details}" if details else ""))
    
    def make_request(self, method: str, endpoint: str, data: dict = None, headers: dict = None) -> tuple:
        """Make HTTP request and return (success, response, status_code)"""
        url = f"{API_BASE_URL}{endpoint}"
        
        if headers is None:
            headers = {}
        
        if self.access_token and "Authorization" not in headers:
            headers["Authorization"] = f"Bearer {self.access_token}"
        
        headers["Content-Type"] = "application/json"
        
        try:
            if method == "GET":
                response = requests.get(url, headers=headers)
            elif method == "POST":
                response = requests.post(url, headers=headers, json=data)
            elif method == "PUT":
                response = requests.put(url, headers=headers, json=data)
            elif method == "DELETE":
                response = requests.delete(url, headers=headers)
            else:
                return False, None, 0
            
            return response.status_code < 400, response, response.status_code
        except Exception as e:
            return False, str(e), 0
    
    def test_health_endpoints(self):
        """Test health and status endpoints"""
        print("\n🏥 Testing Health & Status Endpoints")
        print("-" * 50)
        
        # Test health endpoint
        success, response, status = self.make_request("GET", "/health")
        self.log_result("GET /health", success, f"Status: {status}")
        
        # Test API root
        success, response, status = self.make_request("GET", "/api/v1/")
        self.log_result("GET /api/v1/", success, f"Status: {status}")
    
    def test_registration(self):
        """Test user registration"""
        print("\n📝 Testing Registration Endpoints")
        print("-" * 50)
        
        # Test registration with new user
        test_email = f"test_{int(time.time())}@example.com"
        registration_data = {
            "email": test_email,
            "password": "TestPass123!",
            "first_name": "Test",
            "last_name": "User",
            "organization_name": "Test Organization"
        }
        
        success, response, status = self.make_request("POST", "/api/v1/auth/register", registration_data)
        if success and response:
            try:
                data = response.json()
                self.log_result("POST /api/v1/auth/register", True, "Registration successful")
            except:
                self.log_result("POST /api/v1/auth/register", False, f"Invalid JSON response")
        else:
            self.log_result("POST /api/v1/auth/register", False, f"Status: {status}")
        
        # Test registration with duplicate email
        success, response, status = self.make_request("POST", "/api/v1/auth/register", registration_data)
        expected_fail = status == 400 or status == 409  # Should fail with conflict
        self.log_result("POST /api/v1/auth/register (duplicate)", expected_fail, f"Status: {status}")
    
    def test_authentication(self):
        """Test authentication endpoints"""
        print("\n🔐 Testing Authentication Endpoints")
        print("-" * 50)
        
        # Test login with valid credentials
        login_data = {
            "email": TEST_USER_EMAIL,
            "password": TEST_USER_PASSWORD
        }
        
        success, response, status = self.make_request("POST", "/api/v1/auth/login", login_data)
        if success and response:
            try:
                data = response.json()
                if data.get("success") and data.get("data", {}).get("tokens", {}).get("access_token"):
                    self.access_token = data["data"]["tokens"]["access_token"]
                    self.user_data = data["data"]["user"]
                    if data["data"].get("organization"):
                        self.organization_id = data["data"]["organization"]["id"]
                    self.log_result("POST /api/v1/auth/login", True, "Login successful")
                else:
                    self.log_result("POST /api/v1/auth/login", False, "Invalid response format")
            except:
                self.log_result("POST /api/v1/auth/login", False, "Invalid JSON response")
        else:
            self.log_result("POST /api/v1/auth/login", False, f"Status: {status}")
        
        # Test login with invalid credentials
        invalid_login = {
            "email": TEST_USER_EMAIL,
            "password": "WrongPassword"
        }
        success, response, status = self.make_request("POST", "/api/v1/auth/login", invalid_login)
        expected_fail = status == 401  # Should fail with unauthorized
        self.log_result("POST /api/v1/auth/login (invalid)", expected_fail, f"Status: {status}")
        
        # Test logout (if endpoint exists)
        success, response, status = self.make_request("POST", "/api/v1/auth/logout")
        self.log_result("POST /api/v1/auth/logout", True, f"Status: {status} (optional endpoint)")
    
    def test_user_endpoints(self):
        """Test user management endpoints"""
        print("\n👤 Testing User Endpoints")
        print("-" * 50)
        
        if not self.access_token:
            self.log_result("User endpoints", False, "No access token available")
            return
        
        # Test get current user
        success, response, status = self.make_request("GET", "/api/v1/users/me")
        self.log_result("GET /api/v1/users/me", success, f"Status: {status}")
        
        # Test get user profile
        success, response, status = self.make_request("GET", "/api/v1/users/profile")
        self.log_result("GET /api/v1/users/profile", success, f"Status: {status}")
        
        # Test update user profile
        update_data = {
            "first_name": "Updated",
            "last_name": "Name"
        }
        success, response, status = self.make_request("PUT", "/api/v1/users/profile", update_data)
        self.log_result("PUT /api/v1/users/profile", True, f"Status: {status} (optional endpoint)")
    
    def test_organization_endpoints(self):
        """Test organization management endpoints"""
        print("\n🏢 Testing Organization Endpoints")
        print("-" * 50)
        
        if not self.access_token:
            self.log_result("Organization endpoints", False, "No access token available")
            return
        
        # Test get organizations
        success, response, status = self.make_request("GET", "/api/v1/organizations")
        self.log_result("GET /api/v1/organizations", success, f"Status: {status}")
        
        if self.organization_id:
            # Test get organization details
            success, response, status = self.make_request("GET", f"/api/v1/organizations/{self.organization_id}")
            self.log_result(f"GET /api/v1/organizations/{self.organization_id}", success, f"Status: {status}")
            
            # Test get organization members
            success, response, status = self.make_request("GET", f"/api/v1/organizations/{self.organization_id}/members")
            self.log_result(f"GET /api/v1/organizations/{self.organization_id}/members", success, f"Status: {status}")
            
            # Test invite user to organization
            invite_data = {
                "email": f"invite_{int(time.time())}@example.com",
                "role": "member"
            }
            success, response, status = self.make_request("POST", f"/api/v1/organizations/{self.organization_id}/invite", invite_data)
            self.log_result(f"POST /api/v1/organizations/{self.organization_id}/invite", True, f"Status: {status} (optional endpoint)")
    
    def test_project_endpoints(self):
        """Test project management endpoints"""
        print("\n📋 Testing Project Endpoints")
        print("-" * 50)
        
        if not self.access_token:
            self.log_result("Project endpoints", False, "No access token available")
            return
        
        # Test get projects
        success, response, status = self.make_request("GET", "/api/v1/projects")
        if success and response:
            try:
                data = response.json()
                if data.get("data") and len(data["data"]) > 0:
                    self.project_id = data["data"][0]["id"]
                self.log_result("GET /api/v1/projects", True, f"Status: {status}")
            except:
                self.log_result("GET /api/v1/projects", success, f"Status: {status}")
        else:
            self.log_result("GET /api/v1/projects", success, f"Status: {status}")
        
        # Test create project
        project_data = {
            "name": f"Test Project {int(time.time())}",
            "description": "Test project description",
            "status": "active"
        }
        success, response, status = self.make_request("POST", "/api/v1/projects", project_data)
        if success and response:
            try:
                data = response.json()
                if data.get("data", {}).get("id"):
                    self.project_id = data["data"]["id"]
            except:
                pass
        self.log_result("POST /api/v1/projects", success, f"Status: {status}")
        
        if self.project_id:
            # Test get specific project
            success, response, status = self.make_request("GET", f"/api/v1/projects/{self.project_id}")
            self.log_result(f"GET /api/v1/projects/{self.project_id}", success, f"Status: {status}")
            
            # Test update project
            update_data = {
                "name": f"Updated Project {int(time.time())}",
                "description": "Updated description"
            }
            success, response, status = self.make_request("PUT", f"/api/v1/projects/{self.project_id}", update_data)
            self.log_result(f"PUT /api/v1/projects/{self.project_id}", success, f"Status: {status}")
    
    def test_board_and_card_endpoints(self):
        """Test board and card management endpoints"""
        print("\n📋 Testing Board & Card Endpoints")
        print("-" * 50)
        
        if not self.access_token:
            self.log_result("Board/Card endpoints", False, "No access token available")
            return
        
        # Test get boards
        success, response, status = self.make_request("GET", "/api/v1/boards")
        if success and response:
            try:
                data = response.json()
                if data.get("data") and len(data["data"]) > 0:
                    self.board_id = data["data"][0]["id"]
            except:
                pass
        self.log_result("GET /api/v1/boards", success, f"Status: {status}")
        
        # Test get cards
        success, response, status = self.make_request("GET", "/api/v1/cards")
        if success and response:
            try:
                data = response.json()
                if data.get("data") and len(data["data"]) > 0:
                    self.card_id = data["data"][0]["id"]
            except:
                pass
        self.log_result("GET /api/v1/cards", success, f"Status: {status}")
        
        if self.project_id:
            # Test create board
            board_data = {
                "name": f"Test Board {int(time.time())}",
                "project_id": self.project_id
            }
            success, response, status = self.make_request("POST", "/api/v1/boards", board_data)
            self.log_result("POST /api/v1/boards", success, f"Status: {status}")
            
            # Test create card
            card_data = {
                "title": f"Test Card {int(time.time())}",
                "description": "Test card description",
                "project_id": self.project_id,
                "status": "todo"
            }
            success, response, status = self.make_request("POST", "/api/v1/cards", card_data)
            self.log_result("POST /api/v1/cards", success, f"Status: {status}")
    
    def test_dashboard_endpoints(self):
        """Test dashboard and analytics endpoints"""
        print("\n📊 Testing Dashboard Endpoints")
        print("-" * 50)
        
        if not self.access_token:
            self.log_result("Dashboard endpoints", False, "No access token available")
            return
        
        # Test dashboard stats
        success, response, status = self.make_request("GET", "/api/v1/dashboard/stats")
        self.log_result("GET /api/v1/dashboard/stats", success, f"Status: {status}")
        
        # Test dashboard analytics
        success, response, status = self.make_request("GET", "/api/v1/dashboard/analytics")
        self.log_result("GET /api/v1/dashboard/analytics", True, f"Status: {status} (optional endpoint)")
    
    def print_summary(self):
        """Print test summary"""
        print("\n" + "=" * 60)
        print("🎯 COMPREHENSIVE API TEST SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests
        
        print(f"📊 Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"📈 Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print(f"\n❌ Failed Tests:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"   - {result['test']}: {result['details']}")
        
        print("\n🎉 Testing Complete!")
        return passed_tests, failed_tests
    
    def run_all_tests(self):
        """Run comprehensive test suite"""
        print("🚀 Starting Comprehensive API Testing")
        print("=" * 60)
        
        # Run all test categories
        self.test_health_endpoints()
        self.test_registration()
        self.test_authentication()
        self.test_user_endpoints()
        self.test_organization_endpoints()
        self.test_project_endpoints()
        self.test_board_and_card_endpoints()
        self.test_dashboard_endpoints()
        
        # Print summary
        return self.print_summary()

if __name__ == "__main__":
    tester = ComprehensiveAPITester()
    passed, failed = tester.run_all_tests()
    
    # Exit with appropriate code
    exit(0 if failed == 0 else 1)
