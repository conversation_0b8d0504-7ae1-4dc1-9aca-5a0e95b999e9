{"timestamp": "2025-08-02T12:03:53.485612", "phases": {"Phase 1": {"tests": [{"name": "PostgreSQL Connection", "status": "PASS", "details": "Connected successfully: PostgreSQL 16.8, compiled by Visual C++ build 1942..."}, {"name": "Database Connection", "status": "FAIL", "details": "relation \"columns\" does not exist"}], "score": 50.0, "max_score": 100, "critical_issues": ["Database connection failed: relation \"columns\" does not exist"], "live_data_verified": ["PostgreSQL 16.8, connected"], "database_stats": {"postgresql_version": "PostgreSQL 16.8, compiled by Visual C++ build 1942, 64-bit", "users_count": 2, "organizations_count": 2, "organization_members_count": 2, "boards_count": 2}}, "Phase 2": {"tests": [{"name": "Health Endpoint", "status": "PASS", "details": "Health check passed: {'success': True, 'data': {'status': 'healthy', 'version': '1.0.0', 'environment': 'development', 'mode': 'enhanced', 'email_configured': True}, 'timestamp': **********.927556}"}, {"name": "User Registration", "status": "FAIL", "details": "Registration failed with status 404: {\"detail\":\"Not Found\"}"}], "score": 50.0, "max_score": 100, "critical_issues": ["User registration failed: 404"], "live_data_verified": ["API server healthy and responding"], "api_responses": {}}, "Phase 3": {"tests": [{"name": "Frontend Accessibility", "status": "PASS", "details": "Frontend accessible with React: True, Responsive: True"}], "score": 100.0, "max_score": 100, "critical_issues": [], "live_data_verified": ["Frontend React application accessible"], "frontend_stats": {"content_length": 1711, "has_react": true, "has_viewport": true}}, "Phase 4": {"tests": [{"name": "Domain Validation Concept", "status": "PASS", "details": "Valid domains: ['agnoshin.com', 'agno.com'], Invalid domains: ['gmail.com', 'yahoo.com']"}], "score": 100.0, "max_score": 100, "critical_issues": [], "live_data_verified": ["Domain validation concept verified"], "invitation_tests": {}}, "Phase 5": {"tests": [{"name": "Organization Members Table", "status": "PASS", "details": "Table exists with 2 members"}], "score": 100.0, "max_score": 100, "critical_issues": [], "live_data_verified": ["RBAC structure verified: 2 organization members"], "rbac_stats": {}}, "Phase 6": {"tests": [{"name": "Organizations Table", "status": "PASS", "details": "Found 2 organizations"}], "score": 100.0, "max_score": 100, "critical_issues": [], "live_data_verified": ["Multi-tenant structure: 2 organizations"], "multitenant_stats": {}}, "Phase 7": {"tests": [{"name": "API Response Time", "status": "FAIL", "details": "Slow response: 2030.0ms"}, {"name": "Database Query Performance", "status": "PASS", "details": "Query time: 4.0ms for 2 users"}], "score": 50.0, "max_score": 100, "critical_issues": ["Slow API response: 2030.0ms"], "live_data_verified": ["Database performance verified: 4.0ms"], "performance_stats": {"api_response_time_ms": 2030.0304889678955, "db_query_time_ms": 4.028081893920898, "user_count": 2}}, "Phase 8": {"tests": [{"name": "Data Persistence", "status": "PASS", "details": "Data persisted successfully: <EMAIL>"}], "score": 100.0, "max_score": 100, "critical_issues": [], "live_data_verified": ["Data persistence verified: <EMAIL>"], "realtime_stats": {}}}, "overall_score": 81.25, "critical_issues": [], "live_data_validation": {}, "test_data_created": {}}