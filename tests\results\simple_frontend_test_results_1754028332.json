{"timestamp": 1754028332.4471576, "total_time": 16.40415358543396, "summary": {"total": 11, "passed": 11, "failed": 0, "warned": 0, "skipped": 0}, "results": [{"test": "Frontend Server Access", "status": "PASS", "details": "Status: 200", "timestamp": 1754028318.0831544}, {"test": "React App Detection", "status": "PASS", "details": "React app detected", "timestamp": 1754028318.0831544}, {"test": "HTML Structure", "status": "PASS", "details": "Valid HTML structure", "timestamp": 1754028318.0831544}, {"test": "Static Resource: /static/css/", "status": "PASS", "details": "Status: 200", "timestamp": 1754028320.1581807}, {"test": "Static Resource: /static/js/", "status": "PASS", "details": "Status: 200", "timestamp": 1754028322.2184222}, {"test": "Static Resource: /favicon.ico", "status": "PASS", "details": "Status: 200", "timestamp": 1754028324.2687767}, {"test": "Static Resource: /manifest.json", "status": "PASS", "details": "Status: 200", "timestamp": 1754028326.3349514}, {"test": "Backend API Reachability", "status": "PASS", "details": "Backend API is reachable", "timestamp": 1754028328.3696208}, {"test": "CORS Configuration", "status": "PASS", "details": "CORS headers present", "timestamp": 1754028330.4030006}, {"test": "Page Load Time", "status": "PASS", "details": "Loaded in 2.03s", "timestamp": 1754028332.4363525}, {"test": "Response Size", "status": "PASS", "details": "Size: 1711 bytes", "timestamp": 1754028332.4363525}]}