#!/usr/bin/env python3
"""
Test script to verify the organization API is returning correct data
"""

import requests
import json

def test_login_and_get_user():
    """Test login and get current user data"""
    base_url = "http://localhost:3001"
    
    # Test login
    login_data = {
        "email": "<EMAIL>",
        "password": "Owner123!"
    }
    
    print("🔐 Testing login...")
    login_response = requests.post(f"{base_url}/api/v1/auth/login", json=login_data)
    
    if login_response.status_code != 200:
        print(f"❌ Login failed: {login_response.status_code}")
        print(login_response.text)
        return
    
    login_result = login_response.json()
    print("✅ Login successful!")
    print(f"Login response: {json.dumps(login_result, indent=2)}")
    
    # Extract token
    if 'data' in login_result and 'tokens' in login_result['data']:
        token = login_result['data']['tokens']['access_token']
        print(f"🎫 Token: {token[:20]}...")
    else:
        print("❌ No token found in login response")
        return
    
    # Test get current user
    print("\n👤 Testing get current user...")
    headers = {"Authorization": f"Bearer {token}"}
    user_response = requests.get(f"{base_url}/api/v1/users/me", headers=headers)
    
    if user_response.status_code != 200:
        print(f"❌ Get user failed: {user_response.status_code}")
        print(user_response.text)
        return
    
    user_result = user_response.json()
    print("✅ Get user successful!")
    print(f"User response: {json.dumps(user_result, indent=2)}")
    
    # Check organization data
    if 'data' in user_result and 'organizations' in user_result['data']:
        orgs = user_result['data']['organizations']
        print(f"\n🏢 Found {len(orgs)} organizations:")
        for i, org in enumerate(orgs):
            print(f"  {i+1}. {org}")
            if 'organization' in org:
                org_data = org['organization']
                print(f"     Name: {org_data.get('name', 'N/A')}")
                print(f"     Domain: {org_data.get('domain', 'N/A')}")
                print(f"     Role: {org.get('role', 'N/A')}")
    else:
        print("❌ No organizations found in response")

if __name__ == "__main__":
    test_login_and_get_user()
