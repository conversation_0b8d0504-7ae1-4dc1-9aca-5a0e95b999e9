"""
Tests for role-based task assignment and AI checklist features
"""
import pytest
from httpx import AsyncClient
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.models.user import User
from app.models.organization import Organization, OrganizationMember
from app.models.project import Project
from app.models.board import Board
from app.models.column import Column
from app.models.card import Card, ChecklistItem
from app.services.role_permissions import role_permissions
from app.services.ai_checklist_service import ai_checklist_service


class TestRoleBasedPermissions:
    """Test role-based permission system"""
    
    @pytest.mark.asyncio
    async def test_role_permissions_viewer(self):
        """Test viewer role permissions"""
        permissions = role_permissions.get_role_permissions('viewer')
        
        assert permissions['can_assign_tasks_to_self'] is True
        assert permissions['can_assign_tasks_to_others'] is False
        assert permissions['can_create_tasks'] is False
        assert permissions['assignment_scope'] == 'self'
    
    @pytest.mark.asyncio
    async def test_role_permissions_member(self):
        """Test member role permissions"""
        permissions = role_permissions.get_role_permissions('member')
        
        assert permissions['can_assign_tasks_to_self'] is True
        assert permissions['can_assign_tasks_to_others'] is False
        assert permissions['can_create_tasks'] is True
        assert permissions['assignment_scope'] == 'self'
    
    @pytest.mark.asyncio
    async def test_role_permissions_admin(self):
        """Test admin role permissions"""
        permissions = role_permissions.get_role_permissions('admin')
        
        assert permissions['can_assign_tasks_to_others'] is True
        assert permissions['can_create_tasks'] is True
        assert permissions['can_edit_other_tasks'] is True
        assert permissions['assignment_scope'] == 'project'
    
    @pytest.mark.asyncio
    async def test_role_permissions_owner(self):
        """Test owner role permissions"""
        permissions = role_permissions.get_role_permissions('owner')
        
        assert permissions['can_assign_tasks_to_others'] is True
        assert permissions['can_delete_tasks'] is True
        assert permissions['assignment_scope'] == 'organization'
    
    @pytest.mark.asyncio
    async def test_task_assignment_validation(self):
        """Test task assignment validation logic"""
        # Self-assignment should always work
        assert role_permissions.can_assign_task_to_user('viewer', 'user1', 'user1') is True
        assert role_permissions.can_assign_task_to_user('member', 'user1', 'user1') is True
        
        # Viewer/Member cannot assign to others
        assert role_permissions.can_assign_task_to_user('viewer', 'user1', 'user2') is False
        assert role_permissions.can_assign_task_to_user('member', 'user1', 'user2') is False
        
        # Admin/Owner can assign to others
        assert role_permissions.can_assign_task_to_user('admin', 'user1', 'user2') is True
        assert role_permissions.can_assign_task_to_user('owner', 'user1', 'user2') is True


class TestAIChecklistService:
    """Test AI checklist generation service"""
    
    @pytest.mark.asyncio
    async def test_task_type_detection(self):
        """Test task type detection"""
        # Development task
        task_type = ai_checklist_service.detect_task_type(
            "Implement user authentication API",
            "Create REST API endpoints for login and registration"
        )
        assert task_type == 'development'
        
        # Design task
        task_type = ai_checklist_service.detect_task_type(
            "Design user interface mockups",
            "Create wireframes and visual designs for the dashboard"
        )
        assert task_type == 'design'
        
        # Testing task
        task_type = ai_checklist_service.detect_task_type(
            "QA testing for payment module",
            "Test all payment scenarios and edge cases"
        )
        assert task_type == 'testing'
    
    @pytest.mark.asyncio
    async def test_ai_checklist_generation(self):
        """Test AI checklist generation"""
        result = await ai_checklist_service.generate_ai_checklist(
            title="Implement user authentication",
            description="Create login and registration system",
            priority="high",
            project_type="api"
        )
        
        assert result['success'] is True
        assert len(result['items']) > 0
        assert len(result['items']) <= 8
        
        # Check item structure
        first_item = result['items'][0]
        assert 'text' in first_item
        assert 'ai_generated' in first_item
        assert 'confidence' in first_item
        assert first_item['ai_generated'] is True
        assert 0 <= first_item['confidence'] <= 100
    
    @pytest.mark.asyncio
    async def test_confidence_calculation(self):
        """Test confidence score calculation"""
        confidence = ai_checklist_service.calculate_confidence(
            "Review API requirements",
            "Implement user authentication API",
            "Create REST API endpoints"
        )
        
        assert 0 <= confidence <= 100
        assert confidence > 70  # Should have decent confidence due to keyword matches
    
    @pytest.mark.asyncio
    async def test_suggested_items(self):
        """Test suggested items generation"""
        suggestions = ai_checklist_service.get_suggested_items('development')
        
        assert len(suggestions) > 0
        assert all(isinstance(item, str) for item in suggestions)


class TestChecklistAPI:
    """Test checklist API endpoints"""
    
    @pytest.fixture
    async def setup_test_data(self, db_session: AsyncSession):
        """Set up test data for checklist tests"""
        # Create test organization, project, board, column, and card
        org = Organization(name="Test Org")
        db_session.add(org)
        await db_session.flush()
        
        user = User(email="<EMAIL>", first_name="Test", last_name="User")
        db_session.add(user)
        await db_session.flush()
        
        org_member = OrganizationMember(
            organization_id=org.id,
            user_id=user.id,
            role="admin",
            status="active"
        )
        db_session.add(org_member)
        
        project = Project(name="Test Project", organization_id=org.id)
        db_session.add(project)
        await db_session.flush()
        
        board = Board(name="Test Board", project_id=project.id)
        db_session.add(board)
        await db_session.flush()
        
        column = Column(name="To Do", board_id=board.id, position=0)
        db_session.add(column)
        await db_session.flush()
        
        card = Card(
            title="Test Card",
            column_id=column.id,
            position=0,
            created_by=user.id
        )
        db_session.add(card)
        await db_session.commit()
        
        return {
            'org': org,
            'user': user,
            'project': project,
            'board': board,
            'column': column,
            'card': card
        }
    
    @pytest.mark.asyncio
    async def test_generate_ai_checklist_endpoint(
        self, 
        client: AsyncClient, 
        setup_test_data,
        auth_headers
    ):
        """Test AI checklist generation endpoint"""
        test_data = await setup_test_data
        card_id = str(test_data['card'].id)
        
        response = await client.post(
            f"/api/v1/checklist/cards/{card_id}/checklist/ai-generate",
            json={
                "title": "Implement user authentication",
                "description": "Create login system",
                "priority": "high",
                "project_type": "api"
            },
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data['success'] is True
        assert len(data['items']) > 0
    
    @pytest.mark.asyncio
    async def test_create_checklist_items_endpoint(
        self,
        client: AsyncClient,
        setup_test_data,
        auth_headers
    ):
        """Test creating checklist items"""
        test_data = await setup_test_data
        card_id = str(test_data['card'].id)
        
        response = await client.post(
            f"/api/v1/checklist/cards/{card_id}/checklist",
            json={
                "items": [
                    {
                        "text": "Review requirements",
                        "position": 0,
                        "ai_generated": True,
                        "confidence": 85
                    },
                    {
                        "text": "Set up environment",
                        "position": 1,
                        "ai_generated": False
                    }
                ]
            },
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 2
        assert data[0]['text'] == "Review requirements"
        assert data[0]['ai_generated'] is True
    
    @pytest.mark.asyncio
    async def test_get_checklist_items_endpoint(
        self,
        client: AsyncClient,
        setup_test_data,
        auth_headers,
        db_session: AsyncSession
    ):
        """Test getting checklist items"""
        test_data = await setup_test_data
        card_id = test_data['card'].id
        
        # Create test checklist item
        item = ChecklistItem(
            card_id=card_id,
            text="Test item",
            position=0,
            ai_generated=True,
            confidence=90,
            created_by=test_data['user'].id
        )
        db_session.add(item)
        await db_session.commit()
        
        response = await client.get(
            f"/api/v1/checklist/cards/{card_id}/checklist",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert len(data) == 1
        assert data[0]['text'] == "Test item"
    
    @pytest.mark.asyncio
    async def test_update_checklist_item_endpoint(
        self,
        client: AsyncClient,
        setup_test_data,
        auth_headers,
        db_session: AsyncSession
    ):
        """Test updating checklist item"""
        test_data = await setup_test_data
        
        # Create test checklist item
        item = ChecklistItem(
            card_id=test_data['card'].id,
            text="Original text",
            position=0,
            completed=False,
            created_by=test_data['user'].id
        )
        db_session.add(item)
        await db_session.commit()
        await db_session.refresh(item)
        
        response = await client.put(
            f"/api/v1/checklist/checklist/{item.id}",
            json={
                "text": "Updated text",
                "completed": True
            },
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data['text'] == "Updated text"
        assert data['completed'] is True
    
    @pytest.mark.asyncio
    async def test_delete_checklist_item_endpoint(
        self,
        client: AsyncClient,
        setup_test_data,
        auth_headers,
        db_session: AsyncSession
    ):
        """Test deleting checklist item"""
        test_data = await setup_test_data
        
        # Create test checklist item
        item = ChecklistItem(
            card_id=test_data['card'].id,
            text="To be deleted",
            position=0,
            created_by=test_data['user'].id
        )
        db_session.add(item)
        await db_session.commit()
        await db_session.refresh(item)
        
        response = await client.delete(
            f"/api/v1/checklist/checklist/{item.id}",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data['success'] is True
        
        # Verify item is deleted
        result = await db_session.execute(
            select(ChecklistItem).where(ChecklistItem.id == item.id)
        )
        assert result.scalar_one_or_none() is None


class TestRoleBasedAssignmentAPI:
    """Test role-based assignment API endpoints"""
    
    @pytest.mark.asyncio
    async def test_validate_assignment_endpoint_success(
        self,
        client: AsyncClient,
        auth_headers
    ):
        """Test successful assignment validation"""
        response = await client.post(
            "/api/v1/checklist/assignments/validate?organization_id=test-org-id",
            json={"assigned_to": ["same-user-id"]},  # Self-assignment
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert data['valid'] is True
    
    @pytest.mark.asyncio
    async def test_get_role_permissions_endpoint(
        self,
        client: AsyncClient,
        auth_headers
    ):
        """Test getting role permissions"""
        response = await client.get(
            "/api/v1/checklist/permissions/role/member",
            headers=auth_headers
        )
        
        assert response.status_code == 200
        data = response.json()
        assert 'can_assign_tasks_to_self' in data
        assert 'assignment_scope' in data
        assert 'restriction_message' in data
