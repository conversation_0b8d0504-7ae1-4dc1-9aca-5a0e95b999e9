@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    /* Core System Colors */
    --color-background: #FAFBFC; /* gray-50 */
    --color-foreground: #1E293B; /* slate-800 */
    --color-border: #E2E8F0; /* slate-200 */
    --color-input: #FFFFFF; /* white */
    --color-ring: #2563EB; /* blue-600 */
    
    /* Card & Surface Colors */
    --color-card: #FFFFFF; /* white */
    --color-card-foreground: #1E293B; /* slate-800 */
    --color-popover: #FFFFFF; /* white */
    --color-popover-foreground: #1E293B; /* slate-800 */
    
    /* Muted Colors */
    --color-muted: #F1F5F9; /* slate-100 */
    --color-muted-foreground: #64748B; /* slate-500 */
    
    /* Primary Colors */
    --color-primary: #2563EB; /* blue-600 */
    --color-primary-foreground: #FFFFFF; /* white */
    
    /* Secondary Colors */
    --color-secondary: #64748B; /* slate-500 */
    --color-secondary-foreground: #FFFFFF; /* white */
    
    /* Destructive Colors */
    --color-destructive: #EF4444; /* red-500 */
    --color-destructive-foreground: #FFFFFF; /* white */
    
    /* Accent Colors */
    --color-accent: #F59E0B; /* amber-500 */
    --color-accent-foreground: #1E293B; /* slate-800 */
    
    /* Success Colors */
    --color-success: #10B981; /* emerald-500 */
    --color-success-foreground: #FFFFFF; /* white */
    
    /* Warning Colors */
    --color-warning: #F59E0B; /* amber-500 */
    --color-warning-foreground: #1E293B; /* slate-800 */
    
    /* Error Colors */
    --color-error: #EF4444; /* red-500 */
    --color-error-foreground: #FFFFFF; /* white */
    
    /* Additional Theme Colors */
    --color-surface: #FFFFFF; /* white */
    --color-text-primary: #1E293B; /* slate-800 */
    --color-text-secondary: #64748B; /* slate-500 */
  }

  * {
    @apply border-slate-200;
  }
  
  body {
    @apply bg-gray-50 text-slate-800;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  }
  
  h1, h2, h3, h4, h5, h6 {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
  }
  
  .font-mono {
    font-family: 'JetBrains Mono', 'Fira Code', 'Consolas', monospace;
  }
}

@layer components {
  /* Enterprise Shadow System */
  .shadow-ambient {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  }
  
  .shadow-directional {
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.06);
  }
  
  .shadow-enterprise {
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1), 0 1px 2px rgba(0, 0, 0, 0.06);
  }
  
  .shadow-elevated {
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
  }
  
  .shadow-focused {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15), 0 4px 10px rgba(0, 0, 0, 0.1);
  }
  
  /* Micro-interaction Classes */
  .hover-lift {
    @apply transition-transform duration-150 ease-out;
  }
  
  .hover-lift:hover {
    transform: scale(1.02);
  }
  
  /* Role-based Visual Cues */
  .role-indicator {
    @apply border-l-4;
  }
.role-admin  {
    @apply border-l-primary;
  }
.role-manager  {
    @apply border-l-accent;
  }
.role-member  {
    @apply border-l-success;
  }
  
  /* Loading States */
  .skeleton {
    @apply animate-pulse bg-slate-100;
  }
  
  .shimmer {
    @apply relative overflow-hidden;
  }
  
  .shimmer::after {
    @apply absolute inset-0 -translate-x-full animate-shimmer bg-gradient-to-r from-transparent via-white/60 to-transparent;
    content: '';
    animation: shimmer 1.5s infinite;
  }
  
  @keyframes shimmer {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }
}

@layer utilities {
  /* Custom Transitions */
  .transition-micro {
    transition: all 150ms cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .transition-smooth {
    transition: all 200ms cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  /* Touch-friendly Interactions */
  .touch-target {
    @apply min-h-[44px] min-w-[44px];
  }
  
  /* Progressive Disclosure */
  .expandable {
    @apply transition-all duration-200 ease-in-out;
  }
}