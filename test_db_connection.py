#!/usr/bin/env python3
"""
Test PostgreSQL database connection with correct credentials
"""

import asyncio
import asyncpg

async def test_database_connection():
    """Test database connection and show database status"""
    try:
        print("🔍 Testing PostgreSQL connection...")
        conn = await asyncpg.connect('postgresql://postgres:admin@localhost:5432/agno_worksphere')
        
        # Get PostgreSQL version
        version = await conn.fetchval('SELECT version()')
        print(f"✅ PostgreSQL connected: {version[:50]}...")
        
        # Check if database exists and show tables
        tables = await conn.fetch("""
            SELECT table_name FROM information_schema.tables 
            WHERE table_schema = 'public'
        """)
        
        print(f"✅ Found {len(tables)} tables in database:")
        for table in tables:
            table_name = table['table_name']
            count = await conn.fetchval(f'SELECT COUNT(*) FROM {table_name}')
            print(f"  - {table_name}: {count} records")
        
        # Test basic operations
        print("\n🔧 Testing basic database operations...")
        
        # Test UUID generation
        test_uuid = await conn.fetchval("SELECT gen_random_uuid()")
        print(f"✅ UUID generation working: {str(test_uuid)[:8]}...")
        
        # Test foreign key constraints
        fk_constraints = await conn.fetch("""
            SELECT tc.table_name, tc.constraint_name
            FROM information_schema.table_constraints AS tc 
            WHERE tc.constraint_type = 'FOREIGN KEY'
        """)
        print(f"✅ Found {len(fk_constraints)} foreign key constraints")
        
        await conn.close()
        print("✅ Database connection test completed successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        return False

if __name__ == "__main__":
    asyncio.run(test_database_connection())
