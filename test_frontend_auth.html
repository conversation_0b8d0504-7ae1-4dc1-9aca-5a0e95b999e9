<!DOCTYPE html>
<html>
<head>
    <title>Test Frontend Auth</title>
</head>
<body>
    <h1>Test Frontend Authentication</h1>
    <div id="output"></div>
    
    <script>
        async function testAuth() {
            const output = document.getElementById('output');
            
            // Clear localStorage
            localStorage.clear();
            output.innerHTML += '<p>✅ Cleared localStorage</p>';
            
            // Test login
            try {
                const loginResponse = await fetch('http://localhost:3001/api/v1/auth/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        email: '<EMAIL>',
                        password: 'Owner123!'
                    })
                });
                
                const loginResult = await loginResponse.json();
                output.innerHTML += '<p>✅ Login successful</p>';
                output.innerHTML += '<pre>' + JSON.stringify(loginResult, null, 2) + '</pre>';
                
                // Store token
                if (loginResult.data && loginResult.data.tokens) {
                    localStorage.setItem('accessToken', loginResult.data.tokens.access_token);
                    output.innerHTML += '<p>✅ Token stored</p>';
                }
                
                // Test get current user
                const userResponse = await fetch('http://localhost:3001/api/v1/users/me', {
                    headers: {
                        'Authorization': `Bearer ${loginResult.data.tokens.access_token}`
                    }
                });
                
                const userResult = await userResponse.json();
                output.innerHTML += '<p>✅ Get user successful</p>';
                output.innerHTML += '<pre>' + JSON.stringify(userResult, null, 2) + '</pre>';
                
                // Check organization name
                if (userResult.data && userResult.data.organizations && userResult.data.organizations.length > 0) {
                    const org = userResult.data.organizations[0];
                    const orgName = org.organization ? org.organization.name : org.name;
                    output.innerHTML += `<p><strong>Organization Name: ${orgName}</strong></p>`;
                }
                
            } catch (error) {
                output.innerHTML += '<p>❌ Error: ' + error.message + '</p>';
            }
        }
        
        // Run test on page load
        testAuth();
    </script>
</body>
</html>
