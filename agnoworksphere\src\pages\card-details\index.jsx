// ... imports
import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams, useLocation } from 'react-router-dom';
import RoleBasedHeader from '../../components/ui/RoleBasedHeader';
import Icon from '../../components/AppIcon';
import CardHeader from './components/CardHeader';
import CardDescription from './components/CardDescription';
import MemberAssignment from './components/MemberAssignment';
import DueDatePicker from './components/DueDatePicker';
import LabelManager from './components/LabelManager';
import ChecklistManager from './components/ChecklistManager';
import ActivityTimeline from './components/ActivityTimeline';
import authService from '../../utils/authService';

const CardDetails = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const cardId = searchParams.get('id');

  // Authentication state
  const [currentUser, setCurrentUser] = useState(null);
  const [userRole, setUserRole] = useState('member');
  const [currentOrganization, setCurrentOrganization] = useState(null);

  const canEdit = ['member', 'admin', 'owner'].includes(userRole.toLowerCase());
  const canDelete = ['admin', 'owner'].includes(userRole.toLowerCase());
  const canComment = ['member', 'admin', 'owner'].includes(userRole.toLowerCase());

  const [cardData, setCardData] = useState(null);
  const [isLoading, setIsLoading] = useState(true);

  // Track pending changes
  const [pendingChanges, setPendingChanges] = useState({});
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);
  const [isSaving, setIsSaving] = useState(false);



  // Warn user about unsaved changes when leaving
  useEffect(() => {
    const handleBeforeUnload = (e) => {
      if (hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = '';
      }
    };

    window.addEventListener('beforeunload', handleBeforeUnload);
    return () => window.removeEventListener('beforeunload', handleBeforeUnload);
  }, [hasUnsavedChanges]);

  // Load card data from location state or API
  useEffect(() => {
    const loadCardData = async () => {
      setIsLoading(true);

      // First try to get card data from location state (when navigated from kanban board)
      if (location.state?.card) {
        console.log('Loading card from location state:', location.state.card);

        // Normalize the card data - convert checklist_items to checklist for frontend compatibility
        const normalizedCardData = {
          ...location.state.card,
          checklist: location.state.card.checklist_items || location.state.card.checklist || []
        };

        // Remove checklist_items to avoid confusion
        if (normalizedCardData.checklist_items) {
          delete normalizedCardData.checklist_items;
        }

        setCardData(normalizedCardData);
        setIsLoading(false);
        return;
      }

      // If no state data, try to load from API
      if (cardId) {
        try {
          const apiService = (await import('../../utils/realApiService')).default;
          const result = await apiService.cards.getById(cardId);
          console.log('Loading card from API:', result);
          if (result.data) {
            // Normalize the card data - convert checklist_items to checklist for frontend compatibility
            const normalizedCardData = {
              ...result.data,
              checklist: result.data.checklist_items || result.data.checklist || []
            };

            // Remove checklist_items to avoid confusion
            if (normalizedCardData.checklist_items) {
              delete normalizedCardData.checklist_items;
            }

            setCardData(normalizedCardData);
            setIsLoading(false);
            return;
          }
        } catch (error) {
          console.error('Error loading card from API:', error);
        }
      }

      // Fallback: use mock data if no card found
      console.log('No card data found, using fallback data');
      setCardData({
        id: cardId || '1',
        title: 'Card Not Found',
        description: 'This card could not be loaded. Please return to the board and try again.',
        columnTitle: 'Unknown',
        assignedMembers: [],
        dueDate: null,
        labels: [],
        checklist: [],
        completed: false,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
      setIsLoading(false);
    };

    loadCardData();
  }, [cardId, location.state]);

  // Helper function to track changes
  const trackChange = (field, value) => {
    const updatedData = { [field]: value, updatedAt: new Date().toISOString() };

    // Update local card data immediately for UI
    setCardData(prev => ({ ...prev, ...updatedData }));

    // Track pending changes
    setPendingChanges(prev => ({ ...prev, ...updatedData }));
    setHasUnsavedChanges(true);
  };

  // Helper function to check if a field has pending changes
  const hasFieldChanged = (field) => {
    return pendingChanges.hasOwnProperty(field);
  };

  const handleTitleChange = (newTitle) => {
    trackChange('title', newTitle);
  };

  const handleDescriptionChange = (newDescription) => {
    trackChange('description', newDescription);
  };

  // Helper function to update card via API
  const updateCardInAPI = async (updatedData) => {
    try {
      if (!cardData?.id) return;

      const apiService = (await import('../../utils/realApiService')).default;

      // Handle checklist separately if it's being updated
      if (updatedData.checklist) {
        await updateChecklistInAPI(updatedData.checklist);
        // Remove checklist from card update data since it's handled separately
        const { checklist, ...cardUpdateData } = updatedData;
        if (Object.keys(cardUpdateData).length > 0) {
          await apiService.cards.update(cardData.id, cardUpdateData);
        }
      } else {
        await apiService.cards.update(cardData.id, updatedData);
      }

      console.log('Card updated via API:', updatedData);
    } catch (error) {
      console.error('Error updating card via API:', error);
    }
  };

  // Helper function to check if a string is a valid UUID
  const isValidUUID = (str) => {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(str);
  };

  // Helper function to update checklist via dedicated API
  const updateChecklistInAPI = async (checklistItems) => {
    try {
      if (!cardData?.id || !checklistItems) return;

      const apiService = (await import('../../utils/realApiService')).default;

      // Get current checklist items from the card
      const currentItems = cardData.checklist || [];

      // Create a map of current items by ID for easy lookup
      // Only include items with real backend IDs (not temporary ones)
      const currentItemsMap = new Map();
      currentItems.forEach(item => {
        if (item.id) {
          const itemId = item.id.toString();
          const isTemporary = !itemId ||
                             itemId === '' ||
                             itemId.startsWith('temp-') ||
                             itemId.startsWith('ai-') ||
                             !isValidUUID(itemId);

          // Only add items with real backend IDs to the map
          if (!isTemporary) {
            currentItemsMap.set(item.id, item);
          }
        }
      });

      // Create a map of new items by ID
      const newItemsMap = new Map();
      const itemsToCreate = [];

      checklistItems.forEach((item, index) => {
        const itemId = item.id ? item.id.toString() : '';

        // Check if this is a temporary ID (frontend-generated) vs a real backend UUID
        const isTemporary = !itemId ||
                           itemId === '' ||
                           itemId.startsWith('temp-') ||
                           itemId.startsWith('ai-') ||
                           !isValidUUID(itemId); // Check if it's a valid UUID

        if (item.id && !isTemporary) {
          // Existing item with real backend ID (UUID)
          newItemsMap.set(item.id, {
            ...item,
            position: item.position !== undefined ? item.position : index
          });
        } else {
          // New item to create (temporary ID or no ID)
          itemsToCreate.push({
            text: item.text || item.title || '',
            completed: item.completed || false,
            position: item.position !== undefined ? item.position : index,
            ai_generated: item.aiGenerated || item.ai_generated || false,
            confidence: item.confidence || null,
            metadata: item.metadata || null
          });
        }
      });

      // Delete items that are no longer present
      // Only try to delete items that actually exist in the backend (have real IDs)
      for (const [itemId] of currentItemsMap) {
        if (!newItemsMap.has(itemId)) {
          try {
            // Double-check that this is a valid UUID before attempting deletion
            const itemIdStr = itemId.toString();
            if (isValidUUID(itemIdStr)) {
              await apiService.checklist.deleteItem(itemId);
              console.log('Deleted checklist item:', itemId);
            } else {
              console.log('Skipping deletion of non-UUID item:', itemId);
            }
          } catch (error) {
            console.warn('Failed to delete checklist item:', itemId, error);
          }
        }
      }

      // Update existing items that have changed
      for (const [itemId, newItem] of newItemsMap) {
        const currentItem = currentItemsMap.get(itemId);
        if (currentItem) {
          // Check if the item has changed
          const hasChanged =
            currentItem.text !== newItem.text ||
            currentItem.completed !== newItem.completed ||
            currentItem.position !== newItem.position;

          if (hasChanged) {
            try {
              // Double-check that this is a valid UUID before attempting update
              const itemIdStr = itemId.toString();
              if (isValidUUID(itemIdStr)) {
                await apiService.checklist.updateItem(itemId, {
                  text: newItem.text,
                  completed: newItem.completed,
                  position: newItem.position
                });
                console.log('Updated checklist item:', itemId);
              } else {
                console.log('Skipping update of non-UUID item (will be created instead):', itemId);
              }
            } catch (error) {
              console.warn('Failed to update checklist item:', itemId, error);
            }
          }
        }
      }

      // Create new items
      if (itemsToCreate.length > 0) {
        try {
          await apiService.checklist.createBulk(cardData.id, { items: itemsToCreate });
          console.log('Created new checklist items:', itemsToCreate.length);
        } catch (error) {
          console.error('Failed to create new checklist items:', error);
        }
      }

      console.log('Checklist updated via API successfully');
    } catch (error) {
      console.error('Error updating checklist via API:', error);
    }
  };

  const handleMembersChange = (newMembers) => {
    trackChange('assignedMembers', newMembers);
  };

  const handleDueDateChange = (newDueDate) => {
    trackChange('dueDate', newDueDate);
  };

  const handleLabelsChange = (newLabels) => {
    trackChange('labels', newLabels);
  };

  const handleChecklistChange = (newChecklist) => {
    trackChange('checklist', newChecklist);
  };

  // Save all pending changes
  const handleSaveChanges = async () => {
    if (!hasUnsavedChanges || !cardData?.id) return;

    setIsSaving(true);
    try {
      await updateCardInAPI(pendingChanges);

      // Reload card data to get the latest state from the backend
      if (cardData?.id) {
        try {
          const apiService = (await import('../../utils/realApiService')).default;
          const result = await apiService.cards.getById(cardData.id);
          if (result.data) {
            // Normalize the card data
            const normalizedCardData = {
              ...result.data,
              checklist: result.data.checklist_items || result.data.checklist || []
            };

            // Remove checklist_items to avoid confusion
            if (normalizedCardData.checklist_items) {
              delete normalizedCardData.checklist_items;
            }

            setCardData(normalizedCardData);
          }
        } catch (error) {
          console.error('Error reloading card data after save:', error);
        }
      }

      // Clear pending changes
      setPendingChanges({});
      setHasUnsavedChanges(false);

      console.log('All changes saved successfully');
    } catch (error) {
      console.error('Error saving changes:', error);
      // You could show a toast notification here
    } finally {
      setIsSaving(false);
    }
  };

  // Discard all pending changes
  const handleDiscardChanges = () => {
    if (!hasUnsavedChanges) return;

    // Revert cardData to original state (you might want to store original data)
    // For now, we'll just clear pending changes and reload
    setPendingChanges({});
    setHasUnsavedChanges(false);

    // Reload card data to revert changes
    window.location.reload();
  };

  const handleAddComment = (comment) => {
    console.log('New comment added:', comment);
  };

  // Keyboard shortcut for saving
  useEffect(() => {
    const handleKeyDown = (e) => {
      if ((e.ctrlKey || e.metaKey) && e.key === 's') {
        e.preventDefault();
        if (hasUnsavedChanges) {
          handleSaveChanges();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [hasUnsavedChanges, handleSaveChanges]);

  const handleClose = () => {
    if (hasUnsavedChanges) {
      const confirmLeave = window.confirm(
        'You have unsaved changes. Are you sure you want to leave without saving?'
      );
      if (!confirmLeave) return;
    }
    navigate('/kanban-board');
  };

  const handleDelete = () => {
    if (window.confirm('Are you sure you want to delete this card?')) {
      console.log('Card deleted:', cardData.id);
      navigate('/kanban-board');
    }
  };

  // Load user authentication data
  useEffect(() => {
    const loadUserData = async () => {
      try {
        const userResponse = await authService.getCurrentUser();
        const orgResponse = await authService.getCurrentOrganization();

        if (userResponse.data && userResponse.data.user) {
          setCurrentUser(userResponse.data.user);
          setUserRole(userResponse.data.user.role || 'member');
        }

        if (orgResponse.data && orgResponse.data.organization) {
          setCurrentOrganization(orgResponse.data.organization);
        }
      } catch (error) {
        console.error('Error loading user data:', error);
        // Set default values if auth fails
        setUserRole('member');
      }
    };

    loadUserData();
  }, []);

  useEffect(() => {
    document.body.style.overflow = 'hidden';
    return () => {
      document.body.style.overflow = 'unset';
    };
  }, []);

  // Show loading state
  if (isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <RoleBasedHeader
          userRole={userRole.toLowerCase()}
          currentUser={currentUser ? {
            name: `${currentUser.firstName} ${currentUser.lastName}`,
            email: currentUser.email,
            avatar: currentUser.avatar || '/assets/images/avatar.jpg',
            role: userRole
          } : {
            name: 'Loading...',
            email: '',
            avatar: '/assets/images/avatar.jpg',
            role: userRole
          }}
          currentOrganization={currentOrganization}
        />
        <div className="pt-16 flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
            <div className="text-lg font-medium text-text-primary mb-2">Loading card...</div>
            <div className="text-text-secondary">Please wait while we load the card details.</div>
          </div>
        </div>
      </div>
    );
  }

  // Show error state if card not found
  if (!cardData) {
    return (
      <div className="min-h-screen bg-background">
        <RoleBasedHeader
          userRole={userRole.toLowerCase()}
          currentUser={currentUser ? {
            name: `${currentUser.firstName} ${currentUser.lastName}`,
            email: currentUser.email,
            avatar: currentUser.avatar || '/assets/images/avatar.jpg',
            role: userRole
          } : {
            name: 'Loading...',
            email: '',
            avatar: '/assets/images/avatar.jpg',
            role: userRole
          }}
          currentOrganization={currentOrganization}
        />
        <div className="pt-16 flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="text-lg font-medium text-text-primary mb-2">Card not found</div>
            <div className="text-text-secondary mb-4">The requested card could not be found.</div>
            <button onClick={handleClose} className="text-primary hover:underline">
              Return to Board
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <RoleBasedHeader
        userRole={userRole.toLowerCase()}
        currentUser={currentUser ? {
          name: `${currentUser.firstName} ${currentUser.lastName}`,
          email: currentUser.email,
          avatar: currentUser.avatar || '/assets/images/avatar.jpg',
          role: userRole
        } : {
          name: 'Loading...',
          email: '',
          avatar: '/assets/images/avatar.jpg',
          role: userRole
        }}
        currentOrganization={currentOrganization}
      />

      {/* Modal Overlay */}
      <div className="fixed inset-0 bg-black/60 backdrop-blur-sm z-1000 pt-16">
        <div className="flex items-start justify-center min-h-full p-4 overflow-y-auto">
          {/* Modal Content */}
          <div className="w-full max-w-5xl bg-surface rounded-xl shadow-2xl my-8 max-h-screen overflow-hidden border border-border/20">
            {/* Card Header */}
            <CardHeader
              card={cardData}
              onTitleChange={handleTitleChange}
              onClose={handleClose}
              onDelete={handleDelete}
              canEdit={canEdit}
              canDelete={canDelete}
              hasChanged={hasFieldChanged('title')}
            />

            {/* Save Changes Bar */}
            {hasUnsavedChanges && (
              <div className="bg-warning/10 border-b border-warning/20 px-8 py-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <Icon name="AlertCircle" size={20} className="text-warning" />
                    <div>
                      <p className="text-sm font-medium text-warning">You have unsaved changes</p>
                      <p className="text-xs text-warning/80">Save your changes to avoid losing them</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <button
                      onClick={handleDiscardChanges}
                      disabled={isSaving}
                      className="px-4 py-2 text-sm font-medium text-text-secondary hover:text-text-primary border border-border rounded-md hover:bg-muted transition-colors disabled:opacity-50"
                    >
                      Discard
                    </button>
                    <button
                      onClick={handleSaveChanges}
                      disabled={isSaving}
                      className="px-6 py-2 text-sm font-medium text-white bg-primary hover:bg-primary/90 rounded-md transition-colors disabled:opacity-50 flex items-center space-x-2"
                    >
                      {isSaving ? (
                        <>
                          <Icon name="Loader2" size={16} className="animate-spin" />
                          <span>Saving...</span>
                        </>
                      ) : (
                        <>
                          <Icon name="Save" size={16} />
                          <span>Save Changes</span>
                        </>
                      )}
                    </button>
                  </div>
                </div>
              </div>
            )}

            {/* Main Content */}
            <div className="flex flex-col lg:flex-row overflow-y-auto max-h-[calc(100vh-8rem)]">
              {/* Left Column - Main Content */}
              <div className="flex-1 lg:w-3/5 p-8 space-y-8">
                <CardDescription
                  card={cardData}
                  onDescriptionChange={handleDescriptionChange}
                  canEdit={canEdit}
                  hasChanged={hasFieldChanged('description')}
                />
                <ChecklistManager
                  card={cardData}
                  onChecklistChange={handleChecklistChange}
                  canEdit={canEdit}
                  hasChanged={hasFieldChanged('checklist')}
                />
                <ActivityTimeline card={cardData} onAddComment={handleAddComment} canComment={canComment} />
              </div>

              {/* Right Column - Sidebar */}
              <div className="lg:w-2/5 p-8 bg-gradient-to-b from-muted/20 to-muted/40 border-l border-border/50 space-y-8">
                <MemberAssignment
                  card={cardData}
                  onMembersChange={handleMembersChange}
                  canEdit={canEdit}
                  hasChanged={hasFieldChanged('assignedMembers')}
                />
                <DueDatePicker
                  card={cardData}
                  onDueDateChange={handleDueDateChange}
                  canEdit={canEdit}
                  hasChanged={hasFieldChanged('dueDate')}
                />
                <LabelManager
                  card={cardData}
                  onLabelsChange={handleLabelsChange}
                  canEdit={canEdit}
                  hasChanged={hasFieldChanged('labels')}
                />

                {/* Card Information Section */}
                <div className="bg-surface/50 rounded-lg p-6 border border-border/30 space-y-4">
                  <h4 className="font-semibold text-text-primary flex items-center gap-2">
                    <Icon name="Info" size={16} className="text-primary" />
                    Card Information
                  </h4>
                  <div className="space-y-3 text-sm">
                    <div className="flex justify-between items-center py-2 border-b border-border/20">
                      <span className="text-text-secondary font-medium">Created:</span>
                      <span className="text-text-primary">{new Date(cardData.createdAt).toLocaleDateString()}</span>
                    </div>
                    <div className="flex justify-between items-center py-2 border-b border-border/20">
                      <span className="text-text-secondary font-medium">Last updated:</span>
                      <span className="text-text-primary">{new Date(cardData.updatedAt).toLocaleDateString()}</span>
                    </div>
                    <div className="flex justify-between items-center py-2">
                      <span className="text-text-secondary font-medium">Card ID:</span>
                      <span className="text-text-primary font-mono text-xs bg-muted px-2 py-1 rounded">#{cardData.id}</span>
                    </div>
                  </div>
                </div>

                {/* Actions Section */}
                {(canEdit || canDelete) && (
                  <div className="bg-surface/50 rounded-lg p-6 border border-border/30 space-y-4">
                    <h4 className="font-semibold text-text-primary flex items-center gap-2">
                      <Icon name="Settings" size={16} className="text-primary" />
                      Actions
                    </h4>
                    <div className="space-y-2">
                      {canEdit && (
                        <button className="w-full flex items-center gap-3 px-4 py-3 text-sm text-text-primary hover:bg-muted/50 rounded-lg transition-colors border border-transparent hover:border-border/30">
                          <Icon name="Archive" size={16} className="text-text-secondary" />
                          Archive Card
                        </button>
                      )}
                      {canEdit && (
                        <button className="w-full flex items-center gap-3 px-4 py-3 text-sm text-text-primary hover:bg-muted/50 rounded-lg transition-colors border border-transparent hover:border-border/30">
                          <Icon name="Copy" size={16} className="text-text-secondary" />
                          Copy Card
                        </button>
                      )}
                      {canEdit && (
                        <button className="w-full flex items-center gap-3 px-4 py-3 text-sm text-text-primary hover:bg-muted/50 rounded-lg transition-colors border border-transparent hover:border-border/30">
                          <Icon name="Move" size={16} className="text-text-secondary" />
                          Move Card
                        </button>
                      )}
                      {canDelete && (
                        <button
                          onClick={handleDelete}
                          className="w-full flex items-center gap-3 px-4 py-3 text-sm text-destructive hover:bg-destructive/10 rounded-lg transition-colors border border-transparent hover:border-destructive/20"
                        >
                          <Icon name="Trash2" size={16} className="text-destructive" />
                          Delete Card
                        </button>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CardDetails;
