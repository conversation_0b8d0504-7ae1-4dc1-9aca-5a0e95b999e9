"""
File upload endpoints
"""
import os
import uuid
from typing import List
from fastapi import APIRouter, Depends, UploadFile, File, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select

from app.core.database import get_db
from app.core.deps import get_current_active_user
from app.core.exceptions import ValidationError, ResourceNotFoundError
from app.models.user import User
from app.models.card import Card
from app.models.attachment import Attachment
from app.config import settings

router = APIRouter()


def validate_file(file: UploadFile) -> None:
    """Validate uploaded file"""
    # Check file size
    if file.size > settings.max_file_size:
        raise ValidationError(f"File size exceeds maximum allowed size of {settings.max_file_size} bytes")
    
    # Check file type
    file_extension = file.filename.split('.')[-1].lower() if '.' in file.filename else ''
    if file_extension not in settings.allowed_file_types:
        raise ValidationError(f"File type '{file_extension}' not allowed. Allowed types: {', '.join(settings.allowed_file_types)}")


def generate_filename(original_filename: str) -> str:
    """Generate unique filename"""
    file_extension = original_filename.split('.')[-1] if '.' in original_filename else ''
    unique_id = str(uuid.uuid4())
    return f"{unique_id}.{file_extension}" if file_extension else unique_id


async def save_file(file: UploadFile, directory: str) -> str:
    """Save file to storage and return URL"""
    # Create directory if it doesn't exist
    os.makedirs(directory, exist_ok=True)
    
    # Generate unique filename
    filename = generate_filename(file.filename)
    file_path = os.path.join(directory, filename)
    
    # Save file
    with open(file_path, "wb") as buffer:
        content = await file.read()
        buffer.write(content)
    
    # Return relative URL
    return f"/uploads/{os.path.relpath(file_path, 'uploads')}"


@router.post("/avatar")
async def upload_avatar(
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Upload user avatar"""
    # Validate file
    validate_file(file)
    
    # Additional validation for avatars
    if not file.content_type.startswith('image/'):
        raise ValidationError("Avatar must be an image file")
    
    # Save file
    directory = f"uploads/avatars/{current_user.id}"
    file_url = await save_file(file, directory)
    
    # Update user avatar URL
    current_user.avatar_url = file_url
    await db.commit()
    
    return {
        "success": True,
        "data": {
            "avatar_url": file_url
        },
        "message": "Avatar uploaded successfully"
    }


@router.post("/organization-logo")
async def upload_organization_logo(
    org_id: str,
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Upload organization logo"""
    # Validate file
    validate_file(file)
    
    # Additional validation for logos
    if not file.content_type.startswith('image/'):
        raise ValidationError("Logo must be an image file")
    
    # TODO: Check if user has permission to upload logo for this organization
    
    # Save file
    directory = f"uploads/organizations/{org_id}"
    file_url = await save_file(file, directory)
    
    return {
        "success": True,
        "data": {
            "logo_url": file_url
        },
        "message": "Logo uploaded successfully"
    }


@router.post("/card-attachment")
async def upload_card_attachment(
    card_id: str,
    file: UploadFile = File(...),
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Upload card attachment"""
    # Validate file
    validate_file(file)
    
    # Check if card exists and user has access
    card_result = await db.execute(
        select(Card).where(Card.id == card_id)
    )
    card = card_result.scalar_one_or_none()
    if not card:
        raise ResourceNotFoundError("Card not found")
    
    # TODO: Check if user has permission to add attachments to this card
    
    # Save file
    directory = f"uploads/attachments/{card_id}"
    file_url = await save_file(file, directory)
    
    # Create attachment record
    attachment = Attachment(
        card_id=card_id,
        filename=generate_filename(file.filename),
        original_name=file.filename,
        file_size=file.size,
        mime_type=file.content_type,
        file_url=file_url,
        uploaded_by=current_user.id
    )
    
    db.add(attachment)
    await db.commit()
    await db.refresh(attachment)
    
    return {
        "success": True,
        "data": {
            "attachment": {
                "id": str(attachment.id),
                "filename": attachment.filename,
                "original_name": attachment.original_name,
                "file_size": attachment.file_size,
                "mime_type": attachment.mime_type,
                "file_url": attachment.file_url,
                "uploaded_at": attachment.uploaded_at.isoformat()
            }
        },
        "message": "Attachment uploaded successfully"
    }


@router.delete("/{file_id}")
async def delete_file(
    file_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Delete uploaded file"""
    # Check if file is an attachment
    attachment_result = await db.execute(
        select(Attachment).where(Attachment.id == file_id)
    )
    attachment = attachment_result.scalar_one_or_none()
    
    if attachment:
        # TODO: Check if user has permission to delete this attachment
        
        # Delete file from storage
        try:
            file_path = f"uploads{attachment.file_url.replace('/uploads', '')}"
            if os.path.exists(file_path):
                os.remove(file_path)
        except Exception:
            pass  # File might already be deleted
        
        # Delete attachment record
        await db.delete(attachment)
        await db.commit()
        
        return {"success": True, "message": "Attachment deleted successfully"}
    
    raise ResourceNotFoundError("File not found")


@router.get("/{file_id}/download")
async def download_file(
    file_id: str,
    current_user: User = Depends(get_current_active_user),
    db: AsyncSession = Depends(get_db)
):
    """Download file"""
    # Check if file is an attachment
    attachment_result = await db.execute(
        select(Attachment).where(Attachment.id == file_id)
    )
    attachment = attachment_result.scalar_one_or_none()
    
    if attachment:
        # TODO: Check if user has permission to download this attachment
        # TODO: Return file download response
        return {
            "success": True,
            "data": {
                "download_url": attachment.file_url
            }
        }
    
    raise ResourceNotFoundError("File not found")
