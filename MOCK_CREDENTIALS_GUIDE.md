# 🔐 Mock Credentials & Testing Guide

## 🎯 **TESTING CREDENTIALS**

### **Production-Ready Mock Accounts**

These credentials are configured and ready for testing all features of the project management system:

#### **🔑 Owner Account (Full Access)**
```
Email: <EMAIL>
Password: Owner123!
Role: Owner
```
**Features Available:**
- ✅ Create and manage organizations
- ✅ Create and manage projects
- ✅ Full team management (invite, remove, change roles)
- ✅ Access to all project boards and kanban views
- ✅ Budget visibility and management
- ✅ Analytics and reporting
- ✅ Organization settings
- ✅ Export data and generate reports

#### **🔑 Admin Account (Management Access)**
```
Email: <EMAIL>
Password: Admin123!
Role: Admin
```
**Features Available:**
- ✅ Create and manage projects (if permission granted)
- ✅ Team management within assigned projects
- ✅ Access to project boards and kanban views
- ✅ Task creation and management
- ✅ Project settings (limited)
- ✅ Invite team members
- ❌ Budget visibility (restricted)
- ❌ Organization-level settings

#### **🔑 Member Account (Project Access)**
```
Email: <EMAIL>
Password: Member123!
Role: Member
```
**Features Available:**
- ✅ View assigned projects
- ✅ Access to project boards and kanban views
- ✅ Create and edit tasks
- ✅ Collaborate on project activities
- ✅ Basic team view
- ❌ Project creation (unless specifically assigned)
- ❌ Team management
- ❌ Budget visibility

#### **🔑 Viewer Account (Read-Only)**
```
Email: <EMAIL>
Password: Viewer123!
Role: Viewer
```
**Features Available:**
- ✅ View dashboard and project summaries
- ✅ Read-only access to project boards
- ✅ View team information
- ✅ Export and download reports
- ❌ Create or edit projects
- ❌ Create or edit tasks
- ❌ Team management
- ❌ Any modification actions

## 🚀 **TESTING WORKFLOW**

### **1. Authentication Testing**
1. Navigate to the login page
2. Use any of the credentials above
3. Verify successful login and role-based dashboard redirect
4. Check that the header displays correct user information and role

### **2. Project Management Testing**

#### **For Owner/Admin Roles:**
1. **Create New Project:**
   - Click "New Project" button in dashboard or project management page
   - Fill out project details
   - Verify project creation and navigation

2. **Board Navigation:**
   - Click the "Board" button (Kanban icon) on any project card
   - Verify navigation to kanban board view
   - Test drag-and-drop functionality

3. **Task Management:**
   - Click "Add Task" button in project overview or tasks tab
   - Create new tasks with different priorities and assignments
   - Verify tasks appear in kanban board

#### **For Member Role:**
1. **Project Access:**
   - View assigned projects only
   - Access kanban boards for assigned projects
   - Create and edit tasks within permissions

2. **Task Creation:**
   - Use "Add Task" button in accessible projects
   - Verify task creation works within role permissions

#### **For Viewer Role:**
1. **Read-Only Verification:**
   - Confirm no "New Project" or "Add Task" buttons appear
   - Verify board access is read-only
   - Test export and download features

### **3. Navigation Testing**
- **Board Button:** Available on all project cards, redirects to `/kanban-board`
- **Project View:** Click project cards to access project management view
- **Quick Actions:** Test "Go to Board" and "Add New Task" buttons in project overview

## 🔧 **ROLE-BASED ACCESS CONTROL**

### **Project Creation Permissions:**
- **Owner:** ✅ Can create projects in any organization they own
- **Admin:** ✅ Can create projects if permission is granted by owner
- **Member:** ❌ Cannot create projects (view assigned projects only)
- **Viewer:** ❌ Cannot create projects (read-only access)

### **Task Creation Permissions:**
- **Owner:** ✅ Can create tasks in any project
- **Admin:** ✅ Can create tasks in managed projects
- **Member:** ✅ Can create tasks in assigned projects
- **Viewer:** ❌ Cannot create tasks (read-only access)

### **Board Access:**
- **All Roles:** Can access kanban boards for their respective projects
- **Viewer:** Read-only board access (no drag-and-drop or editing)

## 🐛 **TROUBLESHOOTING**

### **Login Issues:**
- Ensure credentials are typed exactly as shown (case-sensitive)
- Clear browser cache and localStorage if needed
- Check browser console for authentication errors

### **Feature Access Issues:**
- Verify you're using the correct role for the feature you're testing
- Some features may require specific project assignments
- Check that the backend server is running (if using real API)

### **Navigation Issues:**
- Ensure project data is loaded before clicking Board buttons
- Check browser console for navigation errors
- Verify project IDs are properly stored in localStorage

## 📝 **NOTES**

- These credentials work with both mock and real backend implementations
- The system automatically handles role-based UI rendering
- All buttons and features are dynamically shown/hidden based on user permissions
- Project and task data persists during the session
- For testing with real backend, ensure the server is running on the configured port

## 🔄 **BACKEND COMPATIBILITY**

These credentials are compatible with:
- ✅ Mock authentication service
- ✅ Real backend API (when running)
- ✅ Enhanced server with RBAC
- ✅ Simple server implementation

The system automatically detects the available backend and adjusts accordingly.
