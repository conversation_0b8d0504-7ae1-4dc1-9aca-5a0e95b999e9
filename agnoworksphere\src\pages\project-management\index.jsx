import React, { useState, useEffect } from 'react';
import { useLocation, useSearchParams, useNavigate } from 'react-router-dom';
import RoleBasedHeader from '../../components/ui/RoleBasedHeader';
// import Sidebar from '../../components/ui/Sidebar';
import Breadcrumb from '../../components/ui/Breadcrumb';
import Icon from '../../components/AppIcon';
import Button from '../../components/ui/Button';
import ProjectOverview from './components/ProjectOverview';
import TasksTab from './components/TasksTab';
import SettingsTab from './components/SettingsTab';
import authService from '../../utils/authService';
import apiService from '../../utils/apiService';
import AIReportModal from '../../components/modals/AIReportModal';
import CreateProjectModal from '../../components/modals/CreateProjectModal';
import { useProject } from '../../contexts/ProjectContext';
import { listenForProjectUpdates } from '../../utils/projectEventService';

const ProjectManagement = () => {
  const location = useLocation();
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState('overview');
  const [sidebarExpanded, setSidebarExpanded] = useState(true);

  // Authentication state
  const [currentUser, setCurrentUser] = useState(null);
  const [userRole, setUserRole] = useState('member');
  const [currentOrganization, setCurrentOrganization] = useState(null);

  // Project context
  const { currentProject, loading, error, switchProject, updateCurrentProject } = useProject();
  const [isAIReportModalOpen, setIsAIReportModalOpen] = useState(false);
  const [isCreateProjectModalOpen, setIsCreateProjectModalOpen] = useState(false);

  const tabs = [
    {
      id: 'overview',
      label: 'Overview',
      icon: 'LayoutDashboard',
      description: 'Project summary and key metrics'
    },
    {
      id: 'tasks',
      label: 'Tasks',
      icon: 'CheckSquare',
      description: 'Task management and tracking'
    },
    {
      id: 'settings',
      label: 'Settings',
      icon: 'Settings',
      description: 'Project configuration and permissions'
    }
  ];

  const handleTabChange = (tabId) => {
    setActiveTab(tabId);
  };

  const handleGenerateReport = () => {
    setIsAIReportModalOpen(true);
  };

  const handleOpenCreateProject = () => {
    setIsCreateProjectModalOpen(true);
  };

  const handleCloseCreateProject = () => {
    setIsCreateProjectModalOpen(false);
  };

  const handleCreateProject = async (projectData) => {
    try {
      if (!currentOrganization?.id) {
        throw new Error('No organization selected');
      }

      console.log('Creating project with data:', projectData);
      console.log('Organization ID:', currentOrganization.id);

      // Create via API
      const newProject = await apiService.projects.create(currentOrganization.id, projectData);
      console.log('Project creation response:', newProject);

      // Show success message or navigate to new project
      console.log('Project created successfully:', newProject);

      // Optionally navigate to the new project
      if (newProject && newProject.id) {
        navigate(`/project-management?id=${newProject.id}`, {
          state: {
            projectId: newProject.id,
            project: newProject
          }
        });
      }

      setIsCreateProjectModalOpen(false);
    } catch (error) {
      console.error('Failed to create project:', error);
      throw error; // Let the modal handle the error display
    }
  };

  const renderTabContent = () => {
    switch (activeTab) {
      case 'overview':
        return <ProjectOverview project={currentProject} userRole={userRole} />;
      case 'tasks':
        return <TasksTab project={currentProject} userRole={userRole} />;
      case 'settings':
        return <SettingsTab project={currentProject} userRole={userRole} />;
      default:
        return <ProjectOverview project={currentProject} userRole={userRole} />;
    }
  };

  const getActiveTabInfo = () => {
    return tabs.find(tab => tab.id === activeTab) || tabs[0];
  };

  // Load user authentication data
  useEffect(() => {
    const loadUserData = async () => {
      try {
        const userResponse = await authService.getCurrentUser();
        const orgResponse = await authService.getCurrentOrganization();

        if (userResponse.data && userResponse.data.user) {
          setCurrentUser(userResponse.data.user);
          setUserRole(userResponse.data.user.role || 'member');
        }

        if (orgResponse.data && orgResponse.data.organization) {
          setCurrentOrganization(orgResponse.data.organization);
        }
      } catch (error) {
        console.error('Error loading user data:', error);
        setUserRole('member');
      }
    };

    loadUserData();
  }, []);

  // Handle project switching from URL params
  useEffect(() => {
    const projectId = searchParams.get('id');
    if (projectId && currentProject?.id !== projectId) {
      switchProject(projectId);
    }
  }, [searchParams, currentProject, switchProject]);

  // Listen for global project updates
  useEffect(() => {
    const cleanup = listenForProjectUpdates((updateData) => {
      const { action, project, organizationId } = updateData;

      // If this is our current project and it was updated, refresh
      if (currentProject && project && project.id === currentProject.id) {
        if (action === 'updated') {
          updateCurrentProject(project);
        } else if (action === 'deleted') {
          // Project was deleted, redirect to dashboard
          navigate('/role-based-dashboard');
        }
      }

      // If a new project was created in our organization, we might want to refresh
      if (action === 'created' && currentOrganization && organizationId === currentOrganization.id) {
        console.log('New project created in organization:', project);
      }
    });

    return cleanup;
  }, [currentProject, currentOrganization, navigate, updateCurrentProject]);

  return (
    <div className="min-h-screen bg-background">
      <RoleBasedHeader
        userRole={userRole.toLowerCase()}
        currentUser={currentUser ? {
          name: `${currentUser.firstName} ${currentUser.lastName}`,
          email: currentUser.email,
          avatar: currentUser.avatar || '/assets/images/avatar.jpg',
          role: userRole
        } : {
          name: 'Loading...',
          email: '',
          avatar: '/assets/images/avatar.jpg',
          role: userRole
        }}
        currentOrganization={currentOrganization}
      />
      {/* <Sidebar /> */}
      
      <main className={`transition-all duration-300 ${sidebarExpanded ? 'ml-60' : 'ml-16'} pt-16`}>
        <div className="p-6 max-w-7xl mx-auto">
          <Breadcrumb projectName={currentProject?.name} />

          {/* Loading State */}
          {loading && (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"></div>
                <p className="text-text-secondary">Loading project...</p>
              </div>
            </div>
          )}

          {/* Error State */}
          {error && !loading && (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <Icon name="AlertTriangle" size={48} className="text-destructive mx-auto mb-4" />
                <h3 className="text-lg font-semibold text-text-primary mb-2">Error Loading Project</h3>
                <p className="text-text-secondary mb-4">{error}</p>
                <Button
                  onClick={() => window.location.reload()}
                  variant="outline"
                >
                  Try Again
                </Button>
              </div>
            </div>
          )}

          {/* Project Content */}
          {!loading && !error && currentProject && (
            <>
              {/* Page Header */}
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4 mb-8">
                <div>
                  <h1 className="text-3xl font-bold text-foreground mb-2">{currentProject.name}</h1>
                  <p className="text-text-secondary">
                    {currentProject.description || 'Project management and team coordination'}
                  </p>
                </div>
            <div className="flex gap-3">
              <Button variant="outline" iconName="Download" iconPosition="left">
                Export Data
              </Button>
              <Button
                variant="outline"
                iconName="BarChart3"
                iconPosition="left"
                onClick={handleGenerateReport}
              >
                Generate Report
              </Button>
              <Button
                variant="default"
                iconName="Plus"
                iconPosition="left"
                onClick={handleOpenCreateProject}
              >
                New Project
              </Button>
            </div>
          </div>

          {/* Tab Navigation */}
          <div className="bg-card rounded-lg border border-border mb-6">
            {/* Desktop Tab Navigation */}
            <div className="hidden md:flex border-b border-border">
              {tabs.map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => handleTabChange(tab.id)}
                  className={`flex items-center gap-3 px-6 py-4 font-medium transition-colors relative ${
                    activeTab === tab.id
                      ? 'text-primary border-b-2 border-primary bg-primary/5' :'text-text-secondary hover:text-foreground hover:bg-muted/50'
                  }`}
                >
                  <Icon name={tab.icon} size={18} />
                  <div className="text-left">
                    <div className="font-medium">{tab.label}</div>
                    <div className="text-xs text-text-secondary">{tab.description}</div>
                  </div>
                </button>
              ))}
            </div>

            {/* Mobile Tab Navigation */}
            <div className="md:hidden border-b border-border p-4">
              <div className="relative">
                <select
                  value={activeTab}
                  onChange={(e) => handleTabChange(e.target.value)}
                  className="w-full appearance-none bg-background border border-border rounded-lg px-4 py-3 pr-10 text-foreground focus:outline-none focus:ring-2 focus:ring-primary focus:border-transparent"
                >
                  {tabs.map((tab) => (
                    <option key={tab.id} value={tab.id}>
                      {tab.label} - {tab.description}
                    </option>
                  ))}
                </select>
                <Icon 
                  name="ChevronDown" 
                  size={20} 
                  className="absolute right-3 top-1/2 transform -translate-y-1/2 text-text-secondary pointer-events-none" 
                />
              </div>
            </div>

            {/* Tab Content */}
            <div className="p-6">
              {/* Active Tab Header */}
              <div className="flex items-center gap-3 mb-6 md:hidden">
                <Icon name={getActiveTabInfo().icon} size={24} className="text-primary" />
                <div>
                  <h2 className="text-xl font-semibold text-foreground">{getActiveTabInfo().label}</h2>
                  <p className="text-sm text-text-secondary">{getActiveTabInfo().description}</p>
                </div>
              </div>

              {/* Render Active Tab Content */}
              {renderTabContent()}
            </div>
          </div>

          {/* Quick Actions Footer */}
          <div className="bg-card rounded-lg border border-border p-6">
            <div className="flex flex-col sm:flex-row items-center justify-between gap-4">
              <div className="flex items-center gap-3">
                <Icon name="Zap" size={20} className="text-primary" />
                <div>
                  <h3 className="font-medium text-foreground">Quick Actions</h3>
                  <p className="text-sm text-text-secondary">Frequently used project management tools</p>
                </div>
              </div>
              <div className="flex flex-wrap gap-2">
                <Button variant="outline" size="sm" iconName="UserPlus" iconPosition="left">
                  Invite Team
                </Button>
                <Button variant="outline" size="sm" iconName="Calendar" iconPosition="left">
                  Schedule Meeting
                </Button>
                <Button variant="outline" size="sm" iconName="FileText" iconPosition="left">
                  Create Template
                </Button>
                <Button variant="outline" size="sm" iconName="Archive" iconPosition="left">
                  Backup Project
                </Button>
              </div>
            </div>
          </div>
            </>
          )}
        </div>
      </main>

      {/* AI Report Modal */}
      <AIReportModal
        isOpen={isAIReportModalOpen}
        onClose={() => setIsAIReportModalOpen(false)}
        project={currentProject}
      />

      {/* Create Project Modal */}
      <CreateProjectModal
        isOpen={isCreateProjectModalOpen}
        onClose={handleCloseCreateProject}
        onCreateProject={handleCreateProject}
        organizationId={currentOrganization?.id}
        organizationName={currentOrganization?.name || 'Organization'}
      />
    </div>
  );
};

export default ProjectManagement;