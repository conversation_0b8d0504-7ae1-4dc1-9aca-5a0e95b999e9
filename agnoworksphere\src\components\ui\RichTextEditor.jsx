import React, { useState, useRef, useEffect } from 'react';
import { cn } from '../../utils/cn';
import Button from './Button';
import Icon from '../AppIcon';

const RichTextEditor = React.forwardRef(({
  className,
  value = '',
  onChange,
  placeholder = 'Start typing...',
  maxLength = 10000,
  label,
  description,
  error,
  disabled = false,
  showToolbar = true,
  showWordCount = true,
  allowMedia = true,
  ...props
}, ref) => {
  const [content, setContent] = useState(value);
  const [wordCount, setWordCount] = useState(0);
  const [lineCount, setLineCount] = useState(1);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [selectedText, setSelectedText] = useState('');
  const editorRef = useRef(null);
  const fileInputRef = useRef(null);

  useEffect(() => {
    setContent(value);
    updateCounts(value);
  }, [value]);

  const updateCounts = (text) => {
    const words = text.trim() ? text.trim().split(/\s+/).length : 0;
    const lines = text.split('\n').length;
    setWordCount(words);
    setLineCount(lines);
  };

  const handleContentChange = (e) => {
    const newContent = e.target.value;
    if (newContent.length <= maxLength) {
      setContent(newContent);
      updateCounts(newContent);
      onChange?.(newContent);
    }
  };

  const handleFormat = (command, value = null) => {
    if (editorRef.current) {
      editorRef.current.focus();
      document.execCommand(command, false, value);
      const newContent = editorRef.current.innerHTML;
      onChange?.(newContent);
    }
  };

  const handleFileUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (event) => {
        const fileUrl = event.target.result;
        if (file.type.startsWith('image/')) {
          insertMedia('image', fileUrl, file.name);
        } else if (file.type.startsWith('video/')) {
          insertMedia('video', fileUrl, file.name);
        }
      };
      reader.readAsDataURL(file);
    }
  };

  const insertMedia = (type, url, name) => {
    const mediaHtml = type === 'image' 
      ? `<img src="${url}" alt="${name}" style="max-width: 100%; height: auto; margin: 10px 0;" />`
      : `<video src="${url}" controls style="max-width: 100%; height: auto; margin: 10px 0;"></video>`;
    
    handleFormat('insertHTML', mediaHtml);
  };

  const insertLink = () => {
    const url = prompt('Enter URL:');
    if (url) {
      const text = selectedText || url;
      const linkHtml = `<a href="${url}" target="_blank" rel="noopener noreferrer">${text}</a>`;
      handleFormat('insertHTML', linkHtml);
    }
  };

  const toolbarButtons = [
    { command: 'bold', icon: 'Bold', title: 'Bold' },
    { command: 'italic', icon: 'Italic', title: 'Italic' },
    { command: 'underline', icon: 'Underline', title: 'Underline' },
    { command: 'strikeThrough', icon: 'Strikethrough', title: 'Strikethrough' },
    { type: 'separator' },
    { command: 'insertUnorderedList', icon: 'List', title: 'Bullet List' },
    { command: 'insertOrderedList', icon: 'ListOrdered', title: 'Numbered List' },
    { type: 'separator' },
    { command: 'justifyLeft', icon: 'AlignLeft', title: 'Align Left' },
    { command: 'justifyCenter', icon: 'AlignCenter', title: 'Align Center' },
    { command: 'justifyRight', icon: 'AlignRight', title: 'Align Right' },
    { type: 'separator' },
    { action: 'link', icon: 'Link', title: 'Insert Link' },
    { action: 'media', icon: 'Image', title: 'Insert Media' },
    { type: 'separator' },
    { action: 'fullscreen', icon: 'Maximize', title: 'Fullscreen' }
  ];

  return (
    <div className={cn(
      "space-y-2",
      isFullscreen && "fixed inset-0 z-50 bg-background p-4",
      className
    )}>
      {label && (
        <label className="text-sm font-medium text-foreground">
          {label}
        </label>
      )}

      <div className={cn(
        "border border-border rounded-lg overflow-hidden",
        error && "border-destructive",
        disabled && "opacity-50 pointer-events-none"
      )}>
        {showToolbar && (
          <div className="flex items-center gap-1 p-2 border-b border-border bg-secondary/30">
            {toolbarButtons.map((button, index) => {
              if (button.type === 'separator') {
                return <div key={index} className="w-px h-6 bg-border mx-1" />;
              }

              if (button.action === 'link') {
                return (
                  <Button
                    key={button.action}
                    variant="ghost"
                    size="sm"
                    onClick={insertLink}
                    title={button.title}
                  >
                    <Icon name={button.icon} className="h-4 w-4" />
                  </Button>
                );
              }

              if (button.action === 'media' && allowMedia) {
                return (
                  <Button
                    key={button.action}
                    variant="ghost"
                    size="sm"
                    onClick={() => fileInputRef.current?.click()}
                    title={button.title}
                  >
                    <Icon name={button.icon} className="h-4 w-4" />
                  </Button>
                );
              }

              if (button.action === 'fullscreen') {
                return (
                  <Button
                    key={button.action}
                    variant="ghost"
                    size="sm"
                    onClick={() => setIsFullscreen(!isFullscreen)}
                    title={button.title}
                  >
                    <Icon name={isFullscreen ? 'Minimize' : 'Maximize'} className="h-4 w-4" />
                  </Button>
                );
              }

              return (
                <Button
                  key={button.command}
                  variant="ghost"
                  size="sm"
                  onClick={() => handleFormat(button.command)}
                  title={button.title}
                >
                  <Icon name={button.icon} className="h-4 w-4" />
                </Button>
              );
            })}
          </div>
        )}

        <div
          ref={editorRef}
          contentEditable={!disabled}
          className={cn(
            "min-h-[200px] p-4 focus:outline-none",
            isFullscreen && "min-h-[calc(100vh-200px)]"
          )}
          style={{ maxHeight: isFullscreen ? 'calc(100vh - 200px)' : '400px', overflowY: 'auto' }}
          onInput={handleContentChange}
          onMouseUp={() => {
            const selection = window.getSelection();
            setSelectedText(selection.toString());
          }}
          dangerouslySetInnerHTML={{ __html: content }}
          {...props}
        />

        {(showWordCount || maxLength) && (
          <div className="flex justify-between items-center p-2 border-t border-border bg-secondary/30 text-xs text-muted-foreground">
            <div className="flex gap-4">
              {showWordCount && (
                <>
                  <span>{wordCount} words</span>
                  <span>{lineCount} lines</span>
                </>
              )}
            </div>
            {maxLength && (
              <span className={content.length > maxLength * 0.9 ? 'text-warning' : ''}>
                {content.length}/{maxLength}
              </span>
            )}
          </div>
        )}
      </div>

      {allowMedia && (
        <input
          ref={fileInputRef}
          type="file"
          accept="image/*,video/*"
          onChange={handleFileUpload}
          className="hidden"
        />
      )}

      {description && !error && (
        <p className="text-xs text-muted-foreground">
          {description}
        </p>
      )}

      {error && (
        <p className="text-xs text-destructive">
          {error}
        </p>
      )}
    </div>
  );
});

RichTextEditor.displayName = "RichTextEditor";

export default RichTextEditor;
