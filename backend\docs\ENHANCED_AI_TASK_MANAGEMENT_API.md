# Enhanced AI Task Management Modal - Backend API Documentation

This document outlines all the backend APIs and endpoints developed to support the enhanced AI Task Management Modal system with modern visual design.

## 🚀 **New API Endpoints Overview**

### **1. AI Projects Management**
**Base URL:** `/api/v1/ai-projects`

#### **POST /ai-preview**
Generate AI project preview without creating actual project.

**Request Body:**
```json
{
  "name": "My Web Application",
  "project_type": "web_application",
  "team_size": 5,
  "team_experience": "intermediate",
  "organization_id": "org-123"
}
```

**Response:**
```json
{
  "project": {
    "name": "My Web Application",
    "description": "AI-generated project description",
    "objectives": ["Objective 1", "Objective 2"],
    "success_criteria": ["Criteria 1", "Criteria 2"]
  },
  "workflow": {
    "phases": ["Planning", "Development", "Testing"],
    "total_duration_weeks": 12,
    "methodology": "agile"
  },
  "tasks": [
    {
      "title": "Setup Development Environment",
      "description": "Configure development tools and environment",
      "priority": "high",
      "estimated_hours": 8,
      "phase": "Planning"
    }
  ],
  "estimated_duration": 60,
  "estimated_cost": 45000.0
}
```

#### **POST /ai-create**
Create actual AI project from confirmed preview data.

**Request Body:**
```json
{
  "project_id": "preview-123",
  "confirmation_data": {
    "name": "Confirmed Project Name",
    "description": "Final project description",
    "organization_id": "org-123"
  },
  "final_tasks": [...],
  "workflow": {...}
}
```

#### **PUT /tasks/bulk-update**
Bulk update multiple tasks.

**Request Body:**
```json
{
  "task_ids": ["task-1", "task-2", "task-3"],
  "updates": {
    "priority": "high",
    "status": "in_progress"
  },
  "operation": "update"
}
```

#### **POST /workflow/update**
Update project workflow for specific phase.

**Request Body:**
```json
{
  "project_id": "proj-123",
  "phase": "configure",
  "workflow_data": {
    "team_size": 8,
    "methodology": "scrum",
    "sprint_duration": 2
  }
}
```

#### **POST /suggestions/generate**
Generate smart suggestions for project optimization.

**Request Body:**
```json
{
  "project_id": "proj-123",
  "suggestion_type": "task_optimization",
  "context": {
    "current_tasks": 25,
    "team_capacity": 40
  }
}
```

**Response:**
```json
{
  "success": true,
  "data": [
    {
      "id": "opt_1",
      "type": "task_optimization",
      "title": "Break down large tasks",
      "description": "Consider breaking tasks with >40 hours into smaller subtasks",
      "impact": "high",
      "effort": "low"
    }
  ]
}
```

#### **POST /suggestions/apply**
Apply a smart suggestion to the project.

**Query Parameters:**
- `suggestion_id`: ID of the suggestion to apply
- `project_id`: Target project ID

#### **GET /templates**
Get available project templates.

**Query Parameters:**
- `project_type` (optional): Filter by project type

#### **GET /tech-stacks**
Get available technology stacks for projects.

**Query Parameters:**
- `project_type` (optional): Filter by project type

---

### **2. Meeting Management**
**Base URL:** `/api/v1/meetings`

#### **POST /create**
Create a new meeting.

**Request Body:**
```json
{
  "title": "Sprint Planning Meeting",
  "description": "Plan upcoming sprint tasks",
  "project_id": "proj-123",
  "meeting_type": "planning",
  "start_time": "2024-01-15T10:00:00Z",
  "duration": 60,
  "attendees": ["user-1", "user-2", "user-3"],
  "agenda": ["Review backlog", "Estimate tasks", "Plan sprint"],
  "is_recurring": false
}
```

**Response:**
```json
{
  "id": "meeting-123",
  "title": "Sprint Planning Meeting",
  "meeting_url": "https://meet.google.com/abc-defg-hij",
  "status": "scheduled",
  "attendees": [
    {
      "id": "user-1",
      "name": "John Doe",
      "email": "<EMAIL>"
    }
  ],
  "created_at": "2024-01-10T09:00:00Z"
}
```

#### **POST /instant**
Create and start an instant meeting.

**Request Body:**
```json
{
  "project_id": "proj-123",
  "meeting_type": "standup",
  "title": "Quick Standup",
  "attendees": ["user-1", "user-2"],
  "duration": 30
}
```

#### **GET /project/{project_id}**
Get meetings for a project.

**Query Parameters:**
- `status` (optional): Filter by meeting status

#### **PUT /{meeting_id}**
Update a meeting.

#### **DELETE /{meeting_id}**
Delete a meeting.

#### **POST /calendar/integrate**
Integrate with external calendar provider.

**Request Body:**
```json
{
  "provider": "google",
  "auth_code": "auth_code_from_oauth",
  "project_id": "proj-123"
}
```

---

### **3. Task Dependencies Management**
**Base URL:** `/api/v1/dependencies`

#### **POST /create**
Create a task dependency.

**Request Body:**
```json
{
  "source_task_id": "task-1",
  "target_task_id": "task-2",
  "dependency_type": "blocks",
  "description": "Task 1 must be completed before Task 2 can start"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "id": "dep-123",
    "source_task_id": "task-1",
    "target_task_id": "task-2",
    "dependency_type": "blocks",
    "is_active": true,
    "created_at": "2024-01-10T09:00:00Z"
  }
}
```

#### **GET /project/{project_id}**
Get all dependencies for a project.

**Query Parameters:**
- `dependency_type` (optional): Filter by dependency type

#### **POST /validate**
Validate a set of dependencies for circular references.

**Request Body:**
```json
{
  "dependencies": [
    {
      "source_task_id": "task-1",
      "target_task_id": "task-2",
      "dependency_type": "blocks"
    }
  ],
  "project_id": "proj-123"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "overall_valid": true,
    "validations": [
      {
        "source_task_id": "task-1",
        "target_task_id": "task-2",
        "is_valid": true,
        "errors": []
      }
    ]
  }
}
```

#### **POST /visualization**
Get dependency visualization data for a project.

**Request Body:**
```json
{
  "project_id": "proj-123",
  "include_completed": false,
  "max_depth": 3
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "nodes": [
      {
        "id": "task-1",
        "label": "Setup Environment",
        "status": "completed",
        "priority": "high"
      }
    ],
    "edges": [
      {
        "id": "dep-1",
        "source": "task-1",
        "target": "task-2",
        "type": "blocks"
      }
    ],
    "layout": "hierarchical"
  }
}
```

#### **POST /critical-path**
Calculate critical path for project tasks.

**Request Body:**
```json
{
  "project_id": "proj-123",
  "start_date": "2024-01-15T00:00:00Z",
  "end_date": "2024-03-15T00:00:00Z"
}
```

**Response:**
```json
{
  "success": true,
  "data": {
    "critical_path": [
      {
        "task_id": "task-1",
        "title": "Core Development",
        "duration": 40,
        "status": "in_progress"
      }
    ],
    "total_duration": 120,
    "bottlenecks": [
      {
        "task_id": "task-5",
        "dependency_count": 5,
        "risk_level": "high"
      }
    ],
    "recommendations": [
      "Consider parallelizing some tasks to reduce overall duration"
    ]
  }
}
```

#### **DELETE /{dependency_id}**
Delete a task dependency.

---

## 🔧 **Enhanced Existing Endpoints**

### **AI Service Extensions**
The existing AI service has been enhanced with new methods:

- `generate_ai_project_preview()` - Generate preview without creating project
- `create_project_from_confirmation()` - Create project from confirmed data
- `update_project_workflow()` - Update workflow by phase
- `generate_smart_suggestions()` - Generate optimization suggestions
- `apply_smart_suggestion()` - Apply suggestions to projects
- `get_project_templates()` - Get available templates
- `setup_project_integrations()` - Background integration setup

### **Bulk Operations**
Enhanced bulk operations for task management:

- Bulk task updates with validation
- Bulk assignment changes
- Bulk priority modifications
- Bulk status updates

### **Role-Based Permissions**
All new endpoints respect the existing role-based permission system:

- **Owner**: Full access to all AI project features
- **Admin**: Can manage projects within their scope
- **Member**: Can view and update assigned tasks
- **Viewer**: Read-only access to project information

---

## 📊 **Integration Points**

### **Frontend Integration**
The new endpoints integrate seamlessly with the enhanced frontend components:

- `EnhancedPhaseBreakdown` → AI Projects endpoints
- `MeetingScheduler` → Meeting Management endpoints
- `DependencyVisualization` → Task Dependencies endpoints
- `SmartSuggestions` → AI Suggestions endpoints
- `ProjectConfirmationSummary` → Project confirmation endpoints

### **Database Models**
New database models support the enhanced functionality:

- Meeting records with calendar integration
- Task dependency relationships
- AI suggestion tracking
- Project workflow states
- Integration configurations

### **Background Tasks**
Asynchronous processing for:

- Meeting notifications
- Calendar synchronization
- AI suggestion generation
- Project integration setup
- Dependency validation

---

## 🚀 **Performance Optimizations**

### **Caching Strategy**
- Project templates cached for 1 hour
- Technology stacks cached for 24 hours
- AI suggestions cached per project context
- Dependency graphs cached with invalidation

### **Async Processing**
- Meeting creation with background notifications
- AI project generation with progress tracking
- Bulk operations with status updates
- Integration setup with retry mechanisms

### **Rate Limiting**
- AI preview generation: 10 requests per minute per user
- Meeting creation: 20 requests per minute per user
- Bulk operations: 5 requests per minute per user
- Suggestion generation: 15 requests per minute per user

---

## 🔒 **Security Considerations**

### **Authentication & Authorization**
- All endpoints require valid JWT tokens
- Role-based access control enforced
- Organization membership validation
- Project access verification

### **Data Validation**
- Input sanitization for all endpoints
- Circular dependency prevention
- Meeting time conflict detection
- Task assignment validation

### **Privacy & Compliance**
- Meeting recordings not stored by default
- Calendar integration with user consent
- AI-generated content marked as such
- Data retention policies enforced

---

## 📈 **Monitoring & Analytics**

### **Endpoint Metrics**
- Response times for AI generation
- Success rates for project creation
- Meeting attendance tracking
- Dependency complexity analysis

### **Usage Analytics**
- Most used project templates
- Popular technology stacks
- Meeting patterns and effectiveness
- Task completion correlations

### **Error Tracking**
- AI generation failures
- Meeting creation errors
- Dependency validation issues
- Integration setup problems

---

## ✅ **Testing Coverage**

### **Unit Tests**
- AI service method testing
- Dependency validation logic
- Meeting scheduling algorithms
- Permission checking functions

### **Integration Tests**
- End-to-end project creation flow
- Meeting lifecycle management
- Dependency graph operations
- Bulk update transactions

### **Performance Tests**
- AI generation under load
- Concurrent meeting creation
- Large dependency graph handling
- Bulk operation scalability

---

This comprehensive backend API system fully supports the enhanced AI Task Management Modal with modern visual design, providing robust, scalable, and secure endpoints for all frontend functionality.
