#!/usr/bin/env python3
"""
Frontend Health Check
Quick test to verify frontend is working correctly
"""
import requests
import time

def test_frontend_health():
    """Test frontend health and functionality"""
    print("🌐 Testing Frontend Health")
    print("=" * 40)
    
    base_url = "http://localhost:3000"
    
    # Test main routes
    routes = [
        "/",
        "/login", 
        "/register",
        "/kanban-board",
        "/organization-dashboard"
    ]
    
    results = []
    
    for route in routes:
        try:
            url = f"{base_url}{route}"
            response = requests.get(url, timeout=10)
            
            if response.status_code == 200:
                # Check if it's a React app (contains root div)
                if 'id="root"' in response.text:
                    status = "✅ Working"
                    results.append(True)
                else:
                    status = "⚠️ No React root found"
                    results.append(False)
            else:
                status = f"❌ HTTP {response.status_code}"
                results.append(False)
                
            print(f"{route:<25} {status}")
            
        except requests.exceptions.RequestException as e:
            print(f"{route:<25} ❌ Connection Error: {str(e)[:50]}")
            results.append(False)
    
    # Summary
    working_routes = sum(results)
    total_routes = len(routes)
    success_rate = (working_routes / total_routes) * 100
    
    print("\n" + "=" * 40)
    print(f"📊 Frontend Health Summary")
    print(f"Working Routes: {working_routes}/{total_routes}")
    print(f"Success Rate: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print("🎉 Frontend is HEALTHY and ready!")
        return True
    else:
        print("⚠️ Frontend has issues that need attention")
        return False

def test_api_communication():
    """Test if frontend can communicate with backend"""
    print("\n🔗 Testing Frontend-Backend Communication")
    print("=" * 40)
    
    try:
        # Test backend health endpoint
        response = requests.get("http://localhost:3001/health", timeout=5)
        if response.status_code == 200:
            print("✅ Backend is responding")
            
            # Test if frontend can reach backend (CORS check)
            frontend_response = requests.get("http://localhost:3000", timeout=5)
            if frontend_response.status_code == 200:
                print("✅ Frontend is accessible")
                print("✅ CORS should be working (both servers running)")
                return True
            else:
                print("❌ Frontend not accessible")
                return False
        else:
            print("❌ Backend not responding")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Communication Error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 AGNO WORKSPHERE - FRONTEND HEALTH CHECK")
    print("=" * 50)
    
    # Test frontend health
    frontend_healthy = test_frontend_health()
    
    # Test API communication
    api_healthy = test_api_communication()
    
    # Final verdict
    print("\n" + "=" * 50)
    print("🏁 FINAL HEALTH CHECK RESULTS")
    print("=" * 50)
    
    if frontend_healthy and api_healthy:
        print("🎉 ALL SYSTEMS HEALTHY!")
        print("✅ Frontend is working perfectly")
        print("✅ Backend communication is working")
        print("✅ Ready for user access")
        print(f"\n🌐 Access your application at: http://localhost:3000")
    else:
        print("⚠️ Some issues detected:")
        if not frontend_healthy:
            print("❌ Frontend has issues")
        if not api_healthy:
            print("❌ Backend communication has issues")
        print("\n🔧 Please check the server logs for more details")
