#!/usr/bin/env python3
"""
Check Application Status and Registration API
Test frontend-backend integration and registration functionality
"""

import requests
import json
import time

def check_application_status():
    """Check the status of all application components"""
    print("🔍 CHECKING AGNO WORKSPHERE APPLICATION STATUS")
    print("=" * 60)
    
    # Check Frontend
    print("\n1. Frontend Status (React)...")
    try:
        frontend = requests.get('http://localhost:3000', timeout=5)
        print(f"   ✅ Frontend: {frontend.status_code}")
        if frontend.status_code == 200:
            print("   ✅ React application is running")
    except Exception as e:
        print(f"   ❌ Frontend Error: {e}")
    
    # Check Backend API
    print("\n2. Backend API Status...")
    try:
        backend = requests.get('http://localhost:3001/health', timeout=5)
        print(f"   ✅ Backend Health: {backend.status_code}")
        if backend.status_code == 200:
            data = backend.json()
            print(f"   ✅ API Version: {data.get('data', {}).get('version', 'unknown')}")
            print(f"   ✅ Status: {data.get('data', {}).get('status', 'unknown')}")
    except Exception as e:
        print(f"   ❌ Backend Error: {e}")
    
    # Check Monitoring
    print("\n3. Monitoring System...")
    try:
        monitoring = requests.get('http://localhost:3002/monitoring/health', timeout=5)
        print(f"   ✅ Monitoring: {monitoring.status_code}")
        if monitoring.status_code == 200:
            data = monitoring.json()
            print(f"   ✅ Health Status: {data.get('data', {}).get('health_status', 'unknown')}")
    except Exception as e:
        print(f"   ❌ Monitoring Error: {e}")
    
    # Test Registration API Endpoints
    print("\n4. Testing Registration API Endpoints...")
    
    endpoints_to_test = [
        'http://localhost:3001/api/v1/auth/register',
        'http://localhost:3001/api/auth/register',
        'http://localhost:3001/auth/register',
        'http://localhost:3001/register'
    ]
    
    working_endpoint = None
    
    for endpoint in endpoints_to_test:
        try:
            response = requests.post(endpoint, json={}, timeout=5)
            print(f"   📍 {endpoint}: {response.status_code}")
            
            if response.status_code in [422, 400]:  # Validation error means endpoint exists
                working_endpoint = endpoint
                print(f"   ✅ Registration endpoint found: {endpoint}")
                break
            elif response.status_code == 200:
                working_endpoint = endpoint
                print(f"   ✅ Registration endpoint working: {endpoint}")
                break
            elif response.status_code != 404:
                print(f"   ⚠️ Unexpected response: {response.text[:100]}")
                
        except Exception as e:
            print(f"   ❌ {endpoint}: {e}")
    
    # Test with valid registration data
    if working_endpoint:
        print(f"\n5. Testing Registration with Valid Data...")
        test_data = {
            "email": f"test_{int(time.time())}@agnoshin.com",
            "password": "TestPassword123!",
            "first_name": "Test",
            "last_name": "User",
            "organization_name": "Test Organization"
        }
        
        try:
            response = requests.post(working_endpoint, json=test_data, timeout=10)
            print(f"   ✅ Registration Test: {response.status_code}")
            
            if response.status_code in [200, 201]:
                data = response.json()
                print("   ✅ Registration successful!")
                if 'data' in data and 'tokens' in data['data']:
                    print("   ✅ Token generated successfully")
                    token = data['data']['tokens']['access_token']
                    
                    # Test protected endpoint
                    print("\n6. Testing Protected Endpoint...")
                    headers = {"Authorization": f"Bearer {token}"}
                    try:
                        profile_response = requests.get(
                            'http://localhost:3001/api/v1/users/profile',
                            headers=headers,
                            timeout=5
                        )
                        print(f"   ✅ Profile Access: {profile_response.status_code}")
                        if profile_response.status_code == 200:
                            profile_data = profile_response.json()
                            print(f"   ✅ User Profile Retrieved: {profile_data['data']['email']}")
                    except Exception as e:
                        print(f"   ❌ Profile Test Error: {e}")
                        
            elif response.status_code == 409:
                print("   ✅ User already exists (expected for repeated tests)")
            else:
                print(f"   ❌ Registration failed: {response.text}")
                
        except Exception as e:
            print(f"   ❌ Registration Test Error: {e}")
    else:
        print("\n❌ No working registration endpoint found!")
    
    # Check API Documentation
    print("\n7. API Documentation...")
    try:
        docs = requests.get('http://localhost:3001/docs', timeout=5)
        print(f"   ✅ API Docs: {docs.status_code}")
        if docs.status_code == 200:
            print("   ✅ Swagger documentation available")
            print("   📚 Visit: http://localhost:3001/docs")
    except Exception as e:
        print(f"   ❌ API Docs Error: {e}")
    
    # Frontend-Backend Integration Test
    print("\n8. Frontend-Backend Integration...")
    
    # Check if frontend can reach backend
    try:
        # This simulates what the frontend would do
        cors_test = requests.options('http://localhost:3001/api/v1/auth/register', 
                                   headers={
                                       'Origin': 'http://localhost:3000',
                                       'Access-Control-Request-Method': 'POST',
                                       'Access-Control-Request-Headers': 'Content-Type'
                                   },
                                   timeout=5)
        print(f"   ✅ CORS Preflight: {cors_test.status_code}")
        
        if cors_test.status_code == 200:
            print("   ✅ CORS configured correctly")
        else:
            print("   ⚠️ CORS may need configuration")
            
    except Exception as e:
        print(f"   ❌ CORS Test Error: {e}")
    
    # Summary
    print("\n" + "=" * 60)
    print("📊 APPLICATION STATUS SUMMARY")
    print("=" * 60)
    
    print("\n🌐 Access Points:")
    print("   • Frontend (React): http://localhost:3000")
    print("   • Backend API: http://localhost:3001")
    print("   • API Documentation: http://localhost:3001/docs")
    print("   • Monitoring Dashboard: http://localhost:3002/monitoring/dashboard/html")
    
    if working_endpoint:
        print(f"\n✅ Registration API Working:")
        print(f"   • Endpoint: {working_endpoint}")
        print(f"   • Method: POST")
        print(f"   • Expected Data: email, password, first_name, last_name")
    else:
        print(f"\n❌ Registration API Issues:")
        print(f"   • No working registration endpoint found")
        print(f"   • Check backend server logs")
        print(f"   • Verify API routes configuration")
    
    print(f"\n📋 Next Steps:")
    print(f"   1. Open http://localhost:3000 in browser")
    print(f"   2. Try registering a new user")
    print(f"   3. Check browser developer tools for any errors")
    print(f"   4. Verify network requests in browser DevTools")

if __name__ == "__main__":
    check_application_status()
