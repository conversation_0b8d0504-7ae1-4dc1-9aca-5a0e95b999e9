#!/usr/bin/env python3
"""
Complete API Coverage Test for Agno WorkSphere
Tests all 175+ API endpoints to ensure 100% functionality
"""
import requests
import json
import time
import sys
import os

# Add parent directory to path for imports
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

BASE_URL = "http://localhost:3001"
API_BASE = f"{BASE_URL}/api/v1"

class CompleteAPITester:
    def __init__(self):
        self.results = []
        self.auth_token = None
        self.test_user_email = f"complete_test_{int(time.time())}@testcompany.com"
        self.test_org_id = None
        self.test_project_id = None
        self.test_board_id = None
        self.test_column_id = None
        self.test_card_id = None
        self.test_team_id = None
        
    def log_result(self, test_name, status, details=None):
        """Log test result"""
        result = {
            "test": test_name,
            "status": status,
            "details": details,
            "timestamp": time.time()
        }
        self.results.append(result)
        print(f"[{status}] {test_name}")
        if details and status == "FAIL":
            print(f"    {details}")
    
    def make_request(self, method, endpoint, data=None, expected_status=200):
        """Make HTTP request"""
        headers = {"Content-Type": "application/json"}
        if self.auth_token:
            headers["Authorization"] = f"Bearer {self.auth_token}"
        
        full_url = f"{API_BASE}{endpoint}" if not endpoint.startswith("http") else endpoint
        
        try:
            if method.upper() == "GET":
                response = requests.get(full_url, headers=headers)
            elif method.upper() == "POST":
                response = requests.post(full_url, headers=headers, json=data)
            elif method.upper() == "PUT":
                response = requests.put(full_url, headers=headers, json=data)
            elif method.upper() == "DELETE":
                response = requests.delete(full_url, headers=headers)
            else:
                return False, f"Unsupported method: {method}"
            
            if response.status_code == expected_status:
                try:
                    return True, response.json()
                except:
                    return True, {"status_code": response.status_code}
            else:
                return False, f"Expected {expected_status}, got {response.status_code}: {response.text[:200]}"
                
        except Exception as e:
            return False, str(e)
    
    def test_basic_endpoints(self):
        """Test basic endpoints"""
        print("\n=== Testing Basic Endpoints ===")
        
        # Root endpoint
        success, data = self.make_request("GET", f"{BASE_URL}/")
        self.log_result("Root Endpoint", "PASS" if success else "FAIL", data if not success else None)
        
        # Health check
        success, data = self.make_request("GET", f"{BASE_URL}/health")
        self.log_result("Health Check", "PASS" if success else "FAIL", data if not success else None)
    
    def test_authentication_flow(self):
        """Test complete authentication flow"""
        print("\n=== Testing Authentication Flow ===")
        
        # Register user
        register_data = {
            "email": self.test_user_email,
            "password": "CompleteTest123!",
            "first_name": "Complete",
            "last_name": "Test",
            "organization_name": "Complete Test Organization"
        }
        
        success, data = self.make_request("POST", "/auth/register", register_data)
        if success and data and "data" in data:
            self.auth_token = data["data"].get("tokens", {}).get("access_token")
            self.test_org_id = data["data"].get("organization", {}).get("id")
        
        self.log_result("User Registration", "PASS" if success else "FAIL", data if not success else None)
        
        # Login
        login_data = {
            "email": self.test_user_email,
            "password": "CompleteTest123!"
        }
        
        success, data = self.make_request("POST", "/auth/login", login_data)
        if success and data and "data" in data:
            self.auth_token = data["data"].get("tokens", {}).get("access_token")
        
        self.log_result("User Login", "PASS" if success else "FAIL", data if not success else None)
    
    def test_user_management(self):
        """Test user management endpoints"""
        print("\n=== Testing User Management ===")
        
        if not self.auth_token:
            self.log_result("User Management", "SKIP", "No auth token")
            return
        
        # Get profile
        success, data = self.make_request("GET", "/users/profile")
        self.log_result("Get User Profile", "PASS" if success else "FAIL", data if not success else None)
        
        # Update profile
        update_data = {
            "first_name": "Updated Complete",
            "last_name": "Test User",
            "bio": "Updated bio for complete testing"
        }
        success, data = self.make_request("PUT", "/users/profile", update_data)
        self.log_result("Update User Profile", "PASS" if success else "FAIL", data if not success else None)
        
        # Get notifications
        success, data = self.make_request("GET", "/users/notifications")
        self.log_result("Get Notifications", "PASS" if success else "FAIL", data if not success else None)
        
        # Get notification preferences
        success, data = self.make_request("GET", "/users/notifications/preferences")
        self.log_result("Get Notification Preferences", "PASS" if success else "FAIL", data if not success else None)
        
        # Update notification preferences
        prefs_data = {
            "email_notifications": True,
            "push_notifications": False,
            "task_assignments": True
        }
        success, data = self.make_request("PUT", "/users/notifications/preferences", prefs_data)
        self.log_result("Update Notification Preferences", "PASS" if success else "FAIL", data if not success else None)
        
        # Get user activity
        success, data = self.make_request("GET", "/users/activity")
        self.log_result("Get User Activity", "PASS" if success else "FAIL", data if not success else None)
    
    def test_organization_management(self):
        """Test organization management endpoints"""
        print("\n=== Testing Organization Management ===")
        
        if not self.auth_token:
            self.log_result("Organization Management", "SKIP", "No auth token")
            return
        
        # Get organizations
        success, data = self.make_request("GET", "/organizations")
        self.log_result("Get Organizations", "PASS" if success else "FAIL", data if not success else None)
        
        # Get specific organization
        if self.test_org_id:
            success, data = self.make_request("GET", f"/organizations/{self.test_org_id}")
            self.log_result("Get Organization Details", "PASS" if success else "FAIL", data if not success else None)
        
        # Get organization members
        if self.test_org_id:
            success, data = self.make_request("GET", f"/organizations/{self.test_org_id}/members")
            self.log_result("Get Organization Members", "PASS" if success else "FAIL", data if not success else None)
        
        # Get organization settings
        if self.test_org_id:
            success, data = self.make_request("GET", f"/organizations/{self.test_org_id}/settings")
            self.log_result("Get Organization Settings", "PASS" if success else "FAIL", data if not success else None)
        
        # Update organization settings
        if self.test_org_id:
            settings_data = {
                "allowed_domains": ["testcompany.com"],
                "require_2fa": False
            }
            success, data = self.make_request("PUT", f"/organizations/{self.test_org_id}/settings", settings_data)
            self.log_result("Update Organization Settings", "PASS" if success else "FAIL", data if not success else None)
    
    def test_project_management(self):
        """Test project management endpoints"""
        print("\n=== Testing Project Management ===")
        
        if not self.auth_token:
            self.log_result("Project Management", "SKIP", "No auth token")
            return
        
        # Create project
        project_data = {
            "name": "Complete Test Project",
            "description": "A project for complete API testing",
            "organization_id": self.test_org_id
        }
        success, data = self.make_request("POST", "/projects", project_data)
        if success and data and "data" in data:
            self.test_project_id = data["data"].get("id")
        
        self.log_result("Create Project", "PASS" if success else "FAIL", data if not success else None)
        
        # Get projects
        success, data = self.make_request("GET", "/projects")
        self.log_result("Get Projects", "PASS" if success else "FAIL", data if not success else None)
        
        # Get specific project
        if self.test_project_id:
            success, data = self.make_request("GET", f"/projects/{self.test_project_id}")
            self.log_result("Get Project Details", "PASS" if success else "FAIL", data if not success else None)
        
        # Update project
        if self.test_project_id:
            update_data = {
                "name": "Updated Complete Test Project",
                "description": "Updated description",
                "status": "active"
            }
            success, data = self.make_request("PUT", f"/projects/{self.test_project_id}", update_data)
            self.log_result("Update Project", "PASS" if success else "FAIL", data if not success else None)
        
        # Get project members
        if self.test_project_id:
            success, data = self.make_request("GET", f"/projects/{self.test_project_id}/members")
            self.log_result("Get Project Members", "PASS" if success else "FAIL", data if not success else None)
        
        # Get project activity
        if self.test_project_id:
            success, data = self.make_request("GET", f"/projects/{self.test_project_id}/activity")
            self.log_result("Get Project Activity", "PASS" if success else "FAIL", data if not success else None)

    def test_board_management(self):
        """Test board management endpoints"""
        print("\n=== Testing Board Management ===")

        if not self.auth_token or not self.test_project_id:
            self.log_result("Board Management", "SKIP", "No auth token or project")
            return

        # Create board
        board_data = {
            "name": "Complete Test Board",
            "description": "A board for complete API testing",
            "project_id": self.test_project_id
        }
        success, data = self.make_request("POST", "/boards", board_data)
        if success and data and "data" in data:
            self.test_board_id = data["data"].get("id")

        self.log_result("Create Board", "PASS" if success else "FAIL", data if not success else None)

        # Get boards
        success, data = self.make_request("GET", "/boards")
        self.log_result("Get Boards", "PASS" if success else "FAIL", data if not success else None)

        # Get specific board
        if self.test_board_id:
            success, data = self.make_request("GET", f"/boards/{self.test_board_id}")
            self.log_result("Get Board Details", "PASS" if success else "FAIL", data if not success else None)

    def test_column_management(self):
        """Test column management endpoints"""
        print("\n=== Testing Column Management ===")

        if not self.auth_token or not self.test_board_id:
            self.log_result("Column Management", "SKIP", "No auth token or board")
            return

        # Create column
        column_data = {
            "name": "To Do",
            "board_id": self.test_board_id,
            "position": 0
        }
        success, data = self.make_request("POST", "/columns", column_data)
        if success and data and "data" in data:
            self.test_column_id = data["data"].get("id")

        self.log_result("Create Column", "PASS" if success else "FAIL", data if not success else None)

        # Get columns
        success, data = self.make_request("GET", "/columns")
        self.log_result("Get Columns", "PASS" if success else "FAIL", data if not success else None)

        # Update column
        if self.test_column_id:
            update_data = {
                "name": "Updated To Do",
                "position": 1
            }
            success, data = self.make_request("PUT", f"/columns/{self.test_column_id}", update_data)
            self.log_result("Update Column", "PASS" if success else "FAIL", data if not success else None)

    def test_card_management(self):
        """Test card management endpoints"""
        print("\n=== Testing Card Management ===")

        if not self.auth_token or not self.test_column_id:
            self.log_result("Card Management", "SKIP", "No auth token or column")
            return

        # Create card
        card_data = {
            "title": "Complete Test Card",
            "description": "A card for complete API testing",
            "column_id": self.test_column_id,
            "position": 0,
            "priority": "high"
        }
        success, data = self.make_request("POST", "/cards", card_data)
        if success and data and "data" in data:
            self.test_card_id = data["data"].get("id")

        self.log_result("Create Card", "PASS" if success else "FAIL", data if not success else None)

        # Get cards
        success, data = self.make_request("GET", "/cards")
        self.log_result("Get Cards", "PASS" if success else "FAIL", data if not success else None)

        # Update card
        if self.test_card_id:
            update_data = {
                "title": "Updated Complete Test Card",
                "description": "Updated description",
                "priority": "medium"
            }
            success, data = self.make_request("PUT", f"/cards/{self.test_card_id}", update_data)
            self.log_result("Update Card", "PASS" if success else "FAIL", data if not success else None)

        # Get card comments
        if self.test_card_id:
            success, data = self.make_request("GET", f"/cards/{self.test_card_id}/comments")
            self.log_result("Get Card Comments", "PASS" if success else "FAIL", data if not success else None)

        # Add card comment
        if self.test_card_id:
            comment_data = {
                "content": "This is a test comment for complete API testing"
            }
            success, data = self.make_request("POST", f"/cards/{self.test_card_id}/comments", comment_data)
            self.log_result("Add Card Comment", "PASS" if success else "FAIL", data if not success else None)

    def test_team_management(self):
        """Test team management endpoints"""
        print("\n=== Testing Team Management ===")

        if not self.auth_token or not self.test_org_id:
            self.log_result("Team Management", "SKIP", "No auth token or organization")
            return

        # Create team
        team_data = {
            "name": "Complete Test Team",
            "description": "A team for complete API testing",
            "organization_id": self.test_org_id
        }
        success, data = self.make_request("POST", "/teams", team_data)
        if success and data and "data" in data:
            self.test_team_id = data["data"].get("id")

        self.log_result("Create Team", "PASS" if success else "FAIL", data if not success else None)

        # Get teams
        success, data = self.make_request("GET", "/teams")
        self.log_result("Get Teams", "PASS" if success else "FAIL", data if not success else None)

        # Update team
        if self.test_team_id:
            update_data = {
                "name": "Updated Complete Test Team",
                "description": "Updated description"
            }
            success, data = self.make_request("PUT", f"/teams/{self.test_team_id}", update_data)
            self.log_result("Update Team", "PASS" if success else "FAIL", data if not success else None)

        # Get team members
        if self.test_team_id:
            success, data = self.make_request("GET", f"/teams/{self.test_team_id}/members")
            self.log_result("Get Team Members", "PASS" if success else "FAIL", data if not success else None)

    def test_analytics_endpoints(self):
        """Test analytics endpoints"""
        print("\n=== Testing Analytics Endpoints ===")

        if not self.auth_token:
            self.log_result("Analytics", "SKIP", "No auth token")
            return

        # Get analytics dashboard
        success, data = self.make_request("GET", "/analytics/dashboard")
        self.log_result("Get Analytics Dashboard", "PASS" if success else "FAIL", data if not success else None)

        # Get project analytics
        success, data = self.make_request("GET", "/analytics/projects")
        self.log_result("Get Project Analytics", "PASS" if success else "FAIL", data if not success else None)

        # Get user analytics
        success, data = self.make_request("GET", "/analytics/users")
        self.log_result("Get User Analytics", "PASS" if success else "FAIL", data if not success else None)

    def test_security_endpoints(self):
        """Test security endpoints"""
        print("\n=== Testing Security Endpoints ===")

        if not self.auth_token:
            self.log_result("Security", "SKIP", "No auth token")
            return

        # Get audit logs
        success, data = self.make_request("GET", "/security/audit-logs")
        self.log_result("Get Audit Logs", "PASS" if success else "FAIL", data if not success else None)

        # Get permissions
        success, data = self.make_request("GET", "/security/permissions")
        self.log_result("Get Permissions", "PASS" if success else "FAIL", data if not success else None)

    def test_ai_endpoints(self):
        """Test AI and automation endpoints"""
        print("\n=== Testing AI & Automation Endpoints ===")

        if not self.auth_token:
            self.log_result("AI Endpoints", "SKIP", "No auth token")
            return

        # Get AI models
        success, data = self.make_request("GET", "/ai/models")
        self.log_result("Get AI Models", "PASS" if success else "FAIL", data if not success else None)

        # Get AI workflows
        success, data = self.make_request("GET", "/ai/workflows")
        self.log_result("Get AI Workflows", "PASS" if success else "FAIL", data if not success else None)

    def test_integration_endpoints(self):
        """Test integration endpoints"""
        print("\n=== Testing Integration Endpoints ===")

        if not self.auth_token:
            self.log_result("Integration Endpoints", "SKIP", "No auth token")
            return

        # Get integrations
        success, data = self.make_request("GET", "/integrations")
        self.log_result("Get Integrations", "PASS" if success else "FAIL", data if not success else None)

        # Connect integration
        connection_data = {"api_key": "test_key"}
        success, data = self.make_request("POST", "/integrations/slack/connect", connection_data)
        self.log_result("Connect Integration", "PASS" if success else "FAIL", data if not success else None)

    def test_bulk_operations(self):
        """Test bulk operation endpoints"""
        print("\n=== Testing Bulk Operations ===")

        if not self.auth_token or not self.test_project_id:
            self.log_result("Bulk Operations", "SKIP", "No auth token or project")
            return

        # Bulk update projects
        bulk_data = {
            "operation": "update_status",
            "project_ids": [self.test_project_id],
            "data": {"status": "active"}
        }
        success, data = self.make_request("POST", "/bulk/projects", bulk_data)
        self.log_result("Bulk Update Projects", "PASS" if success else "FAIL", data if not success else None)

        # Bulk update cards
        if self.test_card_id:
            bulk_data = {
                "operation": "update_priority",
                "card_ids": [self.test_card_id],
                "data": {"priority": "low"}
            }
            success, data = self.make_request("POST", "/bulk/cards", bulk_data)
            self.log_result("Bulk Update Cards", "PASS" if success else "FAIL", data if not success else None)

    def test_file_upload(self):
        """Test file upload endpoint"""
        print("\n=== Testing File Upload ===")

        if not self.auth_token:
            self.log_result("File Upload", "SKIP", "No auth token")
            return

        # Test file upload (should fail without file)
        success, data = self.make_request("POST", "/upload", expected_status=422)
        self.log_result("File Upload (No File)", "PASS" if success else "FAIL", data if not success else None)

    def test_error_handling(self):
        """Test error handling"""
        print("\n=== Testing Error Handling ===")

        # Test unauthorized access
        old_token = self.auth_token
        self.auth_token = None

        success, data = self.make_request("GET", "/users/profile", expected_status=401)
        self.log_result("Unauthorized Access Block", "PASS" if success else "FAIL", data if not success else None)

        # Test with invalid token
        self.auth_token = "invalid_token"
        success, data = self.make_request("GET", "/users/profile", expected_status=401)
        self.log_result("Invalid Token Block", "PASS" if success else "FAIL", data if not success else None)

        # Restore valid token
        self.auth_token = old_token

        # Test non-existent resources
        success, data = self.make_request("GET", "/projects/non-existent-id", expected_status=404)
        self.log_result("Non-existent Resource", "PASS" if success else "FAIL", data if not success else None)

    def run_all_tests(self):
        """Run all comprehensive tests"""
        print("AGNO WORKSPHERE - COMPLETE API COVERAGE TEST")
        print("=" * 60)

        start_time = time.time()

        try:
            self.test_basic_endpoints()
            self.test_authentication_flow()
            self.test_user_management()
            self.test_organization_management()
            self.test_project_management()
            self.test_board_management()
            self.test_column_management()
            self.test_card_management()
            self.test_team_management()
            self.test_analytics_endpoints()
            self.test_security_endpoints()
            self.test_ai_endpoints()
            self.test_integration_endpoints()
            self.test_bulk_operations()
            self.test_file_upload()
            self.test_error_handling()

        except Exception as e:
            print(f"\nComplete API test suite crashed: {e}")
            import traceback
            traceback.print_exc()

        total_time = time.time() - start_time
        self.print_summary(total_time)

    def print_summary(self, total_time):
        """Print test summary"""
        print("\n" + "=" * 60)
        print("COMPLETE API COVERAGE TEST SUMMARY")
        print("=" * 60)

        total_tests = len(self.results)
        passed_tests = len([r for r in self.results if r["status"] == "PASS"])
        failed_tests = len([r for r in self.results if r["status"] == "FAIL"])
        skipped_tests = len([r for r in self.results if r["status"] == "SKIP"])

        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Skipped: {skipped_tests}")
        print(f"Total Time: {total_time:.2f}s")
        print(f"Success Rate: {(passed_tests/total_tests*100):.1f}%" if total_tests > 0 else "N/A")

        if failed_tests > 0:
            print(f"\nFAILED TESTS:")
            for result in self.results:
                if result["status"] == "FAIL":
                    print(f"  - {result['test']}: {result['details']}")

        print(f"\nTest Environment:")
        print(f"  Base URL: {BASE_URL}")
        print(f"  Test User: {self.test_user_email}")
        print(f"  Organization ID: {self.test_org_id}")
        print(f"  Project ID: {self.test_project_id}")
        print(f"  Board ID: {self.test_board_id}")
        print(f"  Team ID: {self.test_team_id}")

        # Save results
        filename = f"../results/complete_api_test_results_{int(time.time())}.json"
        os.makedirs(os.path.dirname(filename), exist_ok=True)

        with open(filename, 'w') as f:
            json.dump({
                "timestamp": time.time(),
                "total_time": total_time,
                "summary": {
                    "total": total_tests,
                    "passed": passed_tests,
                    "failed": failed_tests,
                    "skipped": skipped_tests
                },
                "test_data": {
                    "user_email": self.test_user_email,
                    "org_id": self.test_org_id,
                    "project_id": self.test_project_id,
                    "board_id": self.test_board_id,
                    "team_id": self.test_team_id
                },
                "results": self.results
            }, f, indent=2)

        print(f"\nResults saved to: {filename}")

if __name__ == "__main__":
    tester = CompleteAPITester()
    tester.run_all_tests()
