#!/usr/bin/env python3
"""
Comprehensive API Testing Suite for Agno WorkSphere
Tests all 175+ API endpoints with proper error handling, authentication, and edge cases
"""
import requests
import json
import time
import uuid
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import sys
import traceback

BASE_URL = "http://localhost:3001"
API_BASE = f"{BASE_URL}/api/v1"

@dataclass
class TestResult:
    endpoint: str
    method: str
    status: str  # PASS, FAIL, SKIP
    status_code: int
    response_time: float
    error_message: Optional[str] = None
    details: Optional[Dict] = None

class APITester:
    def __init__(self):
        self.results: List[TestResult] = []
        self.auth_token: Optional[str] = None
        self.test_user_email: str = f"test_{int(time.time())}@testcompany.com"
        self.test_org_id: Optional[str] = None
        self.test_project_id: Optional[str] = None
        self.test_board_id: Optional[str] = None
        self.test_team_id: Optional[str] = None
        
    def log_result(self, result: TestResult):
        """Log test result"""
        self.results.append(result)
        status_emoji = "✅" if result.status == "PASS" else "❌" if result.status == "FAIL" else "⏭️"
        print(f"{status_emoji} {result.method} {result.endpoint} - {result.status} ({result.response_time:.2f}s)")
        if result.error_message:
            print(f"   Error: {result.error_message}")
    
    def make_request(self, method: str, endpoint: str, data: Optional[Dict] = None, 
                    headers: Optional[Dict] = None, files: Optional[Dict] = None,
                    expected_status: int = 200) -> TestResult:
        """Make HTTP request and return test result"""
        start_time = time.time()
        full_url = f"{API_BASE}{endpoint}" if not endpoint.startswith("http") else endpoint
        
        # Default headers
        default_headers = {"Content-Type": "application/json"}
        if self.auth_token:
            default_headers["Authorization"] = f"Bearer {self.auth_token}"
        
        if headers:
            default_headers.update(headers)
        
        try:
            if method.upper() == "GET":
                response = requests.get(full_url, headers=default_headers, params=data)
            elif method.upper() == "POST":
                if files:
                    # Remove Content-Type for file uploads
                    default_headers.pop("Content-Type", None)
                    response = requests.post(full_url, headers=default_headers, data=data, files=files)
                else:
                    response = requests.post(full_url, headers=default_headers, json=data)
            elif method.upper() == "PUT":
                response = requests.put(full_url, headers=default_headers, json=data)
            elif method.upper() == "DELETE":
                response = requests.delete(full_url, headers=default_headers)
            elif method.upper() == "PATCH":
                response = requests.patch(full_url, headers=default_headers, json=data)
            else:
                raise ValueError(f"Unsupported method: {method}")
            
            response_time = time.time() - start_time
            
            # Check if status code matches expected
            if response.status_code == expected_status:
                status = "PASS"
                error_message = None
            else:
                status = "FAIL"
                error_message = f"Expected {expected_status}, got {response.status_code}: {response.text[:200]}"
            
            # Try to parse response as JSON
            try:
                response_data = response.json()
            except:
                response_data = {"raw_response": response.text[:500]}
            
            return TestResult(
                endpoint=endpoint,
                method=method.upper(),
                status=status,
                status_code=response.status_code,
                response_time=response_time,
                error_message=error_message,
                details=response_data
            )
            
        except Exception as e:
            response_time = time.time() - start_time
            return TestResult(
                endpoint=endpoint,
                method=method.upper(),
                status="FAIL",
                status_code=0,
                response_time=response_time,
                error_message=str(e),
                details={"exception": str(e)}
            )
    
    def test_health_endpoints(self):
        """Test health and basic endpoints"""
        print("\n🔍 Testing Health & Basic Endpoints")
        print("-" * 50)
        
        # Root endpoint
        result = self.make_request("GET", f"{BASE_URL}/")
        self.log_result(result)
        
        # Health check
        result = self.make_request("GET", f"{BASE_URL}/health")
        self.log_result(result)
        
        # API docs
        result = self.make_request("GET", f"{BASE_URL}/docs", expected_status=200)
        self.log_result(result)
    
    def test_authentication_endpoints(self):
        """Test authentication endpoints"""
        print("\n🔐 Testing Authentication Endpoints")
        print("-" * 50)
        
        # Test registration
        register_data = {
            "email": self.test_user_email,
            "password": "TestPassword123!",
            "first_name": "Test",
            "last_name": "User",
            "organization_name": "Test Organization"
        }
        
        result = self.make_request("POST", "/auth/register", register_data)
        self.log_result(result)
        
        if result.status == "PASS" and result.details:
            self.auth_token = result.details.get("data", {}).get("tokens", {}).get("access_token")
            self.test_org_id = result.details.get("data", {}).get("organization", {}).get("id")
        
        # Test login
        login_data = {
            "email": self.test_user_email,
            "password": "TestPassword123!"
        }
        
        result = self.make_request("POST", "/auth/login", login_data)
        self.log_result(result)
        
        if result.status == "PASS" and result.details:
            self.auth_token = result.details.get("data", {}).get("tokens", {}).get("access_token")
        
        # Test token refresh
        if self.auth_token:
            refresh_data = {"refresh_token": "dummy_refresh_token"}
            result = self.make_request("POST", "/auth/refresh", refresh_data, expected_status=401)  # Expected to fail with dummy token
            self.log_result(result)
        
        # Test logout
        if self.auth_token:
            result = self.make_request("POST", "/auth/logout")
            self.log_result(result)
        
        # Test password reset request
        reset_data = {"email": self.test_user_email}
        result = self.make_request("POST", "/auth/forgot-password", reset_data)
        self.log_result(result)
        
        # Test invalid login
        invalid_login = {
            "email": "<EMAIL>",
            "password": "wrongpassword"
        }
        result = self.make_request("POST", "/auth/login", invalid_login, expected_status=401)
        self.log_result(result)
    
    def test_user_endpoints(self):
        """Test user management endpoints"""
        print("\n👤 Testing User Management Endpoints")
        print("-" * 50)
        
        if not self.auth_token:
            print("⏭️ Skipping user tests - no auth token")
            return
        
        # Get user profile
        result = self.make_request("GET", "/users/profile")
        self.log_result(result)
        
        # Update user profile
        update_data = {
            "first_name": "Updated",
            "last_name": "User",
            "bio": "Updated bio for testing"
        }
        result = self.make_request("PUT", "/users/profile", update_data)
        self.log_result(result)
        
        # Get notification preferences
        result = self.make_request("GET", "/users/notifications/preferences")
        self.log_result(result)
        
        # Update notification preferences
        notif_data = {
            "email_notifications": True,
            "push_notifications": False,
            "task_assignments": True,
            "project_updates": True
        }
        result = self.make_request("PUT", "/users/notifications/preferences", notif_data)
        self.log_result(result)
        
        # Get user activity
        result = self.make_request("GET", "/users/activity")
        self.log_result(result)
        
        # Upload avatar (test file upload)
        # Note: This would need a real file for full testing
        result = self.make_request("POST", "/users/avatar", expected_status=422)  # Expected to fail without file
        self.log_result(result)
    
    def test_organization_endpoints(self):
        """Test organization management endpoints"""
        print("\n🏢 Testing Organization Management Endpoints")
        print("-" * 50)
        
        if not self.auth_token:
            print("⏭️ Skipping organization tests - no auth token")
            return
        
        # Get organizations
        result = self.make_request("GET", "/organizations")
        self.log_result(result)
        
        # Get specific organization
        if self.test_org_id:
            result = self.make_request("GET", f"/organizations/{self.test_org_id}")
            self.log_result(result)
        
        # Update organization
        if self.test_org_id:
            update_data = {
                "name": "Updated Test Organization",
                "description": "Updated description for testing"
            }
            result = self.make_request("PUT", f"/organizations/{self.test_org_id}", update_data)
            self.log_result(result)
        
        # Get organization members
        if self.test_org_id:
            result = self.make_request("GET", f"/organizations/{self.test_org_id}/members")
            self.log_result(result)
        
        # Invite member
        if self.test_org_id:
            invite_data = {
                "email": f"member_{int(time.time())}@testcompany.com",
                "role": "member"
            }
            result = self.make_request("POST", f"/organizations/{self.test_org_id}/invite", invite_data)
            self.log_result(result)
        
        # Get organization settings
        if self.test_org_id:
            result = self.make_request("GET", f"/organizations/{self.test_org_id}/settings")
            self.log_result(result)
        
        # Update organization settings
        if self.test_org_id:
            settings_data = {
                "allowed_domains": ["testcompany.com"],
                "require_2fa": False,
                "project_creation_restricted": False
            }
            result = self.make_request("PUT", f"/organizations/{self.test_org_id}/settings", settings_data)
            self.log_result(result)

    def test_project_endpoints(self):
        """Test project management endpoints"""
        print("\n📁 Testing Project Management Endpoints")
        print("-" * 50)

        if not self.auth_token:
            print("⏭️ Skipping project tests - no auth token")
            return

        # Create project
        project_data = {
            "name": "Test Project",
            "description": "A test project for API testing",
            "organization_id": self.test_org_id
        }
        result = self.make_request("POST", "/projects", project_data)
        self.log_result(result)

        if result.status == "PASS" and result.details:
            self.test_project_id = result.details.get("data", {}).get("id")

        # Get projects
        result = self.make_request("GET", "/projects")
        self.log_result(result)

        # Get specific project
        if self.test_project_id:
            result = self.make_request("GET", f"/projects/{self.test_project_id}")
            self.log_result(result)

        # Update project
        if self.test_project_id:
            update_data = {
                "name": "Updated Test Project",
                "description": "Updated description",
                "status": "active"
            }
            result = self.make_request("PUT", f"/projects/{self.test_project_id}", update_data)
            self.log_result(result)

        # Get project members
        if self.test_project_id:
            result = self.make_request("GET", f"/projects/{self.test_project_id}/members")
            self.log_result(result)

        # Add project member
        if self.test_project_id:
            member_data = {
                "user_email": self.test_user_email,
                "role": "member"
            }
            result = self.make_request("POST", f"/projects/{self.test_project_id}/members", member_data)
            self.log_result(result)

        # Get project activity
        if self.test_project_id:
            result = self.make_request("GET", f"/projects/{self.test_project_id}/activity")
            self.log_result(result)

    def test_board_endpoints(self):
        """Test board/kanban management endpoints"""
        print("\n📋 Testing Board/Kanban Management Endpoints")
        print("-" * 50)

        if not self.auth_token or not self.test_project_id:
            print("⏭️ Skipping board tests - no auth token or project")
            return

        # Create board
        board_data = {
            "name": "Test Board",
            "description": "A test board for API testing",
            "project_id": self.test_project_id
        }
        result = self.make_request("POST", "/boards", board_data)
        self.log_result(result)

        if result.status == "PASS" and result.details:
            self.test_board_id = result.details.get("data", {}).get("id")

        # Get boards
        result = self.make_request("GET", "/boards")
        self.log_result(result)

        # Get specific board
        if self.test_board_id:
            result = self.make_request("GET", f"/boards/{self.test_board_id}")
            self.log_result(result)

        # Update board
        if self.test_board_id:
            update_data = {
                "name": "Updated Test Board",
                "description": "Updated description"
            }
            result = self.make_request("PUT", f"/boards/{self.test_board_id}", update_data)
            self.log_result(result)

        # Get board columns
        if self.test_board_id:
            result = self.make_request("GET", f"/boards/{self.test_board_id}/columns")
            self.log_result(result)

    def test_column_endpoints(self):
        """Test column management endpoints"""
        print("\n📊 Testing Column Management Endpoints")
        print("-" * 50)

        if not self.auth_token or not self.test_board_id:
            print("⏭️ Skipping column tests - no auth token or board")
            return

        # Create column
        column_data = {
            "name": "To Do",
            "board_id": self.test_board_id,
            "position": 0
        }
        result = self.make_request("POST", "/columns", column_data)
        self.log_result(result)

        test_column_id = None
        if result.status == "PASS" and result.details:
            test_column_id = result.details.get("data", {}).get("id")

        # Get columns
        result = self.make_request("GET", "/columns")
        self.log_result(result)

        # Update column
        if test_column_id:
            update_data = {
                "name": "Updated To Do",
                "position": 1
            }
            result = self.make_request("PUT", f"/columns/{test_column_id}", update_data)
            self.log_result(result)

        # Delete column
        if test_column_id:
            result = self.make_request("DELETE", f"/columns/{test_column_id}")
            self.log_result(result)

    def test_card_endpoints(self):
        """Test card management endpoints"""
        print("\n🃏 Testing Card Management Endpoints")
        print("-" * 50)

        if not self.auth_token or not self.test_board_id:
            print("⏭️ Skipping card tests - no auth token or board")
            return

        # First create a column for the card
        column_data = {
            "name": "Test Column",
            "board_id": self.test_board_id,
            "position": 0
        }
        column_result = self.make_request("POST", "/columns", column_data)

        test_column_id = None
        if column_result.status == "PASS" and column_result.details:
            test_column_id = column_result.details.get("data", {}).get("id")

        if not test_column_id:
            print("⏭️ Skipping card tests - could not create column")
            return

        # Create card
        card_data = {
            "title": "Test Card",
            "description": "A test card for API testing",
            "column_id": test_column_id,
            "position": 0
        }
        result = self.make_request("POST", "/cards", card_data)
        self.log_result(result)

        test_card_id = None
        if result.status == "PASS" and result.details:
            test_card_id = result.details.get("data", {}).get("id")

        # Get cards
        result = self.make_request("GET", "/cards")
        self.log_result(result)

        # Update card
        if test_card_id:
            update_data = {
                "title": "Updated Test Card",
                "description": "Updated description",
                "priority": "high"
            }
            result = self.make_request("PUT", f"/cards/{test_card_id}", update_data)
            self.log_result(result)

        # Get card comments
        if test_card_id:
            result = self.make_request("GET", f"/cards/{test_card_id}/comments")
            self.log_result(result)

        # Add card comment
        if test_card_id:
            comment_data = {
                "content": "This is a test comment"
            }
            result = self.make_request("POST", f"/cards/{test_card_id}/comments", comment_data)
            self.log_result(result)

        # Move card
        if test_card_id:
            move_data = {
                "column_id": test_column_id,
                "position": 1
            }
            result = self.make_request("PUT", f"/cards/{test_card_id}/move", move_data)
            self.log_result(result)

    def test_team_endpoints(self):
        """Test team management endpoints"""
        print("\n👥 Testing Team Management Endpoints")
        print("-" * 50)

        if not self.auth_token or not self.test_org_id:
            print("⏭️ Skipping team tests - no auth token or organization")
            return

        # Create team
        team_data = {
            "name": "Test Team",
            "description": "A test team for API testing",
            "organization_id": self.test_org_id
        }
        result = self.make_request("POST", "/teams", team_data)
        self.log_result(result)

        if result.status == "PASS" and result.details:
            self.test_team_id = result.details.get("data", {}).get("id")

        # Get teams
        result = self.make_request("GET", "/teams")
        self.log_result(result)

        # Update team
        if self.test_team_id:
            update_data = {
                "name": "Updated Test Team",
                "description": "Updated description"
            }
            result = self.make_request("PUT", f"/teams/{self.test_team_id}", update_data)
            self.log_result(result)

        # Get team members
        if self.test_team_id:
            result = self.make_request("GET", f"/teams/{self.test_team_id}/members")
            self.log_result(result)

        # Add team member
        if self.test_team_id:
            member_data = {
                "user_email": self.test_user_email,
                "role": "member"
            }
            result = self.make_request("POST", f"/teams/{self.test_team_id}/members", member_data)
            self.log_result(result)

    def test_advanced_endpoints(self):
        """Test advanced features endpoints"""
        print("\n🚀 Testing Advanced Features Endpoints")
        print("-" * 50)

        if not self.auth_token:
            print("⏭️ Skipping advanced tests - no auth token")
            return

        # Analytics endpoints
        result = self.make_request("GET", "/analytics/dashboard")
        self.log_result(result)

        result = self.make_request("GET", "/analytics/projects")
        self.log_result(result)

        result = self.make_request("GET", "/analytics/users")
        self.log_result(result)

        # Bulk operations
        if self.test_project_id:
            bulk_data = {
                "operation": "update_status",
                "project_ids": [self.test_project_id],
                "data": {"status": "active"}
            }
            result = self.make_request("POST", "/bulk/projects", bulk_data)
            self.log_result(result)

        # Security endpoints
        result = self.make_request("GET", "/security/audit-logs")
        self.log_result(result)

        result = self.make_request("GET", "/security/permissions")
        self.log_result(result)

        # AI automation endpoints
        result = self.make_request("GET", "/ai/models")
        self.log_result(result)

        result = self.make_request("GET", "/ai/workflows")
        self.log_result(result)

        # Integration endpoints
        result = self.make_request("GET", "/integrations")
        self.log_result(result)

        # File upload endpoint
        result = self.make_request("POST", "/upload", expected_status=422)  # Expected to fail without file
        self.log_result(result)

    def test_error_handling(self):
        """Test error handling and edge cases"""
        print("\n⚠️ Testing Error Handling & Edge Cases")
        print("-" * 50)

        # Test unauthorized access
        old_token = self.auth_token
        self.auth_token = None

        result = self.make_request("GET", "/users/profile", expected_status=401)
        self.log_result(result)

        # Test with invalid token
        self.auth_token = "invalid_token"
        result = self.make_request("GET", "/users/profile", expected_status=401)
        self.log_result(result)

        # Restore valid token
        self.auth_token = old_token

        # Test non-existent resources
        result = self.make_request("GET", "/projects/non-existent-id", expected_status=404)
        self.log_result(result)

        result = self.make_request("GET", "/organizations/non-existent-id", expected_status=404)
        self.log_result(result)

        # Test invalid data
        invalid_data = {
            "email": "invalid-email",
            "password": "123"  # Too short
        }
        result = self.make_request("POST", "/auth/register", invalid_data, expected_status=422)
        self.log_result(result)

        # Test method not allowed
        result = self.make_request("DELETE", "/auth/login", expected_status=405)
        self.log_result(result)

    def run_all_tests(self):
        """Run all test suites"""
        print("🚀 Starting Comprehensive API Testing Suite")
        print("=" * 60)

        start_time = time.time()

        try:
            self.test_health_endpoints()
            self.test_authentication_endpoints()
            self.test_user_endpoints()
            self.test_organization_endpoints()
            self.test_project_endpoints()
            self.test_board_endpoints()
            self.test_column_endpoints()
            self.test_card_endpoints()
            self.test_team_endpoints()
            self.test_advanced_endpoints()
            self.test_error_handling()

        except Exception as e:
            print(f"\n💥 Test suite crashed: {e}")
            traceback.print_exc()

        total_time = time.time() - start_time
        self.print_summary(total_time)

    def print_summary(self, total_time: float):
        """Print test summary"""
        print("\n" + "=" * 60)
        print("📊 TEST SUMMARY")
        print("=" * 60)

        total_tests = len(self.results)
        passed_tests = len([r for r in self.results if r.status == "PASS"])
        failed_tests = len([r for r in self.results if r.status == "FAIL"])
        skipped_tests = len([r for r in self.results if r.status == "SKIP"])

        print(f"Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"⏭️ Skipped: {skipped_tests}")
        print(f"⏱️ Total Time: {total_time:.2f}s")
        print(f"📈 Success Rate: {(passed_tests/total_tests*100):.1f}%" if total_tests > 0 else "N/A")

        if failed_tests > 0:
            print(f"\n❌ FAILED TESTS:")
            for result in self.results:
                if result.status == "FAIL":
                    print(f"   {result.method} {result.endpoint} - {result.error_message}")

        print(f"\n🌐 Test Environment:")
        print(f"   Base URL: {BASE_URL}")
        print(f"   Test User: {self.test_user_email}")
        print(f"   Organization ID: {self.test_org_id}")
        print(f"   Project ID: {self.test_project_id}")

        # Save detailed results to file
        self.save_results_to_file()

    def save_results_to_file(self):
        """Save test results to JSON file"""
        results_data = {
            "timestamp": time.time(),
            "summary": {
                "total": len(self.results),
                "passed": len([r for r in self.results if r.status == "PASS"]),
                "failed": len([r for r in self.results if r.status == "FAIL"]),
                "skipped": len([r for r in self.results if r.status == "SKIP"])
            },
            "test_data": {
                "user_email": self.test_user_email,
                "org_id": self.test_org_id,
                "project_id": self.test_project_id
            },
            "results": [
                {
                    "endpoint": r.endpoint,
                    "method": r.method,
                    "status": r.status,
                    "status_code": r.status_code,
                    "response_time": r.response_time,
                    "error_message": r.error_message
                }
                for r in self.results
            ]
        }

        filename = f"api_test_results_{int(time.time())}.json"
        with open(filename, 'w') as f:
            json.dump(results_data, f, indent=2)

        print(f"\n💾 Detailed results saved to: {filename}")

if __name__ == "__main__":
    tester = APITester()
    tester.run_all_tests()
