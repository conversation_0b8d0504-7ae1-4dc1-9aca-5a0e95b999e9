#!/usr/bin/env python3
"""
Simple test to run the backend without database
"""
import sys
import os

# Add the backend directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_basic_imports():
    """Test basic imports"""
    try:
        print("Testing basic imports...")
        
        # Test FastAPI
        from fastapi import FastAPI
        print("✓ FastAPI imported")
        
        # Test Pydantic
        from pydantic import BaseModel
        print("✓ Pydantic imported")
        
        # Test our config
        from app.config import settings
        print("✓ Config imported")
        
        # Test basic security
        from app.core.security import hash_password, verify_password
        print("✓ Security utilities imported")
        
        return True
        
    except Exception as e:
        print(f"❌ Import error: {e}")
        return False


def test_password_hashing():
    """Test password hashing"""
    try:
        print("\nTesting password hashing...")
        
        from app.core.security import hash_password, verify_password
        
        password = "TestPassword123!"
        hashed = hash_password(password)
        
        if verify_password(password, hashed):
            print("✓ Password hashing works")
            return True
        else:
            print("❌ Password verification failed")
            return False
            
    except Exception as e:
        print(f"❌ Password hashing error: {e}")
        return False


def test_fastapi_app():
    """Test FastAPI app creation"""
    try:
        print("\nTesting FastAPI app...")
        
        from fastapi import FastAPI
        
        app = FastAPI(title="Test App")
        
        @app.get("/")
        def root():
            return {"message": "Hello World"}
        
        print("✓ FastAPI app created successfully")
        return True
        
    except Exception as e:
        print(f"❌ FastAPI app error: {e}")
        return False


def main():
    """Run all tests"""
    print("🚀 Simple Backend Test\n")
    
    tests = [
        test_basic_imports,
        test_password_hashing,
        test_fastapi_app
    ]
    
    results = []
    for test in tests:
        result = test()
        results.append(result)
    
    print(f"\n📊 Test Results:")
    print(f"✅ Passed: {sum(results)}")
    print(f"❌ Failed: {len(results) - sum(results)}")
    
    if all(results):
        print("\n🎉 All basic tests passed!")
        return 0
    else:
        print("\n💥 Some tests failed.")
        return 1


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
