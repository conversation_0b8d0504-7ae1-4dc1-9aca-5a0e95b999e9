import React, { useState, useEffect } from 'react';
import Button from '../ui/Button';
import Input from '../ui/Input';
import Select from '../ui/Select';
import Icon from '../AppIcon';
import ProjectExportButton from '../ui/ProjectExportButton';
import openaiService from '../../utils/openaiService';

const EnhancedCreateAIProjectModal = ({ isOpen, onClose, onCreateAIProject, organizationId, organizationName }) => {
  const [formData, setFormData] = useState({
    name: '',
    projectType: 'general',
    teamSize: 5,
    teamExperience: 'intermediate'
  });
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [currentStep, setCurrentStep] = useState(1);
  const [projectPreview, setProjectPreview] = useState(null);
  const [isGeneratingPreview, setIsGeneratingPreview] = useState(false);

  // AI-related state
  const [isGeneratingAI, setIsGeneratingAI] = useState(false);
  const [aiSuggestions, setAiSuggestions] = useState(null);
  const [aiError, setAiError] = useState('');
  const [hasAutoGenerated, setHasAutoGenerated] = useState(false);

  const projectTypes = [
    { value: 'general', label: 'General Project', description: 'Standard project with flexible workflow', icon: 'Folder', color: 'bg-gray-500' },
    { value: 'web_application', label: 'Web Application', description: 'Full-stack web development project', icon: 'Globe', color: 'bg-blue-500' },
    { value: 'mobile_app', label: 'Mobile App', description: 'iOS/Android mobile application', icon: 'Smartphone', color: 'bg-green-500' },
    { value: 'ecommerce_platform', label: 'E-commerce Platform', description: 'Online marketplace with payment processing', icon: 'ShoppingCart', color: 'bg-purple-500' },
    { value: 'saas_application', label: 'SaaS Application', description: 'Multi-tenant cloud-based software', icon: 'Cloud', color: 'bg-indigo-500' },
    { value: 'devops_infrastructure', label: 'DevOps/Infrastructure', description: 'CI/CD pipelines and infrastructure automation', icon: 'Server', color: 'bg-orange-500' }
  ];

  const teamSizeOptions = [
    { value: 2, label: '2 people (Small team)' },
    { value: 3, label: '3 people (Small team)' },
    { value: 5, label: '5 people (Optimal team)' },
    { value: 8, label: '8 people (Large team)' },
    { value: 12, label: '12+ people (Enterprise team)' }
  ];

  const experienceOptions = [
    { value: 'junior', label: 'Junior (0-2 years)' },
    { value: 'intermediate', label: 'Intermediate (2-5 years)' },
    { value: 'senior', label: 'Senior (5+ years)' },
    { value: 'expert', label: 'Expert (10+ years)' }
  ];

  // Auto-generate AI suggestions when project name changes
  useEffect(() => {
    if (formData.name && formData.name.length > 3 && !hasAutoGenerated) {
      handleAutoGenerateAI();
    }
  }, [formData.name, formData.projectType, formData.teamSize, formData.teamExperience]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      openaiService.cancelAutoGeneration();
    };
  }, []);

  // Auto-generate AI suggestions
  const handleAutoGenerateAI = async () => {
    if (isGeneratingAI || !formData.name || formData.name.length <= 3) return;

    setIsGeneratingAI(true);
    setAiError('');

    try {
      const suggestions = await openaiService.autoGenerateFromName(
        formData.name,
        formData.projectType,
        formData.teamSize,
        formData.teamExperience
      );

      if (suggestions) {
        setAiSuggestions(suggestions);
        setHasAutoGenerated(true);

        // Auto-apply some suggestions to form
        setFormData(prev => ({
          ...prev,
          projectType: suggestions.recommendedType || prev.projectType,
          teamSize: suggestions.recommendedTeamSize || prev.teamSize,
          teamExperience: suggestions.recommendedExperience || prev.teamExperience
        }));
      }
    } catch (error) {
      console.error('Auto AI generation failed:', error);
      setAiError(error.message);
    } finally {
      setIsGeneratingAI(false);
    }
  };

  // Manual AI generation trigger
  const handleManualGenerateAI = async () => {
    if (isGeneratingAI || !formData.name || formData.name.length <= 3) return;

    setIsGeneratingAI(true);
    setAiError('');
    setHasAutoGenerated(false);

    try {
      const suggestions = await openaiService.generateProjectSuggestions(
        formData.name,
        formData.projectType,
        formData.teamSize,
        formData.teamExperience
      );

      setAiSuggestions(suggestions);
      setHasAutoGenerated(true);

      // Auto-apply suggestions to form
      setFormData(prev => ({
        ...prev,
        projectType: suggestions.recommendedType || prev.projectType,
        teamSize: suggestions.recommendedTeamSize || prev.teamSize,
        teamExperience: suggestions.recommendedExperience || prev.teamExperience
      }));
    } catch (error) {
      console.error('Manual AI generation failed:', error);
      setAiError(error.message);
    } finally {
      setIsGeneratingAI(false);
    }
  };

  const handleInputChange = (field, value) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (error) setError('');

    // Reset auto-generation flag when user manually changes key fields
    if (['projectType', 'teamSize', 'teamExperience'].includes(field)) {
      setHasAutoGenerated(false);
    }
  };

  const generatePreview = async () => {
    if (!formData.name.trim()) {
      setError('Project name is required for preview');
      return;
    }

    setIsGeneratingPreview(true);
    try {
      // If we have AI suggestions, use them; otherwise generate new ones
      let suggestions = aiSuggestions;

      if (!suggestions) {
        setIsGeneratingAI(true);
        try {
          suggestions = await openaiService.generateProjectSuggestions(
            formData.name,
            formData.projectType,
            formData.teamSize,
            formData.teamExperience
          );
          setAiSuggestions(suggestions);
        } catch (aiError) {
          console.error('AI generation failed, using fallback:', aiError);
          // Fallback to original logic if AI fails
          suggestions = null;
        } finally {
          setIsGeneratingAI(false);
        }
      }

      const selectedType = projectTypes.find(type => type.value === formData.projectType);

      // Use AI suggestions if available, otherwise fallback to original logic
      if (suggestions) {
        setProjectPreview({
          name: formData.name,
          type: suggestions.type || selectedType,
          description: suggestions.description,
          estimatedDuration: suggestions.estimatedDuration,
          estimatedTasks: suggestions.estimatedTasks,
          phases: suggestions.phases,
          teamRecommendations: suggestions.teamRecommendations,
          technologies: suggestions.technologies,
          tasks: suggestions.tasks,
          risks: suggestions.risks,
          aiGenerated: true
        });
      } else {
        // Fallback to original preview generation
        setProjectPreview({
          name: formData.name,
          type: selectedType,
          estimatedDuration: getEstimatedDuration(formData.projectType, formData.teamSize, formData.teamExperience),
          estimatedTasks: getEstimatedTaskCount(formData.projectType),
          phases: getProjectPhases(formData.projectType),
          teamRecommendations: getTeamRecommendations(formData.teamSize, formData.teamExperience),
          technologies: getTechnologies(formData.projectType),
          aiGenerated: false
        });
      }
      
      setCurrentStep(2);
    } catch (err) {
      setError('Failed to generate preview');
    } finally {
      setIsGeneratingPreview(false);
    }
  };

  const getEstimatedDuration = (type, teamSize, experience) => {
    const baseDurations = {
      'web_application': 50, 'mobile_app': 57, 'ecommerce_platform': 87,
      'saas_application': 97, 'devops_infrastructure': 52, 'general': 43
    };
    
    let duration = baseDurations[type] || 43;
    const experienceMultipliers = { junior: 1.3, intermediate: 1.0, senior: 0.8, expert: 0.7 };
    duration *= experienceMultipliers[experience] || 1.0;
    
    if (teamSize <= 2) duration *= 1.2;
    else if (teamSize >= 8) duration *= 0.9;
    
    return Math.round(duration);
  };

  const getEstimatedTaskCount = (type) => {
    const taskCounts = {
      'web_application': 25, 'mobile_app': 28, 'ecommerce_platform': 45,
      'saas_application': 52, 'devops_infrastructure': 22, 'general': 15
    };
    return taskCounts[type] || 15;
  };

  const getProjectPhases = (type) => {
    const phases = {
      'web_application': ['Planning & Analysis', 'Design & Architecture', 'Development', 'Testing & QA', 'Deployment & Launch'],
      'ecommerce_platform': ['Market Research & Planning', 'Core Platform Development', 'Payment & Security Integration', 'Advanced Features & Optimization', 'Testing & Launch']
    };
    return phases[type] || ['Initiation & Planning', 'Execution Phase 1', 'Execution Phase 2', 'Finalization & Review', 'Closure & Handover'];
  };

  const getTeamRecommendations = (size, experience) => {
    const recommendations = [];
    if (size <= 2) recommendations.push('Consider adding more team members for complex phases');
    if (size >= 8) recommendations.push('Break into smaller sub-teams to reduce coordination overhead');
    if (experience === 'junior') recommendations.push('Assign senior mentor for guidance');
    if (experience === 'expert') recommendations.push('Leverage team expertise for innovation opportunities');
    return recommendations;
  };

  const getTechnologies = (type) => {
    const tech = {
      'web_application': ['React/Vue.js', 'Node.js/Python', 'PostgreSQL', 'Docker', 'AWS/Azure'],
      'ecommerce_platform': ['React/Next.js', 'Node.js/Express', 'PostgreSQL', 'Stripe/PayPal', 'Redis', 'Docker']
    };
    return tech[type] || ['Modern Framework', 'Backend Technology', 'Database', 'Cloud Platform'];
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    
    if (currentStep === 1) {
      generatePreview();
      return;
    }

    setIsLoading(true);
    setError('');

    try {
      const projectData = {
        name: formData.name.trim(),
        project_type: formData.projectType,
        team_size: formData.teamSize,
        team_experience: formData.teamExperience,
        organization_id: organizationId,
        // Include AI-generated data for Kanban board population
        ai_generated: projectPreview?.aiGenerated || false,
        description: projectPreview?.description || '',
        estimated_duration: projectPreview?.estimatedDuration || 0,
        phases: projectPreview?.phases || [],
        technologies: projectPreview?.technologies || [],
        risks: projectPreview?.risks || [],
        team_recommendations: projectPreview?.teamRecommendations || [],
        // Tasks for Kanban board - will be placed in "To-do" column
        generated_tasks: projectPreview?.tasks || []
      };

      await onCreateAIProject(projectData);
      handleClose();
    } catch (err) {
      setError(err.message || 'Failed to create AI project');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    if (!isLoading && !isGeneratingPreview) {
      setFormData({ name: '', projectType: 'general', teamSize: 5, teamExperience: 'intermediate' });
      setError('');
      setCurrentStep(1);
      setProjectPreview(null);
      onClose();
    }
  };

  const handleBack = () => {
    setCurrentStep(1);
    setProjectPreview(null);
    setError('');
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div className="bg-surface rounded-2xl shadow-enterprise border border-border w-full max-w-6xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-8 border-b border-border bg-gradient-to-r from-purple-50 to-blue-50">
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 bg-gradient-to-br from-purple-500 via-purple-600 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
              <Icon name="Sparkles" size={24} className="text-white" />
            </div>
            <div>
              <h2 className="text-2xl font-bold text-foreground">Create AI Project</h2>
              <p className="text-muted-foreground">Let AI generate your complete project structure</p>
            </div>
          </div>
          <Button
            variant="ghost"
            size="icon"
            onClick={handleClose}
            disabled={isLoading || isGeneratingPreview}
            className="text-muted-foreground hover:text-foreground hover:bg-background/80 rounded-xl"
          >
            <Icon name="X" size={20} />
          </Button>
        </div>

        {/* Step Indicator */}
        <div className="px-8 py-6 bg-background/50">
          <div className="flex items-center justify-center space-x-6">
            <div className={`flex items-center transition-all duration-300 ${currentStep >= 1 ? 'text-primary' : 'text-muted-foreground'}`}>
              <div className={`w-10 h-10 rounded-full flex items-center justify-center font-semibold transition-all duration-300 ${
                currentStep >= 1
                  ? 'bg-primary text-primary-foreground shadow-lg scale-110'
                  : 'bg-muted text-muted-foreground'
              }`}>
                1
              </div>
              <span className="ml-3 text-sm font-medium">Configure</span>
            </div>
            <div className={`w-20 h-1 rounded-full transition-all duration-300 ${
              currentStep >= 2 ? 'bg-primary' : 'bg-muted'
            }`}></div>
            <div className={`flex items-center transition-all duration-300 ${currentStep >= 2 ? 'text-primary' : 'text-muted-foreground'}`}>
              <div className={`w-10 h-10 rounded-full flex items-center justify-center font-semibold transition-all duration-300 ${
                currentStep >= 2
                  ? 'bg-primary text-primary-foreground shadow-lg scale-110'
                  : 'bg-muted text-muted-foreground'
              }`}>
                2
              </div>
              <span className="ml-3 text-sm font-medium">Preview & Create</span>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-8 overflow-y-auto max-h-[calc(90vh-200px)]">
          {currentStep === 1 ? (
            <ConfigurationStep
              formData={formData}
              projectTypes={projectTypes}
              teamSizeOptions={teamSizeOptions}
              experienceOptions={experienceOptions}
              onInputChange={handleInputChange}
              onSubmit={handleSubmit}
              isGeneratingPreview={isGeneratingPreview}
              error={error}
              organizationName={organizationName}
              // AI-related props
              isGeneratingAI={isGeneratingAI}
              aiSuggestions={aiSuggestions}
              aiError={aiError}
              handleManualGenerateAI={handleManualGenerateAI}
            />
          ) : (
            <PreviewStep
              projectPreview={projectPreview}
              onSubmit={handleSubmit}
              onBack={handleBack}
              isLoading={isLoading}
              error={error}
            />
          )}
        </div>
      </div>
    </div>
  );
};

// Configuration Step Component
const ConfigurationStep = ({
  formData,
  projectTypes,
  teamSizeOptions,
  experienceOptions,
  onInputChange,
  onSubmit,
  isGeneratingPreview,
  error,
  organizationName,
  // AI-related props
  isGeneratingAI,
  aiSuggestions,
  aiError,
  handleManualGenerateAI
}) => (
  <form onSubmit={onSubmit} className="space-y-8">
    {/* Organization Info */}
    <div className="bg-gradient-to-r from-blue-50 to-purple-50 border border-blue-200 rounded-xl p-6">
      <div className="flex items-center gap-3">
        <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
          <Icon name="Building2" size={16} className="text-white" />
        </div>
        <div>
          <p className="text-sm text-muted-foreground">Creating in</p>
          <p className="font-semibold text-foreground">{organizationName}</p>
        </div>
      </div>
    </div>

    {/* Error Message */}
    {error && (
      <div className="bg-destructive/10 border border-destructive/20 rounded-xl p-4">
        <div className="flex items-center gap-3 text-destructive">
          <Icon name="AlertCircle" size={20} />
          <span className="font-medium">{error}</span>
        </div>
      </div>
    )}

    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
      {/* Left Column */}
      <div className="space-y-8">
        {/* Project Name */}
        <div className="space-y-3">
          <label className="block text-sm font-semibold text-foreground">Project Name *</label>
          <div className="relative">
            <Input
              type="text"
              value={formData.name}
              onChange={(e) => onInputChange('name', e.target.value)}
              placeholder="Enter your project name..."
              disabled={isGeneratingPreview || isGeneratingAI}
              className="w-full h-12 text-base pr-12"
              required
            />
            {isGeneratingAI && (
              <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                <Icon name="Loader2" size={20} className="animate-spin text-primary" />
              </div>
            )}
          </div>

          {/* AI Generation Status */}
          {isGeneratingAI && (
            <div className="flex items-center gap-2 text-sm text-primary">
              <Icon name="Sparkles" size={16} className="animate-pulse" />
              <span>AI is generating suggestions...</span>
            </div>
          )}

          {aiError && (
            <div className="flex items-center gap-2 text-sm text-red-600 bg-red-50 p-3 rounded-lg">
              <Icon name="AlertCircle" size={16} />
              <span>{aiError}</span>
            </div>
          )}

          {aiSuggestions && !isGeneratingAI && (
            <div className="bg-gradient-to-r from-emerald-50 to-green-50 border border-emerald-200 rounded-lg p-4">
              <div className="flex items-center gap-2 text-emerald-800 mb-2">
                <Icon name="CheckCircle" size={16} />
                <span className="text-sm font-medium">AI suggestions applied!</span>
              </div>
              <p className="text-xs text-emerald-700">
                Project type, team size, and experience level have been optimized based on your project name.
              </p>
            </div>
          )}

          {/* Manual AI Generation Button */}
          {formData.name && formData.name.length > 3 && (
            <div className="flex justify-center">
              <Button
                type="button"
                variant="outline"
                onClick={handleManualGenerateAI}
                disabled={isGeneratingAI || isGeneratingPreview}
                iconName={isGeneratingAI ? "Loader2" : "Sparkles"}
                iconPosition="left"
                className={`border-2 border-primary/20 hover:border-primary/40 text-primary hover:bg-primary/5 ${
                  isGeneratingAI ? "animate-pulse" : ""
                }`}
              >
                {isGeneratingAI ? 'Generating...' : 'Generate AI Suggestions'}
              </Button>
            </div>
          )}
        </div>

        {/* Team Configuration */}
        <div className="bg-card border border-border rounded-xl p-6 space-y-6">
          <div className="flex items-center gap-3">
            <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
              <Icon name="Users" size={16} className="text-white" />
            </div>
            <h3 className="text-lg font-semibold text-foreground">Team Configuration</h3>
          </div>

          <div className="space-y-4">
            <div className="space-y-3">
              <label className="block text-sm font-medium text-foreground">Team Size</label>
              <Select
                value={formData.teamSize}
                onChange={(value) => onInputChange('teamSize', value)}
                options={teamSizeOptions}
                disabled={isGeneratingPreview}
                className="w-full h-12"
              />
            </div>

            <div className="space-y-3">
              <label className="block text-sm font-medium text-foreground">Team Experience Level</label>
              <Select
                value={formData.teamExperience}
                onChange={(value) => onInputChange('teamExperience', value)}
                options={experienceOptions}
                disabled={isGeneratingPreview}
                className="w-full h-12"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Right Column - Project Type Selector */}
      <div className="space-y-6">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
            <Icon name="Layers" size={16} className="text-white" />
          </div>
          <h3 className="text-lg font-semibold text-foreground">Project Type</h3>
        </div>
        <div className="grid grid-cols-1 gap-4 max-h-96 overflow-y-auto pr-2">
          {projectTypes.map((type) => (
            <div
              key={type.value}
              className={`group p-5 border-2 rounded-xl cursor-pointer transition-all duration-200 hover:shadow-lg ${
                formData.projectType === type.value
                  ? 'border-primary bg-primary/5 shadow-md scale-[1.02]'
                  : 'border-border hover:border-primary/50 hover:bg-primary/5'
              }`}
              onClick={() => onInputChange('projectType', type.value)}
            >
              <div className="flex items-start gap-4">
                <div className={`w-12 h-12 ${type.color} rounded-xl flex items-center justify-center flex-shrink-0 shadow-sm group-hover:shadow-md transition-shadow`}>
                  <Icon name={type.icon} size={24} className="text-white" />
                </div>
                <div className="flex-1 min-w-0">
                  <h4 className="font-semibold text-foreground text-base">{type.label}</h4>
                  <p className="text-sm text-muted-foreground mt-1 leading-relaxed">{type.description}</p>
                </div>
                {formData.projectType === type.value && (
                  <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center flex-shrink-0">
                    <Icon name="Check" size={14} className="text-primary-foreground" />
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>

    {/* Actions */}
    <div className="flex items-center justify-end gap-4 pt-8 border-t border-border">
      <Button
        type="submit"
        size="lg"
        disabled={isGeneratingPreview || !formData.name.trim()}
        iconName={isGeneratingPreview ? "Loader2" : "Sparkles"}
        iconPosition="left"
        className={`bg-gradient-to-r from-purple-500 to-blue-600 hover:from-purple-600 hover:to-blue-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200 ${
          isGeneratingPreview ? "animate-pulse" : ""
        }`}
      >
        {isGeneratingPreview ? 'Generating Preview...' : 'Generate AI Preview'}
      </Button>
    </div>
  </form>
);

// Preview Step Component
const PreviewStep = ({ projectPreview, onSubmit, onBack, isLoading, error }) => (
  <div className="space-y-8">
    {/* Error Message */}
    {error && (
      <div className="bg-destructive/10 border border-destructive/20 rounded-xl p-4">
        <div className="flex items-center gap-3 text-destructive">
          <Icon name="AlertCircle" size={20} />
          <span className="font-medium">{error}</span>
        </div>
      </div>
    )}

    {/* Project Overview */}
    <div className="bg-gradient-to-br from-purple-50 via-blue-50 to-indigo-50 border border-purple-200 rounded-2xl p-8 shadow-lg">
      <div className="flex items-center gap-4 mb-6">
        <div className={`w-16 h-16 ${projectPreview?.type?.color || 'bg-gray-500'} rounded-2xl flex items-center justify-center shadow-lg`}>
          <Icon name={projectPreview?.type?.icon || 'Folder'} size={32} className="text-white" />
        </div>
        <div className="flex-1">
          <div className="flex items-center gap-3 mb-2">
            <h2 className="text-2xl font-bold text-foreground">{projectPreview?.name}</h2>
            {projectPreview?.aiGenerated && (
              <div className="flex items-center gap-1 px-2 py-1 bg-emerald-100 text-emerald-800 text-xs font-medium rounded-full">
                <Icon name="Sparkles" size={12} />
                <span>AI Generated</span>
              </div>
            )}
          </div>
          <p className="text-lg text-muted-foreground">{projectPreview?.type?.label}</p>
          {projectPreview?.description && (
            <p className="text-sm text-muted-foreground mt-2 line-clamp-2">{projectPreview.description}</p>
          )}
        </div>
      </div>

      <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
        <div className="text-center bg-white/60 rounded-xl p-4">
          <div className="text-3xl font-bold text-primary">{projectPreview?.estimatedDuration}</div>
          <div className="text-sm font-medium text-muted-foreground">Days</div>
        </div>
        <div className="text-center bg-white/60 rounded-xl p-4">
          <div className="text-3xl font-bold text-primary">{projectPreview?.estimatedTasks}</div>
          <div className="text-sm font-medium text-muted-foreground">Tasks</div>
        </div>
        <div className="text-center bg-white/60 rounded-xl p-4">
          <div className="text-3xl font-bold text-primary">{projectPreview?.phases?.length || 0}</div>
          <div className="text-sm font-medium text-muted-foreground">Phases</div>
        </div>
        <div className="text-center bg-white/60 rounded-xl p-4">
          <div className="text-3xl font-bold text-primary">{projectPreview?.technologies?.length || 0}</div>
          <div className="text-sm font-medium text-muted-foreground">Technologies</div>
        </div>
      </div>
    </div>

    <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
      {/* Project Phases */}
      <div className="bg-card border border-border rounded-xl p-6 space-y-6">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-blue-500 rounded-lg flex items-center justify-center">
            <Icon name="GitBranch" size={16} className="text-white" />
          </div>
          <h3 className="text-lg font-semibold text-foreground">Project Phases</h3>
        </div>
        <div className="space-y-3">
          {projectPreview?.phases?.map((phase, index) => (
            <div key={index} className="flex items-center gap-4 p-4 bg-background rounded-lg border border-border hover:shadow-sm transition-shadow">
              <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-blue-600 text-white rounded-full flex items-center justify-center text-sm font-bold shadow-sm">
                {index + 1}
              </div>
              <span className="font-medium text-foreground">{phase}</span>
            </div>
          ))}
        </div>
      </div>

      {/* Technologies */}
      <div className="bg-card border border-border rounded-xl p-6 space-y-6">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-green-500 rounded-lg flex items-center justify-center">
            <Icon name="Code" size={16} className="text-white" />
          </div>
          <h3 className="text-lg font-semibold text-foreground">Recommended Technologies</h3>
        </div>
        <div className="flex flex-wrap gap-3">
          {projectPreview?.technologies?.map((tech, index) => (
            <span key={index} className="px-4 py-2 bg-primary/10 text-primary rounded-lg text-sm font-medium border border-primary/20 hover:bg-primary/20 transition-colors">
              {tech}
            </span>
          ))}
        </div>

        {/* Team Recommendations */}
        {projectPreview?.teamRecommendations?.length > 0 && (
          <div className="pt-4 border-t border-border">
            <div className="flex items-center gap-3 mb-4">
              <div className="w-6 h-6 bg-orange-500 rounded-md flex items-center justify-center">
                <Icon name="Users" size={14} className="text-white" />
              </div>
              <h4 className="font-semibold text-foreground">Team Recommendations</h4>
            </div>
            <div className="space-y-3">
              {projectPreview.teamRecommendations.map((rec, index) => (
                <div key={index} className="flex items-start gap-3 text-sm text-muted-foreground">
                  <Icon name="CheckCircle" size={16} className="text-green-500 mt-0.5 flex-shrink-0" />
                  <span>{rec}</span>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>

    {/* AI-Generated Tasks Preview */}
    {projectPreview?.tasks && projectPreview.tasks.length > 0 && (
      <div className="bg-card border border-border rounded-xl p-6 space-y-6">
        <div className="flex items-center gap-3">
          <div className="w-8 h-8 bg-purple-500 rounded-lg flex items-center justify-center">
            <Icon name="CheckSquare" size={16} className="text-white" />
          </div>
          <h3 className="text-lg font-semibold text-foreground">AI-Generated Tasks</h3>
          <span className="px-2 py-1 bg-emerald-100 text-emerald-800 text-xs font-medium rounded-full">
            {projectPreview.tasks.length} tasks ready for Kanban board
          </span>
        </div>

        <div className="space-y-4 max-h-64 overflow-y-auto">
          {projectPreview.tasks.slice(0, 8).map((task, index) => (
            <div key={index} className="flex items-start gap-3 p-3 bg-background rounded-lg border border-border">
              <div className={`w-2 h-2 rounded-full mt-2 flex-shrink-0 ${
                task.priority === 'high' ? 'bg-red-500' :
                task.priority === 'medium' ? 'bg-yellow-500' : 'bg-green-500'
              }`} />
              <div className="flex-1 min-w-0">
                <div className="flex items-center gap-2 mb-1">
                  <h4 className="font-medium text-foreground text-sm">{task.title}</h4>
                  <span className={`px-2 py-0.5 text-xs font-medium rounded ${
                    task.priority === 'high' ? 'bg-red-100 text-red-800' :
                    task.priority === 'medium' ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'
                  }`}>
                    {task.priority}
                  </span>
                </div>
                <p className="text-xs text-muted-foreground mb-1">{task.description}</p>
                <div className="flex items-center gap-3 text-xs text-muted-foreground">
                  <span>Phase: {task.phase}</span>
                  <span>•</span>
                  <span>{task.estimatedHours}h estimated</span>
                </div>
              </div>
            </div>
          ))}
          {projectPreview.tasks.length > 8 && (
            <div className="text-center text-sm text-muted-foreground">
              +{projectPreview.tasks.length - 8} more tasks will be added to your Kanban board
            </div>
          )}
        </div>
      </div>
    )}

    {/* AI Features Info */}
    <div className="bg-gradient-to-br from-emerald-50 via-green-50 to-teal-50 border border-emerald-200 rounded-xl p-6 shadow-sm">
      <div className="flex items-center gap-3 mb-4">
        <div className="w-8 h-8 bg-emerald-500 rounded-lg flex items-center justify-center">
          <Icon name="Sparkles" size={16} className="text-white" />
        </div>
        <h3 className="text-lg font-semibold text-emerald-900">What AI will generate for you:</h3>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="flex items-center gap-3 text-sm text-emerald-800">
          <Icon name="CheckCircle" size={16} className="text-emerald-600 flex-shrink-0" />
          <span>Comprehensive project description and objectives</span>
        </div>
        <div className="flex items-center gap-3 text-sm text-emerald-800">
          <Icon name="CheckCircle" size={16} className="text-emerald-600 flex-shrink-0" />
          <span>Detailed workflow with phases and milestones</span>
        </div>
        <div className="flex items-center gap-3 text-sm text-emerald-800">
          <Icon name="CheckCircle" size={16} className="text-emerald-600 flex-shrink-0" />
          <span>Complete task breakdown with priorities and estimates</span>
        </div>
        <div className="flex items-center gap-3 text-sm text-emerald-800">
          <Icon name="CheckCircle" size={16} className="text-emerald-600 flex-shrink-0" />
          <span>Kanban board with organized task categories</span>
        </div>
        <div className="flex items-center gap-3 text-sm text-emerald-800">
          <Icon name="CheckCircle" size={16} className="text-emerald-600 flex-shrink-0" />
          <span>Project timeline and resource recommendations</span>
        </div>
        <div className="flex items-center gap-3 text-sm text-emerald-800">
          <Icon name="CheckCircle" size={16} className="text-emerald-600 flex-shrink-0" />
          <span>Risk assessment and mitigation strategies</span>
        </div>
      </div>
    </div>

    <div className="flex items-center justify-between pt-8 border-t border-border">
      <div className="flex items-center gap-4">
        <Button
          variant="outline"
          size="lg"
          onClick={onBack}
          disabled={isLoading}
          iconName="ArrowLeft"
          iconPosition="left"
          className="border-2"
        >
          Back to Configuration
        </Button>
        <ProjectExportButton projectData={projectPreview} />
      </div>
      <Button
        size="lg"
        onClick={onSubmit}
        disabled={isLoading}
        iconName={isLoading ? "Loader2" : "Rocket"}
        iconPosition="left"
        className={`bg-gradient-to-r from-purple-500 to-blue-600 hover:from-purple-600 hover:to-blue-700 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-200 ${
          isLoading ? "animate-pulse" : ""
        }`}
      >
        {isLoading ? 'Creating Project...' : 'Create AI Project'}
      </Button>
    </div>
  </div>
);

export default EnhancedCreateAIProjectModal;
