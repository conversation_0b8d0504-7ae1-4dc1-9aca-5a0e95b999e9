import React, { createContext, useContext, useState, useEffect } from 'react';
import apiService from '../utils/apiService';
import authService from '../utils/authService';
import { useAuth } from './AuthContext';

const ProjectContext = createContext();

export const useProject = () => {
  const context = useContext(ProjectContext);
  if (!context) {
    throw new Error('useProject must be used within a ProjectProvider');
  }
  return context;
};

export const ProjectProvider = ({ children }) => {
  const { isAuthenticated, loading: authLoading } = useAuth();
  const [currentProject, setCurrentProject] = useState(null);
  const [projects, setProjects] = useState([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);

  // Load all projects for the current organization
  const loadProjects = async () => {
    try {
      // Check authentication before making API calls
      if (!isAuthenticated) {
        console.log('Cannot load projects - user not authenticated');
        return;
      }

      setLoading(true);
      setError(null);

      const organizationId = authService.getOrganizationId();
      if (!organizationId) {
        throw new Error('No organization found');
      }

      const response = await apiService.projects.getAll(organizationId);
      if (response && response.data) {
        setProjects(response.data);

        // If no current project is set, set the first one as current
        if (!currentProject && response.data.length > 0) {
          setCurrentProject(response.data[0]);
          localStorage.setItem('currentProjectId', response.data[0].id);
          localStorage.setItem('currentProject', JSON.stringify(response.data[0]));
        }
      }
    } catch (error) {
      console.error('Failed to load projects:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Switch to a different project
  const switchProject = async (projectId) => {
    try {
      setLoading(true);
      setError(null);

      // Find project in current projects list
      let project = projects.find(p => p.id === projectId);
      
      // If not found in current list, fetch from API
      if (!project) {
        project = await apiService.projects.getById(projectId);
      }

      if (project) {
        setCurrentProject(project);
        localStorage.setItem('currentProjectId', project.id);
        localStorage.setItem('currentProject', JSON.stringify(project));
        
        // Emit project change event for other components
        window.dispatchEvent(new CustomEvent('projectChanged', {
          detail: { project, projectId }
        }));
      } else {
        throw new Error('Project not found');
      }
    } catch (error) {
      console.error('Failed to switch project:', error);
      setError(error.message);
    } finally {
      setLoading(false);
    }
  };

  // Update current project data
  const updateCurrentProject = (updatedProject) => {
    setCurrentProject(updatedProject);
    localStorage.setItem('currentProject', JSON.stringify(updatedProject));
    
    // Update in projects list as well
    setProjects(prev => prev.map(p => 
      p.id === updatedProject.id ? updatedProject : p
    ));

    // Emit project update event
    window.dispatchEvent(new CustomEvent('projectUpdated', {
      detail: { project: updatedProject }
    }));
  };

  // Add a new project to the list
  const addProject = (newProject) => {
    setProjects(prev => [...prev, newProject]);
    
    // Emit project added event
    window.dispatchEvent(new CustomEvent('projectAdded', {
      detail: { project: newProject }
    }));
  };

  // Remove a project from the list
  const removeProject = (projectId) => {
    setProjects(prev => prev.filter(p => p.id !== projectId));
    
    // If the removed project was the current one, switch to another
    if (currentProject && currentProject.id === projectId) {
      const remainingProjects = projects.filter(p => p.id !== projectId);
      if (remainingProjects.length > 0) {
        switchProject(remainingProjects[0].id);
      } else {
        setCurrentProject(null);
        localStorage.removeItem('currentProjectId');
        localStorage.removeItem('currentProject');
      }
    }

    // Emit project removed event
    window.dispatchEvent(new CustomEvent('projectRemoved', {
      detail: { projectId }
    }));
  };

  // Initialize project context
  useEffect(() => {
    const initializeProject = async () => {
      // Don't load projects if user is not authenticated or auth is still loading
      if (authLoading || !isAuthenticated) {
        console.log('Skipping project initialization - user not authenticated');
        return;
      }

      // Try to get project from localStorage first
      const storedProjectId = localStorage.getItem('currentProjectId');
      const storedProject = localStorage.getItem('currentProject');

      if (storedProject && storedProjectId) {
        try {
          const project = JSON.parse(storedProject);
          setCurrentProject(project);
        } catch (e) {
          console.warn('Failed to parse stored project:', e);
        }
      }

      // Load all projects
      await loadProjects();
    };

    initializeProject();
  }, [isAuthenticated, authLoading]); // Re-run when auth state changes

  // Listen for external project updates
  useEffect(() => {
    const handleStorageChange = (e) => {
      if (e.key === 'currentProject' && e.newValue) {
        try {
          const project = JSON.parse(e.newValue);
          setCurrentProject(project);
        } catch (error) {
          console.warn('Failed to parse project from storage:', error);
        }
      }
    };

    window.addEventListener('storage', handleStorageChange);
    return () => window.removeEventListener('storage', handleStorageChange);
  }, []);

  const value = {
    currentProject,
    projects,
    loading,
    error,
    switchProject,
    updateCurrentProject,
    addProject,
    removeProject,
    loadProjects
  };

  return (
    <ProjectContext.Provider value={value}>
      {children}
    </ProjectContext.Provider>
  );
};

export default ProjectContext;
