#!/usr/bin/env python3
"""
Agno WorkSphere - Comprehensive 8-Phase Testing Execution
Executes the complete testing checklist following the production readiness validation
"""

import asyncio
import asyncpg
import aiohttp
import json
import time
import sys
import os
import subprocess
from datetime import datetime
from typing import Dict, List, Any, Optional
from pathlib import Path

class ComprehensiveTestingExecution:
    def __init__(self):
        self.results = {
            "timestamp": datetime.now().isoformat(),
            "phases": {},
            "overall_score": 0,
            "critical_issues": [],
            "recommendations": [],
            "test_summary": {}
        }
        
        # Configuration
        self.config = {
            "database_url": "postgresql://postgres:postgres@localhost:5432/agno_worksphere",
            "api_base_url": "http://localhost:3001",
            "frontend_url": "http://localhost:3000",
            "backend_dir": Path("backend"),
            "frontend_dir": Path("agnoworksphere")
        }
        
    async def execute_all_phases(self):
        """Execute all 8 phases of comprehensive testing"""
        print("🚀 AGNO WORKSPHERE - COMPREHENSIVE 8-PHASE TESTING")
        print("=" * 70)
        print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 70)
        
        phases = [
            ("Phase 1", "PostgreSQL Migration & Setup", self.phase1_postgresql_setup),
            ("Phase 2", "Application Testing & API Validation", self.phase2_application_testing),
            ("Phase 3", "Live User Testing & Workflow Validation", self.phase3_user_testing),
            ("Phase 4", "Role-Based Access Control Testing", self.phase4_rbac_testing),
            ("Phase 5", "Multi-Tenant & Security Testing", self.phase5_multitenant_testing),
            ("Phase 6", "Performance & Load Testing", self.phase6_performance_testing),
            ("Phase 7", "Error Handling & Edge Cases", self.phase7_error_handling),
            ("Phase 8", "Mobile & Browser Testing", self.phase8_browser_testing)
        ]
        
        total_score = 0
        max_score = 0
        
        for phase_id, phase_name, phase_func in phases:
            print(f"\n📋 {phase_id}: {phase_name}")
            print("-" * 50)
            
            try:
                phase_result = await phase_func()
                self.results["phases"][phase_id] = phase_result
                
                score = phase_result.get("score", 0)
                max_phase_score = phase_result.get("max_score", 100)
                
                total_score += score
                max_score += max_phase_score
                
                status = "✅ PASS" if score >= 80 else "⚠️ PARTIAL" if score >= 60 else "❌ FAIL"
                print(f"\n{status} - Score: {score:.1f}/{max_phase_score} ({score/max_phase_score*100:.1f}%)")
                
                if phase_result.get("critical_issues"):
                    print("🚨 Critical Issues:")
                    for issue in phase_result["critical_issues"]:
                        print(f"  • {issue}")
                
            except Exception as e:
                print(f"❌ ERROR: {str(e)}")
                self.results["phases"][phase_id] = {
                    "error": str(e),
                    "score": 0,
                    "max_score": 100,
                    "critical_issues": [f"Phase execution failed: {str(e)}"]
                }
                max_score += 100
        
        # Calculate overall score
        self.results["overall_score"] = (total_score / max_score * 100) if max_score > 0 else 0
        
        # Generate final report
        await self.generate_final_report()
        
    async def phase1_postgresql_setup(self) -> Dict[str, Any]:
        """Phase 1: PostgreSQL Migration & Setup"""
        print("🔧 Testing PostgreSQL Migration & Database Setup...")
        
        results = {
            "tests": [],
            "score": 0,
            "max_score": 100,
            "critical_issues": [],
            "details": {}
        }
        
        # Step 1.1: Check PostgreSQL Installation
        print("  📦 Step 1.1: Checking PostgreSQL Installation...")
        try:
            # Test if PostgreSQL is accessible
            conn = await asyncpg.connect(
                "postgresql://postgres:postgres@localhost:5432/postgres"
            )
            pg_version = await conn.fetchval("SELECT version()")
            await conn.close()
            
            results["tests"].append({
                "name": "PostgreSQL Installation",
                "status": "PASS",
                "details": f"Version: {pg_version[:50]}..."
            })
            results["details"]["postgresql_version"] = pg_version
            print("    ✅ PostgreSQL accessible")
            
        except Exception as e:
            results["tests"].append({
                "name": "PostgreSQL Installation",
                "status": "FAIL",
                "details": str(e)
            })
            results["critical_issues"].append(f"PostgreSQL not accessible: {str(e)}")
            print(f"    ❌ PostgreSQL connection failed: {str(e)}")
            return results
        
        # Step 1.2: Test Database Connection
        print("  🔗 Step 1.2: Testing Database Connection...")
        try:
            conn = await asyncpg.connect(self.config["database_url"])
            await conn.close()
            
            results["tests"].append({
                "name": "Database Connection",
                "status": "PASS",
                "details": "Successfully connected to agno_worksphere database"
            })
            print("    ✅ Database connection successful")
            
        except Exception as e:
            # Try to create database if it doesn't exist
            try:
                print("    🔄 Database doesn't exist, attempting to create...")
                conn = await asyncpg.connect(
                    "postgresql://postgres:postgres@localhost:5432/postgres"
                )
                await conn.execute("CREATE DATABASE agno_worksphere")
                await conn.close()
                
                # Test connection again
                conn = await asyncpg.connect(self.config["database_url"])
                await conn.close()
                
                results["tests"].append({
                    "name": "Database Creation & Connection",
                    "status": "PASS",
                    "details": "Database created and connection successful"
                })
                print("    ✅ Database created and connection successful")
                
            except Exception as create_error:
                results["tests"].append({
                    "name": "Database Connection",
                    "status": "FAIL",
                    "details": str(create_error)
                })
                results["critical_issues"].append(f"Database connection failed: {str(create_error)}")
                print(f"    ❌ Database creation failed: {str(create_error)}")
                return results
        
        # Step 1.3: Check Database Schema
        print("  📊 Step 1.3: Checking Database Schema...")
        try:
            conn = await asyncpg.connect(self.config["database_url"])
            
            # Check if tables exist
            required_tables = [
                "users", "organizations", "organization_members", 
                "projects", "boards", "columns", "cards"
            ]
            
            existing_tables = []
            for table in required_tables:
                exists = await conn.fetchval(
                    "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = $1)",
                    table
                )
                if exists:
                    existing_tables.append(table)
            
            if len(existing_tables) == len(required_tables):
                results["tests"].append({
                    "name": "Database Schema",
                    "status": "PASS",
                    "details": f"All {len(required_tables)} required tables exist"
                })
                print(f"    ✅ All {len(required_tables)} required tables exist")
            else:
                missing_tables = set(required_tables) - set(existing_tables)
                results["tests"].append({
                    "name": "Database Schema",
                    "status": "PARTIAL",
                    "details": f"Missing tables: {', '.join(missing_tables)}"
                })
                print(f"    ⚠️ Missing tables: {', '.join(missing_tables)}")
                
                # Try to run migrations
                print("    🔄 Attempting to run Alembic migrations...")
                try:
                    migration_result = subprocess.run(
                        ["python", "-m", "alembic", "upgrade", "head"],
                        cwd=self.config["backend_dir"],
                        capture_output=True,
                        text=True,
                        timeout=60
                    )
                    
                    if migration_result.returncode == 0:
                        print("    ✅ Alembic migrations completed successfully")
                        
                        # Re-check tables
                        existing_tables_after = []
                        for table in required_tables:
                            exists = await conn.fetchval(
                                "SELECT EXISTS (SELECT FROM information_schema.tables WHERE table_name = $1)",
                                table
                            )
                            if exists:
                                existing_tables_after.append(table)
                        
                        if len(existing_tables_after) == len(required_tables):
                            results["tests"][-1]["status"] = "PASS"
                            results["tests"][-1]["details"] = "All tables created via migration"
                            print("    ✅ All tables now exist after migration")
                        else:
                            missing_after = set(required_tables) - set(existing_tables_after)
                            results["critical_issues"].append(f"Tables still missing after migration: {', '.join(missing_after)}")
                    else:
                        print(f"    ❌ Migration failed: {migration_result.stderr}")
                        results["critical_issues"].append(f"Migration failed: {migration_result.stderr}")
                        
                except subprocess.TimeoutExpired:
                    print("    ❌ Migration timed out")
                    results["critical_issues"].append("Migration process timed out")
                except Exception as migration_error:
                    print(f"    ❌ Migration error: {str(migration_error)}")
                    results["critical_issues"].append(f"Migration error: {str(migration_error)}")
            
            await conn.close()
            
        except Exception as e:
            results["tests"].append({
                "name": "Database Schema",
                "status": "FAIL",
                "details": str(e)
            })
            results["critical_issues"].append(f"Schema check failed: {str(e)}")
            print(f"    ❌ Schema check failed: {str(e)}")
        
        # Step 1.4: Test Data Integrity
        print("  🔒 Step 1.4: Testing Data Integrity...")
        try:
            conn = await asyncpg.connect(self.config["database_url"])
            
            # Check foreign key constraints
            fk_count = await conn.fetchval("""
                SELECT COUNT(*) FROM information_schema.table_constraints 
                WHERE constraint_type = 'FOREIGN KEY'
            """)
            
            results["tests"].append({
                "name": "Foreign Key Constraints",
                "status": "PASS" if fk_count > 0 else "WARN",
                "details": f"Found {fk_count} foreign key constraints"
            })
            
            if fk_count > 0:
                print(f"    ✅ Found {fk_count} foreign key constraints")
            else:
                print(f"    ⚠️ No foreign key constraints found")
            
            # Test UUID generation
            test_uuid = await conn.fetchval("SELECT gen_random_uuid()")
            if test_uuid:
                results["tests"].append({
                    "name": "UUID Generation",
                    "status": "PASS",
                    "details": f"Generated UUID: {test_uuid}"
                })
                print("    ✅ UUID generation working")
            else:
                results["tests"].append({
                    "name": "UUID Generation",
                    "status": "FAIL",
                    "details": "UUID generation failed"
                })
                results["critical_issues"].append("UUID generation not working")
                print("    ❌ UUID generation failed")
            
            await conn.close()
            
        except Exception as e:
            results["tests"].append({
                "name": "Data Integrity",
                "status": "FAIL",
                "details": str(e)
            })
            results["critical_issues"].append(f"Data integrity check failed: {str(e)}")
            print(f"    ❌ Data integrity check failed: {str(e)}")
        
        # Calculate score
        passed_tests = sum(1 for test in results["tests"] if test["status"] == "PASS")
        partial_tests = sum(1 for test in results["tests"] if test["status"] == "PARTIAL")
        total_tests = len(results["tests"])
        
        if total_tests > 0:
            results["score"] = ((passed_tests + partial_tests * 0.5) / total_tests * 100)
        else:
            results["score"] = 0
        
        return results
    
    async def phase2_application_testing(self) -> Dict[str, Any]:
        """Phase 2: Application Testing & API Validation"""
        print("🔧 Testing Application & API Validation...")
        
        results = {
            "tests": [],
            "score": 0,
            "max_score": 100,
            "critical_issues": [],
            "details": {}
        }
        
        # Step 2.1: Start Backend Server (check if running)
        print("  🚀 Step 2.1: Checking Backend Server...")
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.config['api_base_url']}/health", timeout=5) as response:
                    if response.status == 200:
                        results["tests"].append({
                            "name": "Backend Server Health",
                            "status": "PASS",
                            "details": "Server responding to health checks"
                        })
                        print("    ✅ Backend server is running and healthy")
                    else:
                        results["tests"].append({
                            "name": "Backend Server Health",
                            "status": "FAIL",
                            "details": f"Health check returned status {response.status}"
                        })
                        results["critical_issues"].append(f"Backend health check failed with status {response.status}")
                        print(f"    ❌ Backend health check failed: {response.status}")
                        
        except Exception as e:
            results["tests"].append({
                "name": "Backend Server Health",
                "status": "FAIL",
                "details": str(e)
            })
            results["critical_issues"].append(f"Backend server not accessible: {str(e)}")
            print(f"    ❌ Backend server not accessible: {str(e)}")
            print("    💡 Try starting the backend with: cd backend && python run.py")
        
        # Step 2.2: Test API Documentation
        print("  📚 Step 2.2: Testing API Documentation...")
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(f"{self.config['api_base_url']}/docs", timeout=5) as response:
                    if response.status == 200:
                        results["tests"].append({
                            "name": "API Documentation",
                            "status": "PASS",
                            "details": "Swagger/OpenAPI docs accessible"
                        })
                        print("    ✅ API documentation accessible")
                    else:
                        results["tests"].append({
                            "name": "API Documentation",
                            "status": "FAIL",
                            "details": f"Docs returned status {response.status}"
                        })
                        print(f"    ❌ API docs not accessible: {response.status}")
                        
        except Exception as e:
            results["tests"].append({
                "name": "API Documentation",
                "status": "FAIL",
                "details": str(e)
            })
            print(f"    ❌ API docs check failed: {str(e)}")
        
        # Step 2.3: Test Core API Endpoints
        print("  🔌 Step 2.3: Testing Core API Endpoints...")
        
        api_endpoints = [
            ("POST", "/api/auth/register", "User Registration"),
            ("POST", "/api/auth/login", "User Login"),
            ("GET", "/api/users/profile", "User Profile"),
            ("GET", "/api/organizations", "Organizations List"),
            ("POST", "/api/organizations", "Organization Creation"),
            ("GET", "/api/projects", "Projects List"),
            ("POST", "/api/projects", "Project Creation")
        ]
        
        async with aiohttp.ClientSession() as session:
            for method, endpoint, description in api_endpoints:
                try:
                    url = f"{self.config['api_base_url']}{endpoint}"
                    
                    if method == "GET":
                        # GET requests should return 401 without auth
                        async with session.get(url, timeout=5) as response:
                            expected_status = 401 if "profile" in endpoint or "organizations" in endpoint or "projects" in endpoint else 200
                            status = "PASS" if response.status == expected_status else "PARTIAL"
                    else:
                        # POST requests should return 422 for missing data
                        async with session.request(method, url, timeout=5) as response:
                            status = "PASS" if response.status in [422, 400, 401] else "PARTIAL"
                    
                    results["tests"].append({
                        "name": f"{description} ({method})",
                        "status": status,
                        "details": f"Endpoint responded with status {response.status}"
                    })
                    
                    if status == "PASS":
                        print(f"    ✅ {description}: {response.status}")
                    else:
                        print(f"    ⚠️ {description}: {response.status}")
                        
                except Exception as e:
                    results["tests"].append({
                        "name": f"{description} ({method})",
                        "status": "FAIL",
                        "details": str(e)
                    })
                    print(f"    ❌ {description}: {str(e)}")
        
        # Calculate score
        passed_tests = sum(1 for test in results["tests"] if test["status"] == "PASS")
        partial_tests = sum(1 for test in results["tests"] if test["status"] == "PARTIAL")
        total_tests = len(results["tests"])
        
        if total_tests > 0:
            results["score"] = ((passed_tests + partial_tests * 0.7) / total_tests * 100)
        else:
            results["score"] = 0
        
        return results

    async def phase3_user_testing(self) -> Dict[str, Any]:
        """Phase 3: Live User Testing & Workflow Validation"""
        print("🔧 Testing User Workflows & Live Testing...")

        results = {
            "tests": [],
            "score": 0,
            "max_score": 100,
            "critical_issues": [],
            "details": {}
        }

        # Step 3.1: Test Frontend Accessibility
        print("  🌐 Step 3.1: Testing Frontend Accessibility...")
        try:
            async with aiohttp.ClientSession() as session:
                async with session.get(self.config["frontend_url"], timeout=10) as response:
                    if response.status == 200:
                        content = await response.text()

                        # Check for React app
                        has_react = 'id="root"' in content or 'react' in content.lower()
                        results["tests"].append({
                            "name": "Frontend React App",
                            "status": "PASS" if has_react else "FAIL",
                            "details": "React application detected" if has_react else "React app not detected"
                        })

                        # Check for responsive design
                        has_viewport = 'name="viewport"' in content
                        results["tests"].append({
                            "name": "Responsive Design Meta",
                            "status": "PASS" if has_viewport else "FAIL",
                            "details": "Viewport meta tag found" if has_viewport else "Missing viewport meta tag"
                        })

                        print(f"    ✅ Frontend accessible: {response.status}")
                        if has_react:
                            print("    ✅ React application detected")
                        if has_viewport:
                            print("    ✅ Responsive design meta tag found")

                    else:
                        results["tests"].append({
                            "name": "Frontend Accessibility",
                            "status": "FAIL",
                            "details": f"Frontend returned status {response.status}"
                        })
                        results["critical_issues"].append(f"Frontend not accessible: {response.status}")
                        print(f"    ❌ Frontend not accessible: {response.status}")

        except Exception as e:
            results["tests"].append({
                "name": "Frontend Accessibility",
                "status": "FAIL",
                "details": str(e)
            })
            results["critical_issues"].append(f"Frontend connection failed: {str(e)}")
            print(f"    ❌ Frontend connection failed: {str(e)}")
            print("    💡 Try starting the frontend with: cd agnoworksphere && npm start")

        # Step 3.2: Test User Registration Flow (API Level)
        print("  👤 Step 3.2: Testing User Registration Flow...")
        try:
            async with aiohttp.ClientSession() as session:
                # Test registration endpoint with sample data
                registration_data = {
                    "email": "<EMAIL>",
                    "password": "SecurePass123!",
                    "first_name": "Test",
                    "last_name": "User"
                }

                async with session.post(
                    f"{self.config['api_base_url']}/api/auth/register",
                    json=registration_data,
                    timeout=10
                ) as response:
                    if response.status in [200, 201, 409]:  # 409 = user already exists
                        results["tests"].append({
                            "name": "User Registration API",
                            "status": "PASS",
                            "details": f"Registration endpoint working (status: {response.status})"
                        })
                        print(f"    ✅ Registration API working: {response.status}")
                    else:
                        response_text = await response.text()
                        results["tests"].append({
                            "name": "User Registration API",
                            "status": "FAIL",
                            "details": f"Unexpected status {response.status}: {response_text[:100]}"
                        })
                        print(f"    ❌ Registration API failed: {response.status}")

        except Exception as e:
            results["tests"].append({
                "name": "User Registration API",
                "status": "FAIL",
                "details": str(e)
            })
            print(f"    ❌ Registration test failed: {str(e)}")

        # Manual testing instructions
        print("  📝 Step 3.3: Manual Testing Instructions...")
        print("    💡 For complete user workflow testing, manually test:")
        print("       1. Navigate to http://localhost:3000")
        print("       2. Test registration with @agnoshin.com email")
        print("       3. Test dashboard functionality")
        print("       4. Test board and card creation")
        print("       5. Test invitation system")

        results["tests"].append({
            "name": "Manual Testing Instructions",
            "status": "MANUAL",
            "details": "Manual testing required for complete workflow validation"
        })

        # Calculate score
        passed_tests = sum(1 for test in results["tests"] if test["status"] == "PASS")
        manual_tests = sum(1 for test in results["tests"] if test["status"] == "MANUAL")
        total_tests = len(results["tests"])

        if total_tests > 0:
            # Give partial credit for manual tests
            results["score"] = ((passed_tests + manual_tests * 0.6) / total_tests * 100)
        else:
            results["score"] = 0

        return results

    async def phase4_rbac_testing(self) -> Dict[str, Any]:
        """Phase 4: Role-Based Access Control Testing"""
        print("🔧 Testing Role-Based Access Control...")

        results = {
            "tests": [],
            "score": 0,
            "max_score": 100,
            "critical_issues": [],
            "details": {}
        }

        # Step 4.1: Check Role Definitions in Database
        print("  🔒 Step 4.1: Checking Role Definitions...")
        try:
            conn = await asyncpg.connect(self.config["database_url"])

            # Check if role constraint exists
            role_constraint = await conn.fetchval("""
                SELECT COUNT(*) FROM information_schema.check_constraints
                WHERE constraint_name = 'valid_role'
            """)

            if role_constraint > 0:
                results["tests"].append({
                    "name": "Role Validation Constraint",
                    "status": "PASS",
                    "details": "Role validation constraint exists in database"
                })
                print("    ✅ Role validation constraint found")
            else:
                results["tests"].append({
                    "name": "Role Validation Constraint",
                    "status": "FAIL",
                    "details": "Role validation constraint missing"
                })
                results["critical_issues"].append("Role validation constraint missing from database")
                print("    ❌ Role validation constraint missing")

            await conn.close()

        except Exception as e:
            results["tests"].append({
                "name": "RBAC Database Structure",
                "status": "FAIL",
                "details": str(e)
            })
            results["critical_issues"].append(f"RBAC database check failed: {str(e)}")
            print(f"    ❌ RBAC database check failed: {str(e)}")

        # Manual RBAC testing instructions
        print("  📝 Step 4.2: Manual RBAC Testing Instructions...")
        print("    💡 For complete RBAC testing, manually test:")
        print("       1. Create users with different roles (Owner, Admin, Member, Viewer)")
        print("       2. Test permission enforcement for each role")
        print("       3. Verify UI elements show/hide based on permissions")
        print("       4. Test cross-organization access prevention")

        results["tests"].append({
            "name": "Manual RBAC Testing",
            "status": "MANUAL",
            "details": "Manual testing required for complete RBAC validation"
        })

        # Calculate score
        passed_tests = sum(1 for test in results["tests"] if test["status"] == "PASS")
        manual_tests = sum(1 for test in results["tests"] if test["status"] == "MANUAL")
        total_tests = len(results["tests"])

        if total_tests > 0:
            results["score"] = ((passed_tests + manual_tests * 0.6) / total_tests * 100)
        else:
            results["score"] = 0

        return results
