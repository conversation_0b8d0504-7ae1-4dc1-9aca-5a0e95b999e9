#!/usr/bin/env python3
"""
Test complete user flow from registration to project creation
"""
import requests
import json
import time

BASE_URL = "http://localhost:3001/api"

def test_complete_user_flow():
    """Test the complete user flow"""
    print("🚀 Testing Complete User Flow")
    print("=" * 50)
    
    # Step 1: Register new user
    print("1️⃣ Testing user registration...")
    user_data = {
        "email": f"flowtest{int(time.time())}@company.com",
        "password": "FlowTest123!",
        "first_name": "Flow",
        "last_name": "Test",
        "organization_name": "Flow Test Company"
    }
    
    try:
        response = requests.post(
            f"{BASE_URL}/v1/auth/register",
            json=user_data,
            headers={"Content-Type": "application/json"}
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Registration successful!")
            print(f"   User: {data['data']['user']['first_name']} {data['data']['user']['last_name']}")
            print(f"   Email: {data['data']['user']['email']}")
            print(f"   Organization: {data['data']['organization']['name']}")
            print(f"   Role: {data['data']['role']}")
            
            token = data['data']['tokens']['access_token']
            org_id = data['data']['organization']['id']
            user_email = data['data']['user']['email']
            
        else:
            print(f"❌ Registration failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Registration error: {e}")
        return False
    
    # Step 2: Test dashboard stats
    print("\n2️⃣ Testing dashboard access...")
    try:
        headers = {
            "Authorization": f"Bearer {token}",
            "Content-Type": "application/json"
        }
        
        response = requests.get(f"{BASE_URL}/v1/dashboard/stats", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            stats = data['data']
            print(f"✅ Dashboard accessible!")
            print(f"   Organizations: {stats['total_organizations']}")
            print(f"   Projects: {stats['total_projects']}")
            print(f"   Members: {stats['total_members']}")
            print(f"   User Role: {data['user_role']}")
        else:
            print(f"❌ Dashboard failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Dashboard error: {e}")
        return False
    
    # Step 3: Create a project
    print("\n3️⃣ Testing project creation...")
    try:
        project_data = {
            "name": "Test Project from Flow",
            "description": "A project created during complete flow testing",
            "organization_id": org_id
        }
        
        response = requests.post(
            f"{BASE_URL}/v1/projects",
            json=project_data,
            headers=headers
        )
        
        if response.status_code == 200:
            data = response.json()
            project = data['data']
            print(f"✅ Project created successfully!")
            print(f"   Name: {project['name']}")
            print(f"   Description: {project['description']}")
            print(f"   Organization: {project['organization_id']}")
            print(f"   Status: {project['status']}")
            
            project_id = project['id']
        else:
            print(f"❌ Project creation failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Project creation error: {e}")
        return False
    
    # Step 4: Test member invitation (same domain)
    print("\n4️⃣ Testing member invitation...")
    try:
        # Extract domain from user email
        domain = user_email.split('@')[1]
        invite_email = f"member{int(time.time())}@{domain}"
        
        invite_data = {
            "email": invite_email,
            "role": "member"
        }
        
        response = requests.post(
            f"{BASE_URL}/v1/organizations/{org_id}/invite",
            json=invite_data,
            headers=headers
        )
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ Member invitation sent!")
            print(f"   Invited: {invite_email}")
            print(f"   Role: member")
            print(f"   Message: {data['message']}")
        else:
            print(f"❌ Invitation failed: {response.status_code} - {response.text}")
            return False
    except Exception as e:
        print(f"❌ Invitation error: {e}")
        return False
    
    # Step 5: Test domain restriction
    print("\n5️⃣ Testing domain restriction...")
    try:
        restricted_invite = {
            "email": "<EMAIL>",
            "role": "member"
        }
        
        response = requests.post(
            f"{BASE_URL}/v1/organizations/{org_id}/invite",
            json=restricted_invite,
            headers=headers
        )
        
        if response.status_code == 403:
            print(f"✅ Domain restriction working!")
            print(f"   Blocked: <EMAIL>")
        else:
            print(f"⚠️  Domain restriction not enforced: {response.status_code}")
    except Exception as e:
        print(f"❌ Domain restriction test error: {e}")
    
    # Step 6: Verify updated dashboard stats
    print("\n6️⃣ Testing updated dashboard stats...")
    try:
        response = requests.get(f"{BASE_URL}/v1/dashboard/stats", headers=headers)
        
        if response.status_code == 200:
            data = response.json()
            stats = data['data']
            print(f"✅ Updated dashboard stats!")
            print(f"   Organizations: {stats['total_organizations']}")
            print(f"   Projects: {stats['total_projects']} (should be 1)")
            print(f"   Members: {stats['total_members']}")
        else:
            print(f"❌ Updated dashboard failed: {response.status_code}")
    except Exception as e:
        print(f"❌ Updated dashboard error: {e}")
    
    print("\n🎉 Complete User Flow Test Finished!")
    print("\n📋 Flow Summary:")
    print("✅ User registration with organization creation")
    print("✅ Automatic owner role assignment")
    print("✅ Dashboard access with role-based data")
    print("✅ Project creation within organization")
    print("✅ Member invitation system")
    print("✅ Domain-based access restrictions")
    print("✅ Real-time dashboard updates")
    
    print(f"\n🌐 Test User Details:")
    print(f"   Email: {user_email}")
    print(f"   Organization: Flow Test Company")
    print(f"   Role: owner")
    print(f"   Projects: 1")
    
    return True

if __name__ == "__main__":
    test_complete_user_flow()
