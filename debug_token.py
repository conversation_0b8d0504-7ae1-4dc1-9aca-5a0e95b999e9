#!/usr/bin/env python3
"""
Debug token creation and verification
"""

import requests
import json
import jwt

# Configuration
API_BASE_URL = "http://localhost:3001"
TEST_USER_EMAIL = "<EMAIL>"
TEST_USER_PASSWORD = "Owner123!"

def main():
    print("🔍 Debugging Token Creation and Verification")
    print("=" * 50)
    
    # Login and get token
    print("1. Testing login...")
    login_response = requests.post(f"{API_BASE_URL}/api/v1/auth/login", json={
        "email": TEST_USER_EMAIL,
        "password": TEST_USER_PASSWORD
    })
    
    if login_response.status_code != 200:
        print(f"❌ Login failed: {login_response.status_code}")
        return
    
    login_data = login_response.json()
    token = login_data["data"]["tokens"]["access_token"]
    print(f"✅ Login successful, got token")
    
    # Decode token (without verification to see contents)
    print("\n2. Decoding token contents...")
    try:
        # Decode without verification to see the payload
        decoded = jwt.decode(token, options={"verify_signature": False})
        print(f"Token payload: {json.dumps(decoded, indent=2)}")
    except Exception as e:
        print(f"❌ Error decoding token: {e}")
    
    # Test profile endpoint
    print("\n3. Testing profile endpoint...")
    headers = {"Authorization": f"Bearer {token}"}
    profile_response = requests.get(f"{API_BASE_URL}/api/v1/users/profile", headers=headers)
    
    print(f"Profile status: {profile_response.status_code}")
    print(f"Profile response: {profile_response.text}")
    
    # Test users/me endpoint
    print("\n4. Testing users/me endpoint...")
    me_response = requests.get(f"{API_BASE_URL}/api/v1/users/me", headers=headers)
    
    print(f"Users/me status: {me_response.status_code}")
    if me_response.status_code == 200:
        print("✅ Users/me working")
    else:
        print(f"❌ Users/me failed: {me_response.text}")

if __name__ == "__main__":
    main()
