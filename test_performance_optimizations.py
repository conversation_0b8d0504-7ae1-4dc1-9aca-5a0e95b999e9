#!/usr/bin/env python3
"""
Performance Testing for Optimized Agno WorkSphere
Test the performance improvements from connection pooling and caching
"""

import asyncio
import aiohttp
import time
import json
import statistics
from concurrent.futures import ThreadPoolExecutor
import threading

class PerformanceTester:
    def __init__(self):
        self.api_base_url = "http://localhost:3001"
        self.results = {
            "response_times": [],
            "concurrent_tests": [],
            "cache_performance": [],
            "database_performance": []
        }
        
    async def test_api_response_times(self):
        """Test API response times"""
        print("🔍 TESTING API RESPONSE TIMES")
        print("-" * 40)
        
        endpoints = [
            ("/health", "GET", None),
            ("/api/v1/auth/register", "POST", {
                "email": f"perf_test_{int(time.time())}@agnoshin.com",
                "password": "TestPass123!",
                "first_name": "Perf",
                "last_name": "Test",
                "organization_name": "Performance Test Org"
            }),
        ]
        
        async with aiohttp.ClientSession() as session:
            for endpoint, method, data in endpoints:
                times = []
                
                # Test each endpoint 5 times
                for i in range(5):
                    start_time = time.time()
                    
                    try:
                        if method == "GET":
                            async with session.get(f"{self.api_base_url}{endpoint}") as response:
                                response_time = (time.time() - start_time) * 1000
                                times.append(response_time)
                                print(f"   {endpoint}: {response.status} ({response_time:.1f}ms)")
                        else:
                            async with session.post(f"{self.api_base_url}{endpoint}", json=data) as response:
                                response_time = (time.time() - start_time) * 1000
                                times.append(response_time)
                                print(f"   {endpoint}: {response.status} ({response_time:.1f}ms)")
                                
                    except Exception as e:
                        print(f"   ❌ {endpoint} error: {e}")
                
                if times:
                    avg_time = statistics.mean(times)
                    min_time = min(times)
                    max_time = max(times)
                    
                    print(f"   📊 {endpoint} stats:")
                    print(f"      Average: {avg_time:.1f}ms")
                    print(f"      Min: {min_time:.1f}ms")
                    print(f"      Max: {max_time:.1f}ms")
                    
                    self.results["response_times"].append({
                        "endpoint": endpoint,
                        "average": avg_time,
                        "min": min_time,
                        "max": max_time
                    })
                
                print()
    
    async def test_concurrent_requests(self):
        """Test concurrent request handling"""
        print("🔍 TESTING CONCURRENT REQUEST HANDLING")
        print("-" * 40)
        
        async def make_request(session, request_id):
            """Make a single request"""
            start_time = time.time()
            try:
                async with session.get(f"{self.api_base_url}/health") as response:
                    response_time = (time.time() - start_time) * 1000
                    return {
                        "request_id": request_id,
                        "status": response.status,
                        "response_time": response_time,
                        "success": response.status == 200
                    }
            except Exception as e:
                return {
                    "request_id": request_id,
                    "status": 0,
                    "response_time": (time.time() - start_time) * 1000,
                    "success": False,
                    "error": str(e)
                }
        
        # Test different concurrency levels
        concurrency_levels = [5, 10, 20]
        
        for concurrency in concurrency_levels:
            print(f"   Testing {concurrency} concurrent requests...")
            
            async with aiohttp.ClientSession() as session:
                start_time = time.time()
                
                # Create concurrent tasks
                tasks = [make_request(session, i) for i in range(concurrency)]
                results = await asyncio.gather(*tasks)
                
                total_time = (time.time() - start_time) * 1000
                
                # Analyze results
                successful_requests = [r for r in results if r["success"]]
                failed_requests = [r for r in results if not r["success"]]
                
                if successful_requests:
                    response_times = [r["response_time"] for r in successful_requests]
                    avg_response_time = statistics.mean(response_times)
                    
                    print(f"      ✅ Successful: {len(successful_requests)}/{concurrency}")
                    print(f"      📊 Average response time: {avg_response_time:.1f}ms")
                    print(f"      ⏱️ Total time: {total_time:.1f}ms")
                    print(f"      🚀 Requests per second: {concurrency / (total_time / 1000):.1f}")
                    
                    self.results["concurrent_tests"].append({
                        "concurrency": concurrency,
                        "successful": len(successful_requests),
                        "failed": len(failed_requests),
                        "avg_response_time": avg_response_time,
                        "total_time": total_time,
                        "rps": concurrency / (total_time / 1000)
                    })
                else:
                    print(f"      ❌ All requests failed")
                
                if failed_requests:
                    print(f"      ❌ Failed: {len(failed_requests)}")
                
                print()
    
    async def test_cache_performance(self):
        """Test caching performance"""
        print("🔍 TESTING CACHE PERFORMANCE")
        print("-" * 40)
        
        # First, register a user to get a token
        async with aiohttp.ClientSession() as session:
            registration_data = {
                "email": f"cache_test_{int(time.time())}@agnoshin.com",
                "password": "CacheTest123!",
                "first_name": "Cache",
                "last_name": "Test",
                "organization_name": "Cache Test Org"
            }
            
            try:
                async with session.post(
                    f"{self.api_base_url}/api/v1/auth/register",
                    json=registration_data
                ) as response:
                    if response.status in [200, 201]:
                        response_data = await response.json()
                        token = response_data["data"]["tokens"]["access_token"]
                        headers = {"Authorization": f"Bearer {token}"}
                        
                        # Test cached endpoint multiple times
                        endpoint = "/api/v1/users/profile"
                        
                        print(f"   Testing cache performance for {endpoint}...")
                        
                        times = []
                        for i in range(10):
                            start_time = time.time()
                            
                            async with session.get(
                                f"{self.api_base_url}{endpoint}",
                                headers=headers
                            ) as cache_response:
                                response_time = (time.time() - start_time) * 1000
                                times.append(response_time)
                                
                                cache_status = "HIT" if i > 0 else "MISS"
                                print(f"      Request {i+1}: {cache_response.status} ({response_time:.1f}ms) - Cache {cache_status}")
                        
                        if times:
                            first_request = times[0]  # Cache miss
                            cached_requests = times[1:]  # Cache hits
                            
                            if cached_requests:
                                avg_cached = statistics.mean(cached_requests)
                                improvement = ((first_request - avg_cached) / first_request) * 100
                                
                                print(f"   📊 Cache performance:")
                                print(f"      First request (cache miss): {first_request:.1f}ms")
                                print(f"      Cached requests average: {avg_cached:.1f}ms")
                                print(f"      Performance improvement: {improvement:.1f}%")
                                
                                self.results["cache_performance"].append({
                                    "endpoint": endpoint,
                                    "cache_miss": first_request,
                                    "cache_hit_avg": avg_cached,
                                    "improvement_percent": improvement
                                })
                    else:
                        print(f"   ❌ Registration failed: {response.status}")
                        
            except Exception as e:
                print(f"   ❌ Cache test error: {e}")
    
    async def test_database_pool_performance(self):
        """Test database connection pool performance"""
        print("🔍 TESTING DATABASE POOL PERFORMANCE")
        print("-" * 40)
        
        # Test health endpoint which shows pool stats
        async with aiohttp.ClientSession() as session:
            try:
                async with session.get(f"{self.api_base_url}/health") as response:
                    if response.status == 200:
                        data = await response.json()
                        pool_stats = data["data"]["database_pool"]
                        cache_stats = data["data"]["cache"]
                        
                        print(f"   📊 Database Pool Statistics:")
                        print(f"      Status: {pool_stats.get('status', 'unknown')}")
                        print(f"      Pool size: {pool_stats.get('size', 0)}")
                        print(f"      Min size: {pool_stats.get('min_size', 0)}")
                        print(f"      Max size: {pool_stats.get('max_size', 0)}")
                        print(f"      Idle connections: {pool_stats.get('idle_size', 0)}")
                        
                        print(f"   📊 Cache Statistics:")
                        print(f"      Cache type: {cache_stats.get('cache_type', 'unknown')}")
                        print(f"      Total keys: {cache_stats.get('total_keys', 0)}")
                        print(f"      Active keys: {cache_stats.get('active_keys', 0)}")
                        
                        self.results["database_performance"].append({
                            "pool_stats": pool_stats,
                            "cache_stats": cache_stats
                        })
                    else:
                        print(f"   ❌ Health check failed: {response.status}")
                        
            except Exception as e:
                print(f"   ❌ Database pool test error: {e}")
    
    async def run_all_tests(self):
        """Run all performance tests"""
        print("🚀 PERFORMANCE OPTIMIZATION TESTING")
        print("=" * 50)
        print(f"Started at: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 50)
        
        await self.test_api_response_times()
        await self.test_concurrent_requests()
        await self.test_cache_performance()
        await self.test_database_pool_performance()
        
        self.generate_summary()
    
    def generate_summary(self):
        """Generate performance test summary"""
        print("\n" + "=" * 50)
        print("📊 PERFORMANCE OPTIMIZATION SUMMARY")
        print("=" * 50)
        
        # Response time summary
        if self.results["response_times"]:
            print("\n🚀 API Response Times:")
            for result in self.results["response_times"]:
                print(f"   {result['endpoint']}: {result['average']:.1f}ms avg")
        
        # Concurrency summary
        if self.results["concurrent_tests"]:
            print("\n⚡ Concurrent Request Handling:")
            for result in self.results["concurrent_tests"]:
                print(f"   {result['concurrency']} concurrent: {result['rps']:.1f} req/sec")
        
        # Cache performance summary
        if self.results["cache_performance"]:
            print("\n💾 Cache Performance:")
            for result in self.results["cache_performance"]:
                print(f"   {result['endpoint']}: {result['improvement_percent']:.1f}% improvement")
        
        # Overall assessment
        avg_response_times = []
        if self.results["response_times"]:
            avg_response_times = [r["average"] for r in self.results["response_times"]]
        
        if avg_response_times:
            overall_avg = statistics.mean(avg_response_times)
            
            if overall_avg < 200:
                status = "🟢 EXCELLENT"
                assessment = "Production ready with excellent performance"
            elif overall_avg < 500:
                status = "🟡 GOOD"
                assessment = "Good performance, suitable for production"
            elif overall_avg < 1000:
                status = "🟠 FAIR"
                assessment = "Acceptable performance, some optimization recommended"
            else:
                status = "🔴 NEEDS IMPROVEMENT"
                assessment = "Performance optimization required"
            
            print(f"\n🎯 OVERALL PERFORMANCE: {status}")
            print(f"   Average response time: {overall_avg:.1f}ms")
            print(f"   Assessment: {assessment}")
        
        print(f"\n✅ OPTIMIZATIONS IMPLEMENTED:")
        print(f"   • Database connection pooling (5-20 connections)")
        print(f"   • In-memory caching for frequently accessed data")
        print(f"   • Performance monitoring middleware")
        print(f"   • Optimized database queries")
        print(f"   • Async/await throughout the application")

async def main():
    """Main test execution"""
    tester = PerformanceTester()
    await tester.run_all_tests()

if __name__ == "__main__":
    asyncio.run(main())
