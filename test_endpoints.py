#!/usr/bin/env python3
"""
Simple API endpoint test script
"""

import requests
import json

# Configuration
API_BASE_URL = "http://localhost:3001"
TEST_USER_EMAIL = "<EMAIL>"
TEST_USER_PASSWORD = "Owner123!"

def test_login():
    """Test login endpoint"""
    print("🔐 Testing login...")
    
    response = requests.post(f"{API_BASE_URL}/api/v1/auth/login", json={
        "email": TEST_USER_EMAIL,
        "password": TEST_USER_PASSWORD
    })
    
    if response.status_code == 200:
        data = response.json()
        if data.get("success"):
            print("✅ Login successful")
            return data["data"]["tokens"]["access_token"]
        else:
            print(f"❌ Login failed: {data}")
            return None
    else:
        print(f"❌ Login failed with status {response.status_code}: {response.text}")
        return None

def test_endpoint(endpoint, token, method="GET", data=None):
    """Test a specific endpoint"""
    headers = {
        "Authorization": f"Bearer {token}",
        "Content-Type": "application/json"
    }
    
    try:
        if method == "GET":
            response = requests.get(f"{API_BASE_URL}{endpoint}", headers=headers)
        elif method == "POST":
            response = requests.post(f"{API_BASE_URL}{endpoint}", headers=headers, json=data)
        
        if response.status_code == 200:
            print(f"✅ {method} {endpoint} - Success")
            return True
        else:
            print(f"❌ {method} {endpoint} - Failed: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ {method} {endpoint} - Error: {e}")
        return False

def main():
    print("🚀 Testing API Endpoints")
    print("=" * 40)
    
    # Login first
    token = test_login()
    if not token:
        print("❌ Cannot proceed without login")
        return
    
    # Test endpoints
    endpoints = [
        "/health",
        "/api/v1/",
        "/api/v1/users/me",
        "/api/v1/users/profile",
        "/api/v1/organizations",
        "/api/v1/projects",
        "/api/v1/dashboard/stats",
        "/api/v1/boards",
        "/api/v1/cards"
    ]
    
    success_count = 0
    total_count = len(endpoints)
    
    for endpoint in endpoints:
        if test_endpoint(endpoint, token):
            success_count += 1
    
    print("\n" + "=" * 40)
    print(f"🎉 Results: {success_count}/{total_count} endpoints working")
    
    if success_count == total_count:
        print("✅ All endpoints are working correctly!")
    else:
        print(f"⚠️  {total_count - success_count} endpoints need attention")

if __name__ == "__main__":
    main()
