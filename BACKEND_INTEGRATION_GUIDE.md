# Agno WorkSphere - Backend Integration Guide

## Table of Contents
1. [Overview](#overview)
2. [Authentication & Authorization](#authentication--authorization)
3. [User Management](#user-management)
4. [Organization Management](#organization-management)
5. [Project & Kanban Management](#project--kanban-management)
6. [Team Management](#team-management)
7. [Role-Based Access Control](#role-based-access-control)
8. [API Endpoints Structure](#api-endpoints-structure)
9. [Database Schema Requirements](#database-schema-requirements)
10. [Real-time Features](#real-time-features)
11. [File Upload & Storage](#file-upload--storage)
12. [Integrations](#integrations)

## Overview

Agno WorkSphere is a comprehensive project management platform with role-based access control, real-time collaboration, and extensive integration capabilities. This document outlines all frontend features that require backend implementation.

## Authentication & Authorization

### Pages
- `/login` - User authentication
- `/register` - User registration

### Features Required
- JWT token-based authentication
- Password hashing and validation
- Email verification
- Password reset functionality
- Session management
- Multi-factor authentication (2FA)

### API Endpoints Needed
```
POST /api/auth/login
POST /api/auth/register
POST /api/auth/logout
POST /api/auth/refresh-token
POST /api/auth/forgot-password
POST /api/auth/reset-password
POST /api/auth/verify-email
POST /api/auth/enable-2fa
POST /api/auth/verify-2fa
```

### Frontend Functions
```javascript
// Authentication service functions
authService.login(email, password)
authService.register(userData)
authService.logout()
authService.refreshToken()
authService.forgotPassword(email)
authService.resetPassword(token, newPassword)
authService.verifyEmail(token)
authService.enable2FA()
authService.verify2FA(code)
```

## User Management

### Pages
- `/user-profile-settings` - User profile management

### Features Required
- User profile CRUD operations
- Avatar upload and management
- Notification preferences
- Account settings
- Privacy settings

### API Endpoints Needed
```
GET /api/users/profile
PUT /api/users/profile
POST /api/users/avatar
DELETE /api/users/avatar
GET /api/users/notifications/preferences
PUT /api/users/notifications/preferences
DELETE /api/users/account
```

### Frontend Functions
```javascript
// User service functions
userService.getProfile()
userService.updateProfile(profileData)
userService.uploadAvatar(file)
userService.deleteAvatar()
userService.getNotificationPreferences()
userService.updateNotificationPreferences(preferences)
userService.deleteAccount()
```

## Organization Management

### Pages
- `/organization-settings` - Organization configuration
- `/organization-dashboard` - Organization overview

### Features Required
- Organization CRUD operations
- Logo upload and management
- Domain settings and restrictions
- Billing and subscription management
- Organization switching
- Member invitation and management

### API Endpoints Needed
```
GET /api/organizations
POST /api/organizations
GET /api/organizations/:id
PUT /api/organizations/:id
DELETE /api/organizations/:id
POST /api/organizations/:id/logo
DELETE /api/organizations/:id/logo
GET /api/organizations/:id/members
POST /api/organizations/:id/invite
DELETE /api/organizations/:id/members/:userId
PUT /api/organizations/:id/members/:userId/role
GET /api/organizations/:id/billing
PUT /api/organizations/:id/billing
GET /api/organizations/:id/subscription
PUT /api/organizations/:id/subscription
```

### Frontend Functions
```javascript
// Organization service functions
organizationService.getOrganizations()
organizationService.createOrganization(orgData)
organizationService.getOrganization(id)
organizationService.updateOrganization(id, orgData)
organizationService.deleteOrganization(id)
organizationService.uploadLogo(id, file)
organizationService.deleteLogo(id)
organizationService.getMembers(id)
organizationService.inviteMember(id, inviteData)
organizationService.removeMember(id, userId)
organizationService.updateMemberRole(id, userId, role)
organizationService.getBilling(id)
organizationService.updateBilling(id, billingData)
organizationService.getSubscription(id)
organizationService.updateSubscription(id, subscriptionData)
```

## Project & Kanban Management

### Pages
- `/kanban-board` - Main project board
- `/card-details` - Task/card details modal
- `/project-management` - Project overview

### Features Required
- Project CRUD operations
- Board/column management
- Card/task management with drag-and-drop
- Labels and priorities
- Due dates and assignments
- Comments and attachments
- Activity tracking
- Real-time updates

### API Endpoints Needed
```
GET /api/projects
POST /api/projects
GET /api/projects/:id
PUT /api/projects/:id
DELETE /api/projects/:id
GET /api/projects/:id/boards
POST /api/projects/:id/boards
PUT /api/boards/:id
DELETE /api/boards/:id
GET /api/boards/:id/columns
POST /api/boards/:id/columns
PUT /api/columns/:id
DELETE /api/columns/:id
PUT /api/columns/:id/order
GET /api/columns/:id/cards
POST /api/columns/:id/cards
GET /api/cards/:id
PUT /api/cards/:id
DELETE /api/cards/:id
PUT /api/cards/:id/move
POST /api/cards/:id/comments
GET /api/cards/:id/comments
PUT /api/comments/:id
DELETE /api/comments/:id
POST /api/cards/:id/attachments
DELETE /api/attachments/:id
GET /api/cards/:id/activity
```

### Frontend Functions
```javascript
// Project service functions
projectService.getProjects()
projectService.createProject(projectData)
projectService.getProject(id)
projectService.updateProject(id, projectData)
projectService.deleteProject(id)

// Board service functions
boardService.getBoards(projectId)
boardService.createBoard(projectId, boardData)
boardService.updateBoard(id, boardData)
boardService.deleteBoard(id)

// Column service functions
columnService.getColumns(boardId)
columnService.createColumn(boardId, columnData)
columnService.updateColumn(id, columnData)
columnService.deleteColumn(id)
columnService.reorderColumns(boardId, columnOrder)

// Card service functions
cardService.getCards(columnId)
cardService.createCard(columnId, cardData)
cardService.getCard(id)
cardService.updateCard(id, cardData)
cardService.deleteCard(id)
cardService.moveCard(id, targetColumnId, position)
cardService.addComment(id, commentData)
cardService.getComments(id)
cardService.updateComment(commentId, commentData)
cardService.deleteComment(commentId)
cardService.addAttachment(id, file)
cardService.deleteAttachment(attachmentId)
cardService.getActivity(id)
```

## Team Management

### Pages
- `/team-members` - Team member management

### Features Required
- Member listing with search and filters
- Role management and permissions
- Member invitation system
- Activity tracking
- Bulk operations

### API Endpoints Needed
```
GET /api/teams/:orgId/members
POST /api/teams/:orgId/invite
PUT /api/teams/:orgId/members/:userId
DELETE /api/teams/:orgId/members/:userId
GET /api/teams/:orgId/invitations
DELETE /api/teams/:orgId/invitations/:inviteId
POST /api/teams/:orgId/invitations/:inviteId/resend
GET /api/teams/:orgId/members/:userId/activity
POST /api/teams/:orgId/members/bulk-action
```

### Frontend Functions
```javascript
// Team service functions
teamService.getMembers(orgId, filters)
teamService.inviteMember(orgId, inviteData)
teamService.updateMember(orgId, userId, memberData)
teamService.removeMember(orgId, userId)
teamService.getInvitations(orgId)
teamService.cancelInvitation(orgId, inviteId)
teamService.resendInvitation(orgId, inviteId)
teamService.getMemberActivity(orgId, userId)
teamService.bulkAction(orgId, action, memberIds)
```

## Role-Based Access Control

### Pages
- `/role-based-dashboard` - Role-specific dashboard

### Roles & Permissions
- **Viewer**: Read-only access to projects and boards
- **Member**: Create/edit projects, view team members
- **Admin**: All member permissions + organization settings + invite members
- **Owner**: Full access including billing, analytics, and user management

### Features Required
- Role-based navigation filtering
- Permission checking middleware
- Resource access control
- Audit logging

### API Endpoints Needed
```
GET /api/roles
GET /api/roles/:roleId/permissions
GET /api/users/:userId/permissions
POST /api/audit/log
GET /api/audit/logs
```

### Frontend Functions
```javascript
// Role service functions
roleService.getRoles()
roleService.getRolePermissions(roleId)
roleService.getUserPermissions(userId)
roleService.checkPermission(permission, resource)
roleService.logAuditEvent(event)
roleService.getAuditLogs(filters)
```

## API Endpoints Structure

### Base URL
```
Production: https://api.agno-worksphere.com
Development: http://localhost:3001/api
```

### Authentication Headers
```javascript
{
  'Authorization': 'Bearer <jwt_token>',
  'Content-Type': 'application/json',
  'X-Organization-ID': '<org_id>' // For multi-tenant requests
}
```

### Standard Response Format
```javascript
// Success Response
{
  "success": true,
  "data": {}, // Response data
  "message": "Operation completed successfully",
  "timestamp": "2024-01-15T10:30:00Z"
}

// Error Response
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "Invalid input data",
    "details": {
      "field": "email",
      "message": "Email is required"
    }
  },
  "timestamp": "2024-01-15T10:30:00Z"
}
```

### Pagination Format
```javascript
{
  "success": true,
  "data": {
    "items": [], // Array of items
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 150,
      "totalPages": 8,
      "hasNext": true,
      "hasPrev": false
    }
  }
}
```

## Database Schema Requirements

### Core Tables

#### Users
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  password_hash VARCHAR(255) NOT NULL,
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  avatar_url VARCHAR(500),
  email_verified BOOLEAN DEFAULT FALSE,
  two_factor_enabled BOOLEAN DEFAULT FALSE,
  two_factor_secret VARCHAR(255),
  last_login_at TIMESTAMP,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### Organizations
```sql
CREATE TABLE organizations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  description TEXT,
  logo_url VARCHAR(500),
  website VARCHAR(255),
  industry VARCHAR(100),
  size VARCHAR(50),
  timezone VARCHAR(100) DEFAULT 'UTC',
  language VARCHAR(10) DEFAULT 'en',
  allowed_domains TEXT[], -- Array of allowed email domains
  created_by UUID REFERENCES users(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### Organization Members
```sql
CREATE TABLE organization_members (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  role VARCHAR(50) NOT NULL CHECK (role IN ('viewer', 'member', 'admin', 'owner')),
  invited_by UUID REFERENCES users(id),
  joined_at TIMESTAMP DEFAULT NOW(),
  created_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(organization_id, user_id)
);
```

#### Projects
```sql
CREATE TABLE projects (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  status VARCHAR(50) DEFAULT 'active',
  priority VARCHAR(20) DEFAULT 'medium',
  start_date DATE,
  due_date DATE,
  created_by UUID REFERENCES users(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### Boards
```sql
CREATE TABLE boards (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  description TEXT,
  created_by UUID REFERENCES users(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### Columns
```sql
CREATE TABLE columns (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  board_id UUID REFERENCES boards(id) ON DELETE CASCADE,
  name VARCHAR(255) NOT NULL,
  position INTEGER NOT NULL,
  color VARCHAR(7), -- Hex color code
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### Cards
```sql
CREATE TABLE cards (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  column_id UUID REFERENCES columns(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  position INTEGER NOT NULL,
  priority VARCHAR(20) DEFAULT 'medium',
  due_date TIMESTAMP,
  created_by UUID REFERENCES users(id),
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### Card Assignments
```sql
CREATE TABLE card_assignments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  card_id UUID REFERENCES cards(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  assigned_by UUID REFERENCES users(id),
  assigned_at TIMESTAMP DEFAULT NOW(),
  UNIQUE(card_id, user_id)
);
```

#### Comments
```sql
CREATE TABLE comments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  card_id UUID REFERENCES cards(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  content TEXT NOT NULL,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

#### Attachments
```sql
CREATE TABLE attachments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  card_id UUID REFERENCES cards(id) ON DELETE CASCADE,
  filename VARCHAR(255) NOT NULL,
  original_name VARCHAR(255) NOT NULL,
  file_size BIGINT NOT NULL,
  mime_type VARCHAR(100) NOT NULL,
  file_url VARCHAR(500) NOT NULL,
  uploaded_by UUID REFERENCES users(id),
  uploaded_at TIMESTAMP DEFAULT NOW()
);
```

#### Activity Logs
```sql
CREATE TABLE activity_logs (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  organization_id UUID REFERENCES organizations(id) ON DELETE CASCADE,
  user_id UUID REFERENCES users(id) ON DELETE CASCADE,
  action VARCHAR(100) NOT NULL,
  resource_type VARCHAR(50) NOT NULL,
  resource_id UUID,
  metadata JSONB,
  ip_address INET,
  user_agent TEXT,
  created_at TIMESTAMP DEFAULT NOW()
);
```

## Real-time Features

### WebSocket Events
The application requires real-time updates for collaborative features.

#### Connection
```javascript
// WebSocket connection with authentication
const socket = io('ws://localhost:3001', {
  auth: {
    token: jwt_token,
    organizationId: org_id
  }
});
```

#### Events to Implement

##### Board Updates
```javascript
// Client sends
socket.emit('join_board', { boardId });
socket.emit('leave_board', { boardId });

// Server broadcasts
socket.on('card_created', (cardData) => {});
socket.on('card_updated', (cardData) => {});
socket.on('card_moved', (moveData) => {});
socket.on('card_deleted', (cardId) => {});
socket.on('column_created', (columnData) => {});
socket.on('column_updated', (columnData) => {});
socket.on('column_deleted', (columnId) => {});
```

##### User Presence
```javascript
// Server broadcasts
socket.on('user_joined_board', (userData) => {});
socket.on('user_left_board', (userId) => {});
socket.on('user_typing', (typingData) => {});
```

##### Notifications
```javascript
// Server sends
socket.on('notification', (notificationData) => {});
socket.on('mention', (mentionData) => {});
```

## File Upload & Storage

### Upload Requirements
- Maximum file size: 10MB per file
- Supported formats: Images (jpg, png, gif), Documents (pdf, doc, docx), Archives (zip, rar)
- Virus scanning for uploaded files
- CDN integration for fast delivery

### API Endpoints
```
POST /api/upload/avatar
POST /api/upload/organization-logo
POST /api/upload/card-attachment
DELETE /api/upload/:fileId
GET /api/upload/:fileId/download
```

### Frontend Functions
```javascript
// Upload service functions
uploadService.uploadAvatar(file, progressCallback)
uploadService.uploadOrgLogo(orgId, file, progressCallback)
uploadService.uploadCardAttachment(cardId, file, progressCallback)
uploadService.deleteFile(fileId)
uploadService.downloadFile(fileId)
```

### Storage Structure
```
/uploads
  /avatars
    /{userId}/
      /avatar.jpg
  /organizations
    /{orgId}/
      /logo.png
  /attachments
    /{cardId}/
      /{fileId}-{originalName}
```

## Integrations

### Third-Party Integrations
The organization settings page includes integration management for:

#### Slack Integration
```javascript
// API endpoints
POST /api/integrations/slack/connect
DELETE /api/integrations/slack/disconnect
POST /api/integrations/slack/test
GET /api/integrations/slack/channels

// Webhook payload format
{
  "event": "card_created",
  "data": {
    "cardId": "uuid",
    "title": "New task created",
    "assignee": "John Doe",
    "boardName": "Development Board"
  }
}
```

#### GitHub Integration
```javascript
// API endpoints
POST /api/integrations/github/connect
DELETE /api/integrations/github/disconnect
GET /api/integrations/github/repositories
POST /api/integrations/github/link-card

// Link format
{
  "cardId": "uuid",
  "repository": "owner/repo",
  "issueNumber": 123,
  "pullRequestNumber": 456
}
```

#### Google Drive Integration
```javascript
// API endpoints
POST /api/integrations/google-drive/connect
DELETE /api/integrations/google-drive/disconnect
GET /api/integrations/google-drive/files
POST /api/integrations/google-drive/attach-file
```

### API Keys Management
```javascript
// API endpoints
GET /api/api-keys
POST /api/api-keys
PUT /api/api-keys/:keyId
DELETE /api/api-keys/:keyId
POST /api/api-keys/:keyId/regenerate
```

## Security Requirements

### Authentication Security
- JWT tokens with 15-minute expiration
- Refresh tokens with 7-day expiration
- Rate limiting: 100 requests per minute per IP
- Password requirements: 8+ characters, special chars, numbers
- Account lockout after 5 failed attempts

### Data Security
- All sensitive data encrypted at rest
- HTTPS only in production
- SQL injection prevention
- XSS protection
- CSRF protection with tokens

### API Security
- API key authentication for integrations
- Request signing for webhook verification
- IP whitelisting for sensitive operations
- Audit logging for all data modifications

## Error Handling

### Error Codes
```javascript
const ERROR_CODES = {
  // Authentication
  INVALID_CREDENTIALS: 'AUTH_001',
  TOKEN_EXPIRED: 'AUTH_002',
  INSUFFICIENT_PERMISSIONS: 'AUTH_003',

  // Validation
  VALIDATION_ERROR: 'VAL_001',
  REQUIRED_FIELD_MISSING: 'VAL_002',
  INVALID_FORMAT: 'VAL_003',

  // Business Logic
  RESOURCE_NOT_FOUND: 'BIZ_001',
  DUPLICATE_RESOURCE: 'BIZ_002',
  OPERATION_NOT_ALLOWED: 'BIZ_003',

  // System
  INTERNAL_SERVER_ERROR: 'SYS_001',
  SERVICE_UNAVAILABLE: 'SYS_002',
  RATE_LIMIT_EXCEEDED: 'SYS_003'
};
```

## Performance Requirements

### Response Times
- API responses: < 200ms for 95% of requests
- File uploads: Progress indicators for files > 1MB
- Real-time updates: < 100ms latency
- Database queries: < 50ms for simple queries

### Caching Strategy
- Redis for session storage
- CDN for static assets
- Database query caching
- API response caching for read-heavy endpoints

### Monitoring
- Application performance monitoring (APM)
- Error tracking and alerting
- Database performance monitoring
- Real-time user analytics

## Development Guidelines

### API Versioning
```
/api/v1/users
/api/v2/projects
```

### Environment Variables
```bash
# Database
DATABASE_URL=postgresql://user:pass@localhost:5432/agno_worksphere
REDIS_URL=redis://localhost:6379

# Authentication
JWT_SECRET=your-secret-key
JWT_EXPIRES_IN=15m
REFRESH_TOKEN_EXPIRES_IN=7d

# File Storage
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_S3_BUCKET=agno-worksphere-uploads

# Integrations
SLACK_CLIENT_ID=your-slack-client-id
SLACK_CLIENT_SECRET=your-slack-client-secret
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret

# Email
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
```

### Testing Requirements
- Unit tests for all service functions
- Integration tests for API endpoints
- End-to-end tests for critical user flows
- Load testing for performance validation

This comprehensive guide provides all the necessary information for backend developers to implement the complete Agno WorkSphere system with proper integration points for the frontend application.
