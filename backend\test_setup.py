#!/usr/bin/env python3
"""
Test script to verify backend setup
"""
import asyncio
import sys
import os

# Add the backend directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_imports():
    """Test that all imports work correctly"""
    try:
        print("Testing imports...")
        
        # Test core imports
        from app.config import settings
        print("✓ Config imported successfully")
        
        from app.core.database import Base, get_db
        print("✓ Database core imported successfully")
        
        from app.core.security import hash_password, verify_password
        print("✓ Security utilities imported successfully")
        
        from app.core.exceptions import APIException
        print("✓ Exceptions imported successfully")
        
        # Test model imports
        from app.models.user import User
        from app.models.organization import Organization, OrganizationMember
        from app.models.project import Project
        from app.models.board import Board
        from app.models.column import Column
        from app.models.card import Card, CardAssignment
        from app.models.comment import Comment
        from app.models.attachment import Attachment
        from app.models.activity_log import ActivityLog
        print("✓ All models imported successfully")
        
        # Test schema imports
        from app.schemas.auth import UserRegister, UserLogin, AuthResponse
        from app.schemas.user import UserProfile, UserProfileUpdate
        from app.schemas.organization import OrganizationCreate, OrganizationResponse
        from app.schemas.project import ProjectCreate, ProjectResponse
        from app.schemas.card import CardCreate, CardResponse
        print("✓ All schemas imported successfully")
        
        # Test API imports
        from app.api.v1.router import api_router
        print("✓ API router imported successfully")
        
        from app.main import app
        print("✓ FastAPI app imported successfully")
        
        print("\n✅ All imports successful!")
        return True
        
    except Exception as e:
        print(f"\n❌ Import error: {e}")
        return False


async def test_password_hashing():
    """Test password hashing functionality"""
    try:
        print("\nTesting password hashing...")
        
        from app.core.security import hash_password, verify_password
        
        password = "TestPassword123!"
        hashed = hash_password(password)
        
        if verify_password(password, hashed):
            print("✓ Password hashing and verification works")
            return True
        else:
            print("❌ Password verification failed")
            return False
            
    except Exception as e:
        print(f"❌ Password hashing error: {e}")
        return False


async def test_jwt_tokens():
    """Test JWT token creation and verification"""
    try:
        print("\nTesting JWT tokens...")
        
        from app.core.security import create_access_token, verify_token
        
        test_data = {"sub": "test-user-id", "email": "<EMAIL>"}
        token = create_access_token(test_data)
        
        decoded = verify_token(token)
        
        if decoded.get("sub") == "test-user-id":
            print("✓ JWT token creation and verification works")
            return True
        else:
            print("❌ JWT token verification failed")
            return False
            
    except Exception as e:
        print(f"❌ JWT token error: {e}")
        return False


async def test_database_models():
    """Test database model creation"""
    try:
        print("\nTesting database models...")
        
        from app.models.user import User
        from app.models.organization import Organization
        
        # Test model instantiation
        user = User(
            email="<EMAIL>",
            password_hash="hashed_password",
            first_name="Test",
            last_name="User"
        )
        
        org = Organization(
            name="Test Organization",
            created_by=user.id
        )
        
        print("✓ Database models can be instantiated")
        return True
        
    except Exception as e:
        print(f"❌ Database model error: {e}")
        return False


async def test_pydantic_schemas():
    """Test Pydantic schema validation"""
    try:
        print("\nTesting Pydantic schemas...")
        
        from app.schemas.auth import UserRegister
        from app.schemas.organization import OrganizationCreate
        
        # Test valid data
        user_data = UserRegister(
            email="<EMAIL>",
            password="TestPassword123!",
            first_name="Test",
            last_name="User"
        )
        
        org_data = OrganizationCreate(
            name="Test Organization",
            description="A test organization"
        )
        
        print("✓ Pydantic schemas work correctly")
        return True
        
    except Exception as e:
        print(f"❌ Pydantic schema error: {e}")
        return False


async def main():
    """Run all tests"""
    print("🚀 Starting Agno WorkSphere Backend Setup Test\n")
    
    tests = [
        test_imports,
        test_password_hashing,
        test_jwt_tokens,
        test_database_models,
        test_pydantic_schemas
    ]
    
    results = []
    for test in tests:
        result = await test()
        results.append(result)
    
    print(f"\n📊 Test Results:")
    print(f"✅ Passed: {sum(results)}")
    print(f"❌ Failed: {len(results) - sum(results)}")
    
    if all(results):
        print("\n🎉 All tests passed! Backend setup is working correctly.")
        print("\nNext steps:")
        print("1. Set up your PostgreSQL database")
        print("2. Copy .env.example to .env and configure your settings")
        print("3. Run database migrations: alembic upgrade head")
        print("4. Start the development server: python run.py")
        return 0
    else:
        print("\n💥 Some tests failed. Please check the errors above.")
        return 1


if __name__ == "__main__":
    exit_code = asyncio.run(main())
