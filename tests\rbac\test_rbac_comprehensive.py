#!/usr/bin/env python3
"""
Comprehensive Role-Based Access Control (RBAC) Testing Suite
Tests user roles, permissions, and access restrictions
"""
import requests
import json
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import sys

BASE_URL = "http://localhost:3001"
API_BASE = f"{BASE_URL}/api/v1"

@dataclass
class RBACTestResult:
    test_name: str
    role: str
    endpoint: str
    expected_status: int
    actual_status: int
    status: str  # PASS, FAIL
    execution_time: float
    error_message: Optional[str] = None

class RBACTester:
    def __init__(self):
        self.results: List[RBACTestResult] = []
        self.test_users: Dict[str, Dict] = {}
        self.test_org_id: Optional[str] = None
        self.test_project_id: Optional[str] = None
        
    def log_result(self, result: RBACTestResult):
        """Log test result"""
        self.results.append(result)
        status_emoji = "✅" if result.status == "PASS" else "❌"
        print(f"{status_emoji} {result.role} - {result.endpoint} - Expected {result.expected_status}, Got {result.actual_status}")
        if result.error_message:
            print(f"   Error: {result.error_message}")
    
    def create_test_user(self, role: str, email_suffix: str = None) -> Optional[Dict]:
        """Create a test user with specific role"""
        if not email_suffix:
            email_suffix = role.lower()
        
        email = f"rbac_{email_suffix}_{int(time.time())}@testcompany.com"
        
        # Create user
        user_data = {
            "email": email,
            "password": "RBACTest123!",
            "first_name": f"RBAC {role.title()}",
            "last_name": "User",
            "organization_name": f"RBAC Test Org {role.title()}"
        }
        
        try:
            response = requests.post(
                f"{API_BASE}/auth/register",
                json=user_data,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                user_info = {
                    "email": email,
                    "password": "RBACTest123!",
                    "token": data['data']['tokens']['access_token'],
                    "user_id": data['data']['user']['id'],
                    "org_id": data['data']['organization']['id'],
                    "role": data['data']['role']
                }
                
                # Store organization ID for first user
                if not self.test_org_id:
                    self.test_org_id = user_info['org_id']
                
                return user_info
            else:
                print(f"❌ Failed to create {role} user: {response.status_code} - {response.text}")
                return None
                
        except Exception as e:
            print(f"❌ Error creating {role} user: {e}")
            return None
    
    def setup_test_users(self):
        """Setup test users with different roles"""
        print("👥 Setting up test users with different roles...")
        
        # Create owner user
        owner = self.create_test_user("owner", "owner")
        if owner:
            self.test_users["owner"] = owner
            print(f"✅ Created owner user: {owner['email']}")
        
        # Create admin user (invite to same org)
        admin = self.create_test_user("admin", "admin")
        if admin:
            self.test_users["admin"] = admin
            print(f"✅ Created admin user: {admin['email']}")
        
        # Create member user
        member = self.create_test_user("member", "member")
        if member:
            self.test_users["member"] = member
            print(f"✅ Created member user: {member['email']}")
        
        # Create viewer user
        viewer = self.create_test_user("viewer", "viewer")
        if viewer:
            self.test_users["viewer"] = viewer
            print(f"✅ Created viewer user: {viewer['email']}")
        
        # Create a project for testing
        if "owner" in self.test_users:
            self.create_test_project()
    
    def create_test_project(self):
        """Create a test project for RBAC testing"""
        if "owner" not in self.test_users:
            return
        
        project_data = {
            "name": "RBAC Test Project",
            "description": "Project for testing role-based access control",
            "organization_id": self.test_users["owner"]["org_id"]
        }
        
        headers = {
            "Authorization": f"Bearer {self.test_users['owner']['token']}",
            "Content-Type": "application/json"
        }
        
        try:
            response = requests.post(f"{API_BASE}/projects", json=project_data, headers=headers)
            if response.status_code == 200:
                data = response.json()
                self.test_project_id = data['data']['id']
                print(f"✅ Created test project: {self.test_project_id}")
            else:
                print(f"❌ Failed to create test project: {response.status_code}")
        except Exception as e:
            print(f"❌ Error creating test project: {e}")
    
    def test_endpoint_access(self, role: str, method: str, endpoint: str, 
                           expected_status: int, data: Optional[Dict] = None):
        """Test access to specific endpoint for a role"""
        if role not in self.test_users:
            print(f"⏭️ Skipping {role} test - user not available")
            return
        
        start_time = time.time()
        user = self.test_users[role]
        
        headers = {
            "Authorization": f"Bearer {user['token']}",
            "Content-Type": "application/json"
        }
        
        full_url = f"{API_BASE}{endpoint}"
        
        try:
            if method.upper() == "GET":
                response = requests.get(full_url, headers=headers)
            elif method.upper() == "POST":
                response = requests.post(full_url, headers=headers, json=data)
            elif method.upper() == "PUT":
                response = requests.put(full_url, headers=headers, json=data)
            elif method.upper() == "DELETE":
                response = requests.delete(full_url, headers=headers)
            else:
                raise ValueError(f"Unsupported method: {method}")
            
            execution_time = time.time() - start_time
            
            if response.status_code == expected_status:
                status = "PASS"
                error_message = None
            else:
                status = "FAIL"
                error_message = f"Response: {response.text[:200]}"
            
            result = RBACTestResult(
                test_name=f"{method} {endpoint}",
                role=role,
                endpoint=endpoint,
                expected_status=expected_status,
                actual_status=response.status_code,
                status=status,
                execution_time=execution_time,
                error_message=error_message
            )
            
            self.log_result(result)
            
        except Exception as e:
            execution_time = time.time() - start_time
            result = RBACTestResult(
                test_name=f"{method} {endpoint}",
                role=role,
                endpoint=endpoint,
                expected_status=expected_status,
                actual_status=0,
                status="FAIL",
                execution_time=execution_time,
                error_message=str(e)
            )
            self.log_result(result)
    
    def test_user_management_access(self):
        """Test user management endpoint access by role"""
        print("\n👤 Testing User Management Access")
        print("-" * 50)
        
        # Test profile access (all roles should have access)
        for role in self.test_users.keys():
            self.test_endpoint_access(role, "GET", "/users/profile", 200)
        
        # Test profile update (all roles should have access to their own profile)
        update_data = {"first_name": "Updated", "last_name": "Name"}
        for role in self.test_users.keys():
            self.test_endpoint_access(role, "PUT", "/users/profile", 200, update_data)
    
    def test_organization_management_access(self):
        """Test organization management access by role"""
        print("\n🏢 Testing Organization Management Access")
        print("-" * 50)
        
        if not self.test_org_id:
            print("⏭️ Skipping organization tests - no test org")
            return
        
        # Test get organizations (all should have access)
        for role in self.test_users.keys():
            self.test_endpoint_access(role, "GET", "/organizations", 200)
        
        # Test get specific organization
        for role in self.test_users.keys():
            self.test_endpoint_access(role, "GET", f"/organizations/{self.test_org_id}", 200)
        
        # Test organization update (only owner/admin should have access)
        update_data = {"name": "Updated Org Name", "description": "Updated description"}
        
        # Owner and admin should succeed
        for role in ["owner", "admin"]:
            if role in self.test_users:
                self.test_endpoint_access(role, "PUT", f"/organizations/{self.test_org_id}", 200, update_data)
        
        # Member and viewer should be forbidden
        for role in ["member", "viewer"]:
            if role in self.test_users:
                self.test_endpoint_access(role, "PUT", f"/organizations/{self.test_org_id}", 403, update_data)
        
        # Test member invitation (only owner/admin should have access)
        invite_data = {"email": f"invite_test_{int(time.time())}@testcompany.com", "role": "member"}
        
        for role in ["owner", "admin"]:
            if role in self.test_users:
                self.test_endpoint_access(role, "POST", f"/organizations/{self.test_org_id}/invite", 200, invite_data)
        
        for role in ["member", "viewer"]:
            if role in self.test_users:
                self.test_endpoint_access(role, "POST", f"/organizations/{self.test_org_id}/invite", 403, invite_data)
    
    def test_project_management_access(self):
        """Test project management access by role"""
        print("\n📁 Testing Project Management Access")
        print("-" * 50)
        
        # Test get projects (all should have access)
        for role in self.test_users.keys():
            self.test_endpoint_access(role, "GET", "/projects", 200)
        
        # Test project creation (owner/admin/member should have access, viewer should not)
        project_data = {
            "name": f"Test Project {int(time.time())}",
            "description": "Test project for RBAC",
            "organization_id": self.test_org_id
        }
        
        for role in ["owner", "admin", "member"]:
            if role in self.test_users:
                self.test_endpoint_access(role, "POST", "/projects", 200, project_data)
        
        if "viewer" in self.test_users:
            self.test_endpoint_access("viewer", "POST", "/projects", 403, project_data)
        
        # Test project update
        if self.test_project_id:
            update_data = {"name": "Updated Project Name", "description": "Updated description"}
            
            # Owner and admin should succeed
            for role in ["owner", "admin"]:
                if role in self.test_users:
                    self.test_endpoint_access(role, "PUT", f"/projects/{self.test_project_id}", 200, update_data)
            
            # Member might have limited access, viewer should be forbidden
            if "member" in self.test_users:
                self.test_endpoint_access("member", "PUT", f"/projects/{self.test_project_id}", 403, update_data)
            
            if "viewer" in self.test_users:
                self.test_endpoint_access("viewer", "PUT", f"/projects/{self.test_project_id}", 403, update_data)

    def test_board_management_access(self):
        """Test board/kanban management access by role"""
        print("\n📋 Testing Board Management Access")
        print("-" * 50)

        if not self.test_project_id:
            print("⏭️ Skipping board tests - no test project")
            return

        # Test board creation
        board_data = {
            "name": f"RBAC Test Board {int(time.time())}",
            "description": "Board for RBAC testing",
            "project_id": self.test_project_id
        }

        # Owner, admin, member should be able to create boards
        for role in ["owner", "admin", "member"]:
            if role in self.test_users:
                self.test_endpoint_access(role, "POST", "/boards", 200, board_data)

        # Viewer should not be able to create boards
        if "viewer" in self.test_users:
            self.test_endpoint_access("viewer", "POST", "/boards", 403, board_data)

        # Test get boards (all should have read access)
        for role in self.test_users.keys():
            self.test_endpoint_access(role, "GET", "/boards", 200)

    def test_advanced_features_access(self):
        """Test access to advanced features by role"""
        print("\n🚀 Testing Advanced Features Access")
        print("-" * 50)

        # Analytics endpoints (typically admin/owner only)
        analytics_endpoints = [
            "/analytics/dashboard",
            "/analytics/projects",
            "/analytics/users"
        ]

        for endpoint in analytics_endpoints:
            # Owner and admin should have access
            for role in ["owner", "admin"]:
                if role in self.test_users:
                    self.test_endpoint_access(role, "GET", endpoint, 200)

            # Member and viewer should be forbidden
            for role in ["member", "viewer"]:
                if role in self.test_users:
                    self.test_endpoint_access(role, "GET", endpoint, 403)

        # Security endpoints (admin/owner only)
        security_endpoints = [
            "/security/audit-logs",
            "/security/permissions"
        ]

        for endpoint in security_endpoints:
            for role in ["owner", "admin"]:
                if role in self.test_users:
                    self.test_endpoint_access(role, "GET", endpoint, 200)

            for role in ["member", "viewer"]:
                if role in self.test_users:
                    self.test_endpoint_access(role, "GET", endpoint, 403)

        # AI/Automation endpoints (might be restricted)
        ai_endpoints = [
            "/ai/models",
            "/ai/workflows"
        ]

        for endpoint in ai_endpoints:
            for role in ["owner", "admin"]:
                if role in self.test_users:
                    self.test_endpoint_access(role, "GET", endpoint, 200)

    def test_cross_organization_access(self):
        """Test that users cannot access other organizations' data"""
        print("\n🔒 Testing Cross-Organization Access Control")
        print("-" * 50)

        if len(self.test_users) < 2:
            print("⏭️ Skipping cross-org tests - need multiple users")
            return

        # Try to access another user's organization
        user_roles = list(self.test_users.keys())
        if len(user_roles) >= 2:
            user1 = self.test_users[user_roles[0]]
            user2 = self.test_users[user_roles[1]]

            # User1 tries to access User2's organization
            headers = {
                "Authorization": f"Bearer {user1['token']}",
                "Content-Type": "application/json"
            }

            try:
                response = requests.get(f"{API_BASE}/organizations/{user2['org_id']}", headers=headers)

                if response.status_code == 403 or response.status_code == 404:
                    print("✅ Cross-organization access properly blocked")
                else:
                    print(f"❌ Cross-organization access not blocked: {response.status_code}")
            except Exception as e:
                print(f"❌ Error testing cross-org access: {e}")

    def run_all_tests(self):
        """Run all RBAC tests"""
        print("🚀 Starting Comprehensive RBAC Testing Suite")
        print("=" * 60)

        start_time = time.time()

        try:
            # Setup test users
            self.setup_test_users()

            if not self.test_users:
                print("❌ No test users created. Cannot run RBAC tests.")
                return

            # Run all test suites
            self.test_user_management_access()
            self.test_organization_management_access()
            self.test_project_management_access()
            self.test_board_management_access()
            self.test_advanced_features_access()
            self.test_cross_organization_access()

        except Exception as e:
            print(f"\n💥 RBAC test suite crashed: {e}")
            import traceback
            traceback.print_exc()

        total_time = time.time() - start_time
        self.print_summary(total_time)

    def print_summary(self, total_time: float):
        """Print test summary"""
        print("\n" + "=" * 60)
        print("📊 RBAC TEST SUMMARY")
        print("=" * 60)

        total_tests = len(self.results)
        passed_tests = len([r for r in self.results if r.status == "PASS"])
        failed_tests = len([r for r in self.results if r.status == "FAIL"])

        print(f"Total Tests: {total_tests}")
        print(f"✅ Passed: {passed_tests}")
        print(f"❌ Failed: {failed_tests}")
        print(f"⏱️ Total Time: {total_time:.2f}s")
        print(f"📈 Success Rate: {(passed_tests/total_tests*100):.1f}%" if total_tests > 0 else "N/A")

        # Group results by role
        role_summary = {}
        for result in self.results:
            if result.role not in role_summary:
                role_summary[result.role] = {"passed": 0, "failed": 0}

            if result.status == "PASS":
                role_summary[result.role]["passed"] += 1
            else:
                role_summary[result.role]["failed"] += 1

        print(f"\n📊 Results by Role:")
        for role, stats in role_summary.items():
            total_role_tests = stats["passed"] + stats["failed"]
            success_rate = (stats["passed"] / total_role_tests * 100) if total_role_tests > 0 else 0
            print(f"   {role.title()}: {stats['passed']}/{total_role_tests} passed ({success_rate:.1f}%)")

        if failed_tests > 0:
            print(f"\n❌ FAILED TESTS:")
            for result in self.results:
                if result.status == "FAIL":
                    print(f"   {result.role} - {result.test_name} - Expected {result.expected_status}, Got {result.actual_status}")

        print(f"\n👥 Test Users Created:")
        for role, user in self.test_users.items():
            print(f"   {role.title()}: {user['email']}")

        # Save results to file
        self.save_results_to_file()

    def save_results_to_file(self):
        """Save test results to JSON file"""
        results_data = {
            "timestamp": time.time(),
            "test_users": {role: {"email": user["email"], "org_id": user["org_id"]}
                          for role, user in self.test_users.items()},
            "test_org_id": self.test_org_id,
            "test_project_id": self.test_project_id,
            "summary": {
                "total": len(self.results),
                "passed": len([r for r in self.results if r.status == "PASS"]),
                "failed": len([r for r in self.results if r.status == "FAIL"])
            },
            "results": [
                {
                    "test_name": r.test_name,
                    "role": r.role,
                    "endpoint": r.endpoint,
                    "expected_status": r.expected_status,
                    "actual_status": r.actual_status,
                    "status": r.status,
                    "execution_time": r.execution_time,
                    "error_message": r.error_message
                }
                for r in self.results
            ]
        }

        filename = f"rbac_test_results_{int(time.time())}.json"
        with open(filename, 'w') as f:
            json.dump(results_data, f, indent=2)

        print(f"\n💾 Detailed results saved to: {filename}")

if __name__ == "__main__":
    tester = RBACTester()
    tester.run_all_tests()
