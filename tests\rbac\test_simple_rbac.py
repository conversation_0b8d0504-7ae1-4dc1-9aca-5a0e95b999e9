#!/usr/bin/env python3
"""
Simple RBAC Testing Suite for Agno WorkSphere
Tests role-based access control without Unicode issues
"""
import requests
import json
import time

BASE_URL = "http://localhost:3001"
API_BASE = f"{BASE_URL}/api/v1"

class SimpleRBACTester:
    def __init__(self):
        self.results = []
        self.test_users = {}
        
    def log_result(self, test_name, status, details=None):
        """Log test result"""
        result = {
            "test": test_name,
            "status": status,
            "details": details,
            "timestamp": time.time()
        }
        self.results.append(result)
        print(f"[{status}] {test_name}")
        if details:
            print(f"    {details}")
    
    def create_test_user(self, role_suffix):
        """Create a test user"""
        email = f"rbac_{role_suffix}_{int(time.time())}@testcompany.com"
        
        user_data = {
            "email": email,
            "password": "RBACTest123!",
            "first_name": f"RBAC {role_suffix.title()}",
            "last_name": "User",
            "organization_name": f"RBAC Test Org {role_suffix.title()}"
        }
        
        try:
            response = requests.post(
                f"{API_BASE}/auth/register",
                json=user_data,
                headers={"Content-Type": "application/json"}
            )
            
            if response.status_code == 200:
                data = response.json()
                user_info = {
                    "email": email,
                    "token": data['data']['tokens']['access_token'],
                    "user_id": data['data']['user']['id'],
                    "org_id": data['data']['organization']['id'],
                    "role": data['data']['role']
                }
                return user_info
            else:
                return None
                
        except Exception as e:
            return None
    
    def test_user_access(self, user_key, endpoint, expected_status=200):
        """Test user access to specific endpoint"""
        if user_key not in self.test_users:
            return False, "User not available"
        
        user = self.test_users[user_key]
        headers = {
            "Authorization": f"Bearer {user['token']}",
            "Content-Type": "application/json"
        }
        
        try:
            response = requests.get(f"{API_BASE}{endpoint}", headers=headers)
            return response.status_code == expected_status, response.status_code
        except Exception as e:
            return False, str(e)
    
    def test_cross_organization_access(self):
        """Test that users cannot access other organizations"""
        print("\n--- Testing Cross-Organization Access Control ---")
        
        if len(self.test_users) < 2:
            self.log_result("Cross-Org Access Test", "SKIP", "Need multiple users")
            return
        
        users = list(self.test_users.values())
        user1 = users[0]
        user2 = users[1]
        
        # User1 tries to access User2's organization
        headers = {
            "Authorization": f"Bearer {user1['token']}",
            "Content-Type": "application/json"
        }
        
        try:
            response = requests.get(f"{API_BASE}/organizations/{user2['org_id']}", headers=headers)
            
            if response.status_code in [403, 404]:
                self.log_result("Cross-Organization Access Block", "PASS", "Access properly blocked")
            else:
                self.log_result("Cross-Organization Access Block", "FAIL", f"Access not blocked: {response.status_code}")
        except Exception as e:
            self.log_result("Cross-Organization Access Block", "FAIL", str(e))
    
    def test_authentication_requirements(self):
        """Test that endpoints require authentication"""
        print("\n--- Testing Authentication Requirements ---")
        
        protected_endpoints = [
            "/users/profile",
            "/organizations",
            "/projects"
        ]
        
        for endpoint in protected_endpoints:
            try:
                response = requests.get(f"{API_BASE}{endpoint}")
                
                if response.status_code == 401:
                    self.log_result(f"Auth Required: {endpoint}", "PASS", "Properly requires authentication")
                else:
                    self.log_result(f"Auth Required: {endpoint}", "FAIL", f"No auth required: {response.status_code}")
            except Exception as e:
                self.log_result(f"Auth Required: {endpoint}", "FAIL", str(e))
    
    def test_role_based_permissions(self):
        """Test role-based permissions"""
        print("\n--- Testing Role-Based Permissions ---")
        
        # Test that all users can access their own profile
        for user_key, user in self.test_users.items():
            success, status = self.test_user_access(user_key, "/users/profile", 200)
            if success:
                self.log_result(f"{user_key} Profile Access", "PASS", "Can access own profile")
            else:
                self.log_result(f"{user_key} Profile Access", "FAIL", f"Cannot access profile: {status}")
        
        # Test that all users can see organizations they belong to
        for user_key, user in self.test_users.items():
            success, status = self.test_user_access(user_key, "/organizations", 200)
            if success:
                self.log_result(f"{user_key} Organizations Access", "PASS", "Can access organizations")
            else:
                self.log_result(f"{user_key} Organizations Access", "FAIL", f"Cannot access organizations: {status}")
    
    def test_token_validation(self):
        """Test token validation"""
        print("\n--- Testing Token Validation ---")
        
        # Test with invalid token
        headers = {
            "Authorization": "Bearer invalid_token_here",
            "Content-Type": "application/json"
        }
        
        try:
            response = requests.get(f"{API_BASE}/users/profile", headers=headers)
            
            if response.status_code == 401:
                self.log_result("Invalid Token Rejection", "PASS", "Invalid token properly rejected")
            else:
                self.log_result("Invalid Token Rejection", "FAIL", f"Invalid token accepted: {response.status_code}")
        except Exception as e:
            self.log_result("Invalid Token Rejection", "FAIL", str(e))
        
        # Test with malformed token
        headers = {
            "Authorization": "Bearer",
            "Content-Type": "application/json"
        }
        
        try:
            response = requests.get(f"{API_BASE}/users/profile", headers=headers)
            
            if response.status_code == 401:
                self.log_result("Malformed Token Rejection", "PASS", "Malformed token properly rejected")
            else:
                self.log_result("Malformed Token Rejection", "FAIL", f"Malformed token accepted: {response.status_code}")
        except Exception as e:
            self.log_result("Malformed Token Rejection", "FAIL", str(e))
    
    def run_all_tests(self):
        """Run all RBAC tests"""
        print("AGNO WORKSPHERE - RBAC TESTING SUITE")
        print("=" * 50)
        
        start_time = time.time()
        
        # Create test users
        print("\n--- Setting Up Test Users ---")
        
        owner = self.create_test_user("owner")
        if owner:
            self.test_users["owner"] = owner
            self.log_result("Create Owner User", "PASS", owner["email"])
        else:
            self.log_result("Create Owner User", "FAIL", "Could not create owner")
        
        member = self.create_test_user("member")
        if member:
            self.test_users["member"] = member
            self.log_result("Create Member User", "PASS", member["email"])
        else:
            self.log_result("Create Member User", "FAIL", "Could not create member")
        
        try:
            # Run all test suites
            self.test_authentication_requirements()
            self.test_token_validation()
            self.test_role_based_permissions()
            self.test_cross_organization_access()
            
        except Exception as e:
            print(f"\nRBAC test suite crashed: {e}")
            import traceback
            traceback.print_exc()
        
        total_time = time.time() - start_time
        self.print_summary(total_time)
    
    def print_summary(self, total_time):
        """Print test summary"""
        print("\n" + "=" * 50)
        print("RBAC TEST SUMMARY")
        print("=" * 50)
        
        total_tests = len(self.results)
        passed_tests = len([r for r in self.results if r["status"] == "PASS"])
        failed_tests = len([r for r in self.results if r["status"] == "FAIL"])
        skipped_tests = len([r for r in self.results if r["status"] == "SKIP"])
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Skipped: {skipped_tests}")
        print(f"Total Time: {total_time:.2f}s")
        print(f"Success Rate: {(passed_tests/total_tests*100):.1f}%" if total_tests > 0 else "N/A")
        
        if failed_tests > 0:
            print(f"\nFAILED TESTS:")
            for result in self.results:
                if result["status"] == "FAIL":
                    print(f"  - {result['test']}: {result['details']}")
        
        print(f"\nTest Users Created:")
        for role, user in self.test_users.items():
            print(f"  {role.title()}: {user['email']} (Org: {user['org_id'][:8]}...)")
        
        # Save results
        filename = f"simple_rbac_test_results_{int(time.time())}.json"
        with open(filename, 'w') as f:
            json.dump({
                "timestamp": time.time(),
                "total_time": total_time,
                "test_users": {role: {"email": user["email"], "org_id": user["org_id"]} 
                              for role, user in self.test_users.items()},
                "summary": {
                    "total": total_tests,
                    "passed": passed_tests,
                    "failed": failed_tests,
                    "skipped": skipped_tests
                },
                "results": self.results
            }, f, indent=2)
        
        print(f"\nResults saved to: {filename}")

if __name__ == "__main__":
    tester = SimpleRBACTester()
    tester.run_all_tests()
