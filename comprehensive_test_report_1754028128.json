{"timestamp": 1754028128.9520986, "execution_time": 9.100590229034424, "summary": {"total_suites": 4, "passed_suites": 0, "failed_suites": 4, "skipped_suites": 0}, "test_suites": [{"name": "Backend API Coverage Testing", "status": "FAIL", "execution_time": 0.2860896587371826, "error_message": "Exit code: 1, Error: Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\PM\\test_comprehensive_api.py\", line 745, in <module>\n    tester.run_all_tests()\n  File \"C:\\Users\\<USER>\\PM\\test_comprehensive_api.py\", line 651, ", "details": {}}, {"name": "Role-Based Access Control Testing", "status": "FAIL", "execution_time": 0.2868661880493164, "error_message": "Exit code: 1, Error: Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\PM\\test_rbac_comprehensive.py\", line 529, in <module>\n    tester.run_all_tests()\n  File \"C:\\Users\\<USER>\\PM\\test_rbac_comprehensive.py\", line 418", "details": {}}, {"name": "Frontend Functionality Testing", "status": "FAIL", "execution_time": 0.38422107696533203, "error_message": "Exit code: 1, Error: Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\PM\\test_frontend_comprehensive.py\", line 579, in <module>\n    tester.run_all_tests()\n  File \"C:\\Users\\<USER>\\PM\\test_frontend_comprehensive.py\", ", "details": {}}, {"name": "End-to-End User Flow Testing", "status": "FAIL", "execution_time": 0.2710907459259033, "error_message": "Exit code: 1, Error: Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\PM\\test_complete_flow.py\", line 198, in <module>\n    test_complete_user_flow()\n  File \"C:\\Users\\<USER>\\PM\\test_complete_flow.py\", line 13, in tes", "details": {}}]}