#!/usr/bin/env python3
"""
Setup script for Agno WorkSphere - Frontend and Backend
"""
import os
import sys
import subprocess
import shutil
from pathlib import Path


def run_command(command, cwd=None, check=True):
    """Run a shell command"""
    print(f"Running: {command}")
    try:
        result = subprocess.run(
            command,
            shell=True,
            cwd=cwd,
            check=check,
            capture_output=True,
            text=True
        )
        if result.stdout:
            print(result.stdout)
        return result
    except subprocess.CalledProcessError as e:
        print(f"Error running command: {e}")
        if e.stderr:
            print(f"Error output: {e.stderr}")
        if check:
            sys.exit(1)
        return e


def check_prerequisites():
    """Check if required tools are installed"""
    print("🔍 Checking prerequisites...")
    
    required_tools = {
        'python': 'python --version',
        'node': 'node --version',
        'npm': 'npm --version',
        'git': 'git --version'
    }
    
    missing_tools = []
    
    for tool, command in required_tools.items():
        result = run_command(command, check=False)
        if result.returncode == 0:
            print(f"✓ {tool} is installed")
        else:
            print(f"❌ {tool} is not installed")
            missing_tools.append(tool)
    
    if missing_tools:
        print(f"\n❌ Missing required tools: {', '.join(missing_tools)}")
        print("Please install the missing tools and run this script again.")
        sys.exit(1)
    
    print("✅ All prerequisites are installed!")


def setup_backend():
    """Set up the Python backend"""
    print("\n🐍 Setting up Python backend...")
    
    backend_dir = Path("backend")
    
    # Create virtual environment
    print("Creating Python virtual environment...")
    run_command("python -m venv venv", cwd=backend_dir)
    
    # Determine activation script path
    if sys.platform == "win32":
        activate_script = backend_dir / "venv" / "Scripts" / "activate"
        pip_path = backend_dir / "venv" / "Scripts" / "pip"
    else:
        activate_script = backend_dir / "venv" / "bin" / "activate"
        pip_path = backend_dir / "venv" / "bin" / "pip"
    
    # Install Python dependencies
    print("Installing Python dependencies...")
    run_command(f"{pip_path} install --upgrade pip", cwd=backend_dir)
    run_command(f"{pip_path} install -r requirements.txt", cwd=backend_dir)
    
    # Copy environment file
    env_example = backend_dir / ".env.example"
    env_file = backend_dir / ".env"
    
    if not env_file.exists() and env_example.exists():
        print("Creating .env file from template...")
        shutil.copy(env_example, env_file)
        print("⚠️  Please edit backend/.env with your configuration!")
    
    # Test backend setup
    print("Testing backend setup...")
    if sys.platform == "win32":
        python_path = backend_dir / "venv" / "Scripts" / "python"
    else:
        python_path = backend_dir / "venv" / "bin" / "python"
    
    result = run_command(f"{python_path} test_setup.py", cwd=backend_dir, check=False)
    
    if result.returncode == 0:
        print("✅ Backend setup completed successfully!")
    else:
        print("⚠️  Backend setup completed with warnings. Check the output above.")


def setup_frontend():
    """Set up the React frontend"""
    print("\n⚛️  Setting up React frontend...")
    
    frontend_dir = Path("agnoworksphere")
    
    if not frontend_dir.exists():
        print("❌ Frontend directory 'agnoworksphere' not found!")
        return
    
    # Install Node.js dependencies
    print("Installing Node.js dependencies...")
    run_command("npm install", cwd=frontend_dir)
    
    print("✅ Frontend setup completed successfully!")


def create_database():
    """Create PostgreSQL database"""
    print("\n🗄️  Setting up database...")
    
    # Check if PostgreSQL is running
    result = run_command("pg_isready", check=False)
    
    if result.returncode != 0:
        print("⚠️  PostgreSQL is not running or not installed.")
        print("Please install and start PostgreSQL, then run database migrations manually:")
        print("  cd backend")
        print("  alembic upgrade head")
        return
    
    # Create database
    print("Creating database...")
    run_command("createdb agno_worksphere", check=False)
    
    # Run migrations
    backend_dir = Path("backend")
    if sys.platform == "win32":
        python_path = backend_dir / "venv" / "Scripts" / "python"
        alembic_path = backend_dir / "venv" / "Scripts" / "alembic"
    else:
        python_path = backend_dir / "venv" / "bin" / "python"
        alembic_path = backend_dir / "venv" / "bin" / "alembic"
    
    print("Running database migrations...")
    result = run_command(f"{alembic_path} upgrade head", cwd=backend_dir, check=False)
    
    if result.returncode == 0:
        print("✅ Database setup completed successfully!")
    else:
        print("⚠️  Database migration failed. You may need to run migrations manually.")


def print_next_steps():
    """Print next steps for the user"""
    print("\n🎉 Setup completed!")
    print("\n📋 Next steps:")
    print("\n1. Configure your environment:")
    print("   - Edit backend/.env with your database and email settings")
    print("   - Update frontend API URL if needed")
    
    print("\n2. Start the development servers:")
    print("   Backend:")
    print("     cd backend")
    if sys.platform == "win32":
        print("     venv\\Scripts\\activate")
    else:
        print("     source venv/bin/activate")
    print("     python run.py")
    
    print("\n   Frontend (in another terminal):")
    print("     cd agnoworksphere")
    print("     npm start")
    
    print("\n3. Access the application:")
    print("   - Frontend: http://localhost:3000")
    print("   - Backend API: http://localhost:3001")
    print("   - API Documentation: http://localhost:3001/docs")
    
    print("\n4. Alternative: Use Docker")
    print("     cd backend")
    print("     docker-compose up")
    
    print("\n📚 For more information, see:")
    print("   - Backend README: backend/README.md")
    print("   - Integration Guide: BACKEND_INTEGRATION_GUIDE.md")


def main():
    """Main setup function"""
    print("🚀 Agno WorkSphere Setup Script")
    print("=" * 50)
    
    # Check if we're in the right directory
    if not Path("BACKEND_INTEGRATION_GUIDE.md").exists():
        print("❌ Please run this script from the project root directory")
        sys.exit(1)
    
    try:
        check_prerequisites()
        setup_backend()
        setup_frontend()
        create_database()
        print_next_steps()
        
    except KeyboardInterrupt:
        print("\n\n⚠️  Setup interrupted by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Setup failed with error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
