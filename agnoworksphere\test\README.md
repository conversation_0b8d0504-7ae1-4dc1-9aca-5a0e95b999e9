# AgnoWorkSphere Testing Suite

Comprehensive testing documentation for the enhanced project management system.

## Overview

This testing suite provides comprehensive coverage for all components, utilities, and features in AgnoWorkSphere, including:

- **Enhanced Project Creation Wizard** - Multi-step project configuration
- **Real-time Collaboration** - WebSocket-based collaborative features
- **Advanced UI Components** - Rich text editors, sliders, toggles
- **Project Management Features** - Task management, workflow visualization
- **Backend Integration** - API endpoints and data persistence

## Test Structure

```
test/
├── setup.js                 # Global test configuration
├── README.md                # This documentation
└── __mocks__/               # Mock implementations

src/
├── components/
│   ├── project/
│   │   └── __tests__/       # Project component tests
│   ├── ui/
│   │   └── __tests__/       # UI component tests
│   └── collaboration/
│       └── __tests__/       # Collaboration feature tests
├── utils/
│   └── __tests__/           # Utility function tests
└── hooks/
    └── __tests__/           # Custom hook tests
```

## Test Categories

### 1. Component Tests

#### Project Components
- **ProjectConfigurationInterface** - Budget, timeline, resource allocation
- **ProjectOverviewEditor** - Project details, objectives, deliverables
- **TechStackDisplay** - Technology selection and visualization
- **WorkflowManagement** - Gantt charts, flow diagrams, timeline views
- **TaskChecklistSystem** - Advanced task management with dependencies
- **ProjectConfirmationSummary** - Final review and project launch
- **EnhancedProjectCreationWizard** - Complete wizard integration

#### UI Components
- **Slider** - Range input with custom formatting
- **Toggle** - Switch component with accessibility
- **RichTextEditor** - WYSIWYG editor with toolbar
- **Modal** - Accessible dialog component
- **DatePicker** - Date selection with validation

#### Collaboration Components
- **RealTimeIndicator** - Connection status and active users
- **ConflictResolutionModal** - Merge conflict handling
- **CollaborativeEditor** - Real-time text editing

### 2. Utility Tests

#### Services
- **websocketService** - WebSocket connection management
- **collaborationService** - Real-time collaboration logic
- **authService** - Authentication and authorization
- **projectService** - Project CRUD operations

#### Helpers
- **cn** - Class name utility
- **formatters** - Date, currency, duration formatting
- **validators** - Form validation functions

### 3. Hook Tests

#### Custom Hooks
- **useRealTimeCollaboration** - Real-time features integration
- **useProjectData** - Project state management
- **useWebSocket** - WebSocket connection hook
- **useLocalStorage** - Persistent storage hook

## Running Tests

### Basic Commands

```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests in watch mode
npm run test:watch

# Run tests for CI/CD
npm run test:ci

# Update snapshots
npm run test:update

# Run specific test pattern
npm run test:specific -- --testNamePattern="ProjectConfiguration"
```

### Advanced Test Runner

```bash
# Use custom test runner
node scripts/test-runner.js --coverage

# Run with specific options
node scripts/test-runner.js --bail --coverage

# Watch mode
node scripts/test-runner.js --watch

# Help
node scripts/test-runner.js --help
```

## Test Configuration

### Jest Configuration

The project uses Jest with the following key configurations:

- **Environment**: jsdom for DOM testing
- **Setup**: Custom setup file with mocks and utilities
- **Coverage**: 70% minimum, 80%+ for critical components
- **Reporters**: Default, JUnit XML, HTML reports
- **Transform**: Babel for JSX/ES6, CSS modules

### Coverage Thresholds

```javascript
{
  global: {
    branches: 70,
    functions: 70,
    lines: 70,
    statements: 70
  },
  "src/components/project/": {
    branches: 80,
    functions: 80,
    lines: 80,
    statements: 80
  },
  "src/utils/": {
    branches: 85,
    functions: 85,
    lines: 85,
    statements: 85
  }
}
```

## Writing Tests

### Component Testing Best Practices

```javascript
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import '@testing-library/jest-dom';
import MyComponent from '../MyComponent';

describe('MyComponent', () => {
  const defaultProps = {
    onAction: jest.fn(),
    data: mockData
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders correctly', () => {
    render(<MyComponent {...defaultProps} />);
    expect(screen.getByText('Expected Text')).toBeInTheDocument();
  });

  it('handles user interactions', async () => {
    const user = userEvent.setup();
    render(<MyComponent {...defaultProps} />);
    
    await user.click(screen.getByRole('button'));
    expect(defaultProps.onAction).toHaveBeenCalled();
  });
});
```

### Service Testing

```javascript
import websocketService from '../websocketService';

// Mock WebSocket
global.WebSocket = jest.fn().mockImplementation(() => ({
  send: jest.fn(),
  close: jest.fn(),
  readyState: WebSocket.OPEN
}));

describe('websocketService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('connects successfully', async () => {
    await websocketService.connect('token', 'userId');
    expect(websocketService.isConnected).toBe(true);
  });
});
```

### Hook Testing

```javascript
import { renderHook, act } from '@testing-library/react';
import useCustomHook from '../useCustomHook';

describe('useCustomHook', () => {
  it('returns expected values', () => {
    const { result } = renderHook(() => useCustomHook());
    
    expect(result.current.value).toBe(expectedValue);
  });

  it('updates state correctly', () => {
    const { result } = renderHook(() => useCustomHook());
    
    act(() => {
      result.current.updateValue('new value');
    });
    
    expect(result.current.value).toBe('new value');
  });
});
```

## Mocking Strategies

### Component Mocks

```javascript
// Mock complex child components
jest.mock('../ComplexComponent', () => {
  return function MockComplexComponent(props) {
    return <div data-testid="mock-complex">{props.children}</div>;
  };
});
```

### Service Mocks

```javascript
// Mock external services
jest.mock('../../utils/apiService', () => ({
  get: jest.fn(),
  post: jest.fn(),
  put: jest.fn(),
  delete: jest.fn()
}));
```

### Browser API Mocks

```javascript
// Mock browser APIs
Object.defineProperty(window, 'localStorage', {
  value: {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn()
  }
});
```

## Debugging Tests

### Common Issues

1. **Async Operations**: Use `waitFor` for async updates
2. **User Events**: Use `userEvent` instead of `fireEvent`
3. **Cleanup**: Ensure proper cleanup in `afterEach`
4. **Mocks**: Clear mocks between tests

### Debug Commands

```bash
# Run with debug output
npm run test:debug

# Run specific test file
npm test -- ProjectConfiguration.test.jsx

# Run with verbose output
npm test -- --verbose

# Debug in VS Code
# Add breakpoints and use "Debug Jest Tests" configuration
```

## Continuous Integration

### GitHub Actions

```yaml
- name: Run Tests
  run: |
    npm ci
    npm run test:ci
    
- name: Upload Coverage
  uses: codecov/codecov-action@v3
  with:
    file: ./coverage/lcov.info
```

### Coverage Reports

- **HTML Report**: `coverage/lcov-report/index.html`
- **LCOV**: `coverage/lcov.info`
- **JUnit XML**: `test-results/junit.xml`
- **JSON**: `coverage/coverage-final.json`

## Performance Testing

### Bundle Size Analysis

```bash
npm run analyze
```

### Memory Leak Detection

```bash
npm test -- --detectLeaks
```

## Accessibility Testing

All components are tested for accessibility compliance:

- **Screen Reader Support**: ARIA attributes
- **Keyboard Navigation**: Tab order and focus management
- **Color Contrast**: Sufficient contrast ratios
- **Semantic HTML**: Proper heading structure

## Contributing

When adding new features:

1. Write tests first (TDD approach)
2. Ensure 80%+ coverage for new components
3. Add integration tests for complex features
4. Update this documentation
5. Run full test suite before submitting PR

## Resources

- [Jest Documentation](https://jestjs.io/docs/getting-started)
- [React Testing Library](https://testing-library.com/docs/react-testing-library/intro/)
- [Testing Best Practices](https://kentcdodds.com/blog/common-mistakes-with-react-testing-library)
- [Accessibility Testing](https://web.dev/accessibility-testing/)
