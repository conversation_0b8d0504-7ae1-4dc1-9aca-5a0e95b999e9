// src/utils/authService.js
// Real authentication service that connects to the backend API

import realApiService from './realApiService';

const authService = {
  // Sign up a new user with real backend integration
  signUp: async (email, password, userData = {}) => {
    try {
      const registrationData = {
        email,
        password,
        firstName: userData.firstName || userData.first_name || '',
        lastName: userData.lastName || userData.last_name || '',
        organizationName: userData.organizationName || userData.organization_name || '',
        organizationSlug: userData.organizationSlug || userData.organization_slug || ''
      };

      const result = await realApiService.auth.register(registrationData);
      
      if (result.error) {
        return {
          data: null,
          error: result.error
        };
      }

      // Transform the response to match the expected format
      return {
        data: {
          user: {
            id: result.data.user.id,
            email: result.data.user.email,
            firstName: result.data.user.first_name,
            lastName: result.data.user.last_name,
            emailVerified: result.data.user.email_verified || false,
            role: result.data.user.organizations?.[0]?.role || 'owner'
          },
          organization: result.data.user.organizations?.[0] || null,
          tokens: result.data.tokens
        },
        error: null
      };
    } catch (error) {
      console.error('SignUp error:', error);
      return {
        data: null,
        error: error.message || 'Sign up failed'
      };
    }
  },

  // Register function (alias for signUp for compatibility)
  register: async (email, password, userData = {}) => {
    return await authService.signUp(email, password, userData);
  },

  // Sign in with email and password using real backend
  signIn: async (email, password) => {
    try {
      const result = await realApiService.auth.login(email, password);

      if (result.error) {
        return {
          data: null,
          error: result.error
        };
      }

      // Transform the response to match the expected format
      return {
        data: {
          user: {
            id: result.data.user.id,
            email: result.data.user.email,
            firstName: result.data.user.first_name,
            lastName: result.data.user.last_name,
            emailVerified: result.data.user.email_verified || true,
            role: result.data.role || 'member'
          },
          organization: result.data.organization || null,
          tokens: result.data.tokens
        },
        error: null
      };
    } catch (error) {
      console.error('SignIn error:', error);
      return {
        data: null,
        error: error.message || 'Login failed'
      };
    }
  },

  // Sign out
  signOut: async () => {
    return await realApiService.auth.logout();
  },

  // Logout (alias for signOut)
  logout: async () => {
    return await realApiService.auth.logout();
  },

  // Get current user profile using real backend
  getCurrentUser: async () => {
    try {
      const result = await realApiService.auth.getCurrentUser();

      if (result.error || !result.data) {
        return {
          data: null,
          error: result.error || 'Failed to get current user'
        };
      }

      // Handle the enhanced server response format
      const userData = result.data.user || result.data;
      const organizations = result.data.organizations || [];

      // Get the user's role - prioritize user.role field, then first organization
      const userRole = userData.role || (organizations.length > 0 ? organizations[0].role : 'member');

      // Transform the response to match the expected format
      return {
        data: {
          user: {
            id: userData.id,
            email: userData.email,
            firstName: userData.first_name || userData.firstName,
            lastName: userData.last_name || userData.lastName,
            emailVerified: userData.email_verified || true,
            role: userRole,
            avatar: userData.avatar_url || userData.avatar
          },
          organizations: organizations.map(org => {
            // Handle nested organization structure from backend
            const orgData = org.organization || org;
            return {
              id: orgData.id,
              name: orgData.name,
              domain: orgData.domain,
              logo: orgData.logo_url || orgData.logo,
              role: org.role,
              member_count: orgData.member_count,
              joined_at: org.joined_at
            };
          })
        },
        error: null
      };
    } catch (error) {
      console.error('GetCurrentUser error:', error);
      return {
        data: null,
        error: error.message || 'Failed to get current user'
      };
    }
  },

  // Refresh access token
  refreshToken: async () => {
    try {
      return {
        data: {
          accessToken: realApiService.auth.getAccessToken(),
          refreshToken: 'mock-refresh-token'
        },
        error: null
      };
    } catch (error) {
      return {
        data: null,
        error: error.message || 'Token refresh failed'
      };
    }
  },

  // Check if user is authenticated
  isAuthenticated: () => {
    return realApiService.auth.isAuthenticated();
  },

  // Get stored access token
  getAccessToken: () => {
    return realApiService.auth.getAccessToken();
  },

  // Get user role
  getUserRole: () => {
    return realApiService.auth.getUserRole();
  },

  // Get organization ID
  getOrganizationId: () => {
    return realApiService.auth.getOrganizationId();
  },

  // Get current organization using real backend
  getCurrentOrganization: async () => {
    try {
      const organizationId = realApiService.auth.getOrganizationId();
      
      if (!organizationId) {
        return {
          data: { organization: null },
          error: null
        };
      }

      const organization = await realApiService.organizations.getById(organizationId);

      return {
        data: { organization },
        error: null
      };
    } catch (error) {
      console.error('GetCurrentOrganization error:', error);
      return {
        data: { organization: null },
        error: null // Don't expose errors for getCurrentOrganization
      };
    }
  },

  // Get dashboard stats using real backend
  getDashboardStats: async () => {
    try {
      const result = await realApiService.dashboard.getStats();
      
      return {
        data: result.data,
        userRole: realApiService.auth.getUserRole(),
        error: result.error
      };
    } catch (error) {
      console.error('GetDashboardStats error:', error);
      return {
        data: null,
        error: error.message || 'Failed to get dashboard stats'
      };
    }
  },

  // Listen to auth state changes
  onAuthStateChange: (callback) => {
    // Simple implementation - check token periodically
    const checkAuth = async () => {
      const result = await authService.getCurrentUser();
      callback(result.data.user, result.error);
    };

    // Check immediately
    checkAuth();

    // Set up periodic check (every 5 minutes)
    const interval = setInterval(checkAuth, 5 * 60 * 1000);

    return {
      data: {
        subscription: {
          unsubscribe: () => clearInterval(interval)
        }
      }
    };
  }
};

export default authService;
